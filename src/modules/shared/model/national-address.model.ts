import { Column, Model, DataType, Table } from 'sequelize-typescript';

export const nationalAddressModelTableName = 'national_addresses';
@Table({
  tableName: nationalAddressModelTableName,
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class NationalAddressModel extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  country: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  city: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  building_no: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  street: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  secondary_no: string;

  @Column({
    type: DataType.STRING,
  })
  district: string;

  @Column({
    type: DataType.STRING,
  })
  postal_code: string;

  @Column({
    type: DataType.STRING,
  })
  governorate: string;

  @Column({
    type: DataType.STRING,
  })
  short_address: string;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  updated_at: Date;
}
