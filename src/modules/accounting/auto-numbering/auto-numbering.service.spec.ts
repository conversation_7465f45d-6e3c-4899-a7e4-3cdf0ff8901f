import { Test, TestingModule } from '@nestjs/testing';
import { AutoNumberingService } from './auto-numbering.service';
import { getModelToken } from '@nestjs/mongoose';
import { AutoNumbering } from './schema/auto-numbering.schema';
import { GeneralJournal } from '../general-journal/schema/general-journal.schema';
import { Voucher } from '../voucher/schema/voucher.schema';

describe('AutoNumberingService', () => {
  let service: AutoNumberingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AutoNumberingService,
        {
          provide: getModelToken(AutoNumbering.name),
          useValue: {},
        },
        {
          provide: getModelToken(GeneralJournal.name),
          useValue: {},
        },
        {
          provide: getModelToken(Voucher.name),
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<AutoNumberingService>(AutoNumberingService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
