import { Modu<PERSON> } from '@nestjs/common';
import { AutoNumberingService } from './auto-numbering.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AutoNumbering,
  autoNumberingSchema,
} from './schema/auto-numbering.schema';
import { AutoNumberingController } from './auto-numbering.controller';
import {
  GeneralJournal,
  generalJournalSchema,
} from '../general-journal/schema/general-journal.schema';
import { Voucher, voucherSchema } from '../voucher/schema/voucher.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AutoNumbering.name, schema: autoNumberingSchema },
      { name: GeneralJournal.name, schema: generalJournalSchema },
      { name: Voucher.name, schema: voucherSchema },
    ]),
  ],
  controllers: [AutoNumberingController],
  providers: [AutoNumberingService],
  exports: [AutoNumberingService],
})
export class AutoNumberingModule {}
