import { Controller, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AutoNumberingService } from './auto-numbering.service';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { publicRpc } from '../../auth/guards/public-event.decorator';
import { UpdateAutoNumberingRpcDto } from './dto/update-auto-numbering-rpc.dto';
import { PolicyInterceptor } from '../../policy/policy.interceptor';
import { serviceName } from '../../policy/service-name.decorator';
import { Types } from 'mongoose';
import { accountingMessagePattern } from '../../../utils/queues.enum';
import { RpcDto } from '../../../utils/dto/rpc.dto';

@Controller('auto-numbering')
@ApiTags('auto-numbering')
export class AutoNumberingController {
  constructor(private readonly autoNumberingService: AutoNumberingService) {}

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.get_auto_numbering })
  async getAutoNumbering(
    @Payload()
    payload: RpcDto,
  ) {
    return await this.autoNumberingService.getAutoNumbering(payload.branch);
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.update_auto_numbering })
  @UseInterceptors(PolicyInterceptor)
  @serviceName([
    'journal',
    'receipt_voucher',
    'payment_voucher_cash',
    'debit_memo',
    'credit_memo',
  ])
  async updateAutoNumbering(
    @Payload()
    payload: {
      data: UpdateAutoNumberingRpcDto;
      policyData: any;
    },
  ) {
    const { branch, data, policyData } = payload as any;
    return await this.autoNumberingService.updateAutoNumbering(
      new Types.ObjectId(branch),
      data,
      policyData,
    );
  }
}
