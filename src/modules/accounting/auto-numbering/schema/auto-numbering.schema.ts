import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
export type AutoNumberingType = AutoNumbering & Document;
@Schema({ timestamps: true })
export class AutoNumbering {
  @Prop({ type: Number, default: 0 })
  number?: number;
  @Prop({ type: Types.ObjectId, required: false })
  branch?: Types.ObjectId | string;
  @Prop({ type: String, required: true })
  sequence_identifier: string;
  @Prop({
    type: Number,
    default: () => {
      return Math.floor(Math.random() * (999999 - 10000) + 10000);
    },
    unique: false,
  })
  version?: number;
}

export const autoNumberingSchema = SchemaFactory.createForClass(AutoNumbering);
