import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AutoNumbering } from './schema/auto-numbering.schema';
import { Model, Types } from 'mongoose';
import {
  GeneralJournal,
  GeneralJournalDocument,
} from '../general-journal/schema/general-journal.schema';
import { Voucher, VoucherDocument } from '../voucher/schema/voucher.schema';
import { voucherType } from '../voucher/dto/voucher-type.enum';
import { SoftDeleteModel } from '../../../utils/schemas';
import { generalExceptionCode } from '../../../exceptions/exception-code.general';
import { nameOf } from '../../../utils/object-key-name';
import { CustomHttpException } from '../../../utils/custom-http.exception';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';

@Injectable()
export class AutoNumberingService {
  constructor(
    @InjectModel(AutoNumbering.name)
    private autoNumberingModel: Model<AutoNumbering>,
    @InjectModel(GeneralJournal.name)
    private generalJournalModel: Model<GeneralJournal>,
    @InjectModel(Voucher.name)
    private voucherModel: Model<Voucher>,
  ) {}

  /**
   * Retrieves the next sequence number for a given sequence name and branch.
   * This method requires a callback function to check if a given number is manually used.
   *
   * @param findFunction A callback function that checks if the given number is already used.
   *                     It should be bound to its original context (if needed) using an arrow function.
   *                     The function should return a Promise resolving to a truthy if document with sequence exit otherwise falsy.
   * @param sequenceName The name of the sequence for which the number is being generated.
   * @param branch (Optional) The branch identifier, if applicable.
   * @returns Promise<number> The next available sequence number.
   *
   * Usage example:
   * getNextSequence((num) => someService.findOne(num), 'sequenceName/documentName', 'branchId')
   */

  async getNextSequence<T extends GeneralJournalDocument | VoucherDocument>(
    model: Model<T> | SoftDeleteModel<T>,
    sequenceName: string[],
    branch?: Types.ObjectId,
    type?: string,
    sameSerial?: boolean,
  ) {
    let attempts = 0;
    let updatedSequence: AutoNumbering;
    let isNumberUsed: boolean;
    const maxAttempts = 5;
    const query: any = {
      sequence_identifier: { $in: sequenceName },
    };

    // if data is outdated update it
    if (typeof branch !== 'undefined' && Types.ObjectId.isValid(branch)) {
      query.branch = new Types.ObjectId(branch);
    }

    do {
      await this.autoNumberingModel.updateMany(
        { ...query },
        { $inc: { number: 1 } },
      );

      updatedSequence = await this.autoNumberingModel.findOne(query).exec();

      if (updatedSequence === null || updatedSequence === undefined) {
        for (const sequence of sequenceName) {
          updatedSequence = await this.autoNumberingModel.create({
            sequence_identifier: sequence,
            number: 1,
            branch: new Types.ObjectId(branch),
          });
        }
      }

      const validationResults = await Promise.all(
        sequenceName.map((name) =>
          this.validateNumber(
            model,
            name,
            updatedSequence?.number,
            branch,
            !sameSerial ? type : undefined,
            false,
          ),
        ),
      );

      isNumberUsed = validationResults.some((result) => result === true);

      if (++attempts > maxAttempts) {
        throw new HttpException(
          nameOf(generalExceptionCode, (x) => x.maxAttemptsReached),
          generalExceptionCode.maxAttemptsReached,
        );
        // general error for number is duplicate
      }
    } while (isNumberUsed);

    if (updatedSequence) {
      return updatedSequence.number;
    }

    throw new HttpException(
      nameOf(generalExceptionCode, (x) => x.maxAttemptsReached),
      generalExceptionCode.maxAttemptsReached,
    );
  }

  async updateSequence(
    number: number,
    sequenceName: string,
    branch?: Types.ObjectId,
  ) {
    const query: AutoNumbering = {
      sequence_identifier: sequenceName,
    };
    if (typeof branch !== 'undefined' && Types.ObjectId.isValid(branch)) {
      query.branch = branch;
    }
    const updatedSequence: AutoNumbering = await this.autoNumberingModel
      .findOneAndUpdate(
        { ...query }, // Check the version
        { number },
        { new: true, upsert: true },
      )
      .exec();
    if (updatedSequence) {
      return updatedSequence.number;
    }
    throw new HttpException(
      nameOf(generalExceptionCode, (x) => x.maxAttemptsReached),
      generalExceptionCode.maxAttemptsReached,
    );
  }

  async getAutoNumbering(branch: Types.ObjectId) {
    return await this.autoNumberingModel
      .find({
        branch: branch,
      })
      .lean();
  }

  async updateAutoNumbering(
    branch: Types.ObjectId,
    data: any,
    policyData: any,
  ) {
    const groupedResult: any = this.groupByFeature(data);

    for (const featureGroup of groupedResult) {
      const sequence = featureGroup.sequence;
      const featureName = featureGroup.feature;

      // Extract featureType and number for each sequence
      const sequences = sequence.map((seq) => {
        const [, featureType] = seq.sequence_identifier.split('.');
        const [invoiceType] = featureType.split('_');
        return {
          sequence_identifier: seq.sequence_identifier,
          feature_type: featureType,
          invoice_type: invoiceType,
          number: seq.number,
        };
      });

      const featurePolicy = policyData[featureName];

      if (featurePolicy?.same_serial) {
        const areAllNumbersSame = sequences.every(
          (seq, index, arr) => seq.number === arr[0].number,
        );

        if (!areAllNumbersSame) {
          throw new CustomHttpException(
            nameOf(erpExceptionCode, (exception) => exception.numberMustBeSame),
            erpExceptionCode.numberMustBeSame,
            {
              feature: featureGroup,
            },
          );
        }

        const sequenceIdentifiers = sequences.map(
          (sequence) => sequence.sequence_identifier,
        );
        const numberNotChanged = await this.checkIfNumberChanged(
          sequenceIdentifiers,
          sequences[0].number,
          branch,
        );

        if (numberNotChanged === null) {
          const autoNumbering = await this.autoNumberingModel
            .find({
              sequence_identifier: { $in: sequenceIdentifiers },
              branch: branch,
            })
            .exec();

          for (const autoNumberingElement of autoNumbering) {
            if (autoNumberingElement?.number > sequences[0].number) {
              throw new CustomHttpException(
                nameOf(
                  erpExceptionCode,
                  (exception) => exception.cannotUseThisNumber,
                ),
                erpExceptionCode.cannotUseThisNumber,
                {
                  feature: featureGroup,
                },
              );
            }
          }
        }

        switch (featureName) {
          case 'journal':
            if (numberNotChanged === null) {
              await this.validateNumber(
                this.generalJournalModel,
                featureName,
                sequence.number,
                branch,
              );
            }
            for (const sequence of sequences) {
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
            }
            break;

          case 'receipt_voucher':
            if (numberNotChanged === null) {
              await this.validateNumber(
                this.voucherModel,
                featureName,
                sequence.number,
                branch,
                voucherType.receipt,
              );
            }
            for (const sequence of sequences) {
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
            }
            break;

          case 'payment_voucher_cash':
            if (numberNotChanged === null) {
              await this.validateNumber(
                this.voucherModel,
                featureName,
                sequence.number,
                branch,
                voucherType.payment,
              );
            }
            for (const sequence of sequences) {
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
            }
            break;

          case 'debit_memo':
            if (numberNotChanged === null) {
              await this.validateNumber(
                this.voucherModel,
                featureName,
                sequence.number,
                branch,
                voucherType.debit_memo,
              );
            }
            for (const sequence of sequences) {
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
            }
            break;

          case 'credit_memo':
            if (numberNotChanged === null) {
              await this.validateNumber(
                this.voucherModel,
                featureName,
                sequence.number,
                branch,
                voucherType.credit_memo,
              );
            }
            for (const sequence of sequences) {
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
            }
            break;
        }
      }

      if (!featurePolicy?.same_serial) {
        for (const sequence of sequences) {
          const numberNotChanged = await this.checkIfNumberChanged(
            sequence.sequence_identifier,
            sequence.number,
            branch,
          );

          if (numberNotChanged === null) {
            for (const sequenceCheck of sequences) {
              const autoNumbering = await this.autoNumberingModel
                .findOne({
                  sequence_identifier: sequenceCheck.sequence_identifier,
                  branch: branch,
                })
                .exec();

              if (autoNumbering?.number == sequence.number) {
                throw new CustomHttpException(
                  nameOf(
                    erpExceptionCode,
                    (exception) => exception.numberDuplicated,
                  ),
                  erpExceptionCode.numberDuplicated,
                  {
                    feature: featureGroup,
                  },
                );
              }
            }

            const autoNumbering = await this.autoNumberingModel
              .findOne({
                sequence_identifier: sequence.sequence_identifier,
                branch: branch,
              })
              .exec();

            if (autoNumbering?.number > sequence.number) {
              throw new CustomHttpException(
                nameOf(
                  erpExceptionCode,
                  (exception) => exception.cannotUseThisNumber,
                ),
                erpExceptionCode.cannotUseThisNumber,
                {
                  feature: featureGroup,
                },
              );
            }
          }

          switch (featureName) {
            case 'journal':
              if (numberNotChanged === null) {
                await this.validateNumber(
                  this.generalJournalModel,
                  featureName,
                  sequence.number,
                  branch,
                );
              }
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
              break;

            case 'receipt_voucher':
              if (numberNotChanged === null) {
                await this.validateNumber(
                  this.voucherModel,
                  featureName,
                  sequence.number,
                  branch,
                  voucherType.receipt,
                );
              }
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
              break;

            case 'payment_voucher_cash':
              if (numberNotChanged === null) {
                await this.validateNumber(
                  this.voucherModel,
                  featureName,
                  sequence.number,
                  branch,
                  voucherType.payment,
                );
              }
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
              break;

            case 'debit_memo':
              if (numberNotChanged === null) {
                await this.validateNumber(
                  this.voucherModel,
                  featureName,
                  sequence.number,
                  branch,
                  voucherType.debit_memo,
                );
              }
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
              break;

            case 'credit_memo':
              if (numberNotChanged === null) {
                await this.validateNumber(
                  this.voucherModel,
                  featureName,
                  sequence.number,
                  branch,
                  voucherType.credit_memo,
                );
              }
              await this.updateSequence(
                sequence.number,
                sequence.sequence_identifier,
                branch,
              );
              break;
          }
        }
      }
    }
    return true;
  }

  public async validateNumber<T extends GeneralJournal | Voucher>(
    model: Model<T>,
    feature: string,
    number: number,
    branch: Types.ObjectId,
    type?: string,
    onUpdate: boolean = true,
  ): Promise<any> {
    const query: any = {
      number: number,
      branch: branch,
    };

    if (type) {
      query.type = type;
    }

    if (branch) {
      query.branch_id = branch;
    }

    const numberExists = await model.findOne(query).exec();

    if (!onUpdate) {
      return numberExists;
    }

    if (numberExists) {
      throw new CustomHttpException(
        nameOf(erpExceptionCode, (exception) => exception.numberDuplicated),
        erpExceptionCode.numberDuplicated,
        {
          feature: feature,
        },
      );
    }
    return false;
  }

  public async checkIfNumberChanged(
    sequence_identifier: string[],
    number: number,
    branch: Types.ObjectId,
  ) {
    return await this.autoNumberingModel
      .findOne({
        number: number,
        sequence_identifier: { $in: sequence_identifier },
        branch: branch,
      })
      .exec();
  }

  private groupByFeature(sequenceIdentifiers) {
    const groupedResults = sequenceIdentifiers.reduce((acc, item) => {
      const feature = item.sequence_identifier.split('.')[0]; // Extract the feature name
      if (!acc[feature]) {
        acc[feature] = { feature: feature, sequence: [] };
      }
      acc[feature].sequence.push({
        sequence_identifier: item.sequence_identifier,
        number: item.number,
      });
      return acc;
    }, {});

    return Object.values(groupedResults);
  }
}
