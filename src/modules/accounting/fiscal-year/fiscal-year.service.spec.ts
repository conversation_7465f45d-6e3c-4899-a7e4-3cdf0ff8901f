import { Test, TestingModule } from '@nestjs/testing';
import { FiscalYearService } from './fiscal-year.service';
import { getModelToken } from '@nestjs/mongoose';
import { FiscalYear } from './schema/fiscal-year.schema';

describe('FiscalYearService', () => {
  let service: FiscalYearService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FiscalYearService,
        { provide: getModelToken(FiscalYear.name), useValue: {} },
      ],
    }).compile();

    service = await module.resolve<FiscalYearService>(FiscalYearService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
