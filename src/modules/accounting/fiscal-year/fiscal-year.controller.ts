import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { FiscalYearService } from './fiscal-year.service';
import { CreateFiscalYearDto } from './dto/create-fiscal-year.dto';
import { UpdateFiscalYearDto } from './dto/update-fiscal-year.dto';
import { GetFiscalYearDto } from './dto/get-fiscal-year.dto';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';

@ApiTags('fiscal-year')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('fiscal-year')
export class FiscalYearController {
  constructor(private readonly fiscalYearService: FiscalYearService) {}

  @UseGuards(CaslGuard)
  @abilities({ action: 'create', subject: 'fiscal_year' })
  @Post()
  create(@Body() createFiscalYearDto: CreateFiscalYearDto) {
    return this.fiscalYearService.create(createFiscalYearDto);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'fiscal_year' })
  @Get()
  findAll(@Query() queries: GetFiscalYearDto) {
    return this.fiscalYearService.findAll(queries);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'fiscal_year' })
  @Get(':id')
  findOne(@Param('_id') _id: string) {
    return this.fiscalYearService.findOne(_id);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'edit', subject: 'fiscal_year' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateFiscalYearDto: UpdateFiscalYearDto,
  ) {
    return this.fiscalYearService.update(+id, updateFiscalYearDto);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'delete', subject: 'fiscal_year' })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.fiscalYearService.remove(+id);
  }
}
