import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';

@Schema({ timestamps: true })
export class FiscalYear {
  _id: Types.ObjectId;
  @Prop({ type: String, required: true })
  name: string;
  @Prop({ type: Date, required: true })
  start_date: Date;
  @Prop({ type: Date, required: true })
  end_date: Date;
}
export const fiscalYearSchema = SchemaFactory.createForClass(FiscalYear);
