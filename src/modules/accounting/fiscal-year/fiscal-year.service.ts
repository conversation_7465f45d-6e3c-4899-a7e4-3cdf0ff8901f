import { Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateFiscalYearDto } from './dto/create-fiscal-year.dto';
import { GetFiscalYearDto } from './dto/get-fiscal-year.dto';
import { UpdateFiscalYearDto } from './dto/update-fiscal-year.dto';
import { FiscalYear } from './schema/fiscal-year.schema';
import { ResultWithType } from './interface/resultwithtype.interface';
import { paginate } from '../../../utils/dto';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class FiscalYearService {
  constructor(
    @InjectModel(FiscalYear.name)
    private readonly fiscalYear: Model<FiscalYear>,
  ) {}
  create(createFiscalYearDto: CreateFiscalYearDto): Promise<FiscalYear> {
    return new this.fiscalYear(this.create(createFiscalYearDto)).save();
  }

  async findAll(queries: GetFiscalYearDto): Promise<ResultWithType> {
    const { page, limit, ...rest } = queries;
    const result = await this.fiscalYear
      .find(rest)
      .skip((page - 1) * limit)
      .limit(limit);
    const meta = await paginate(limit, page, this.fiscalYear, rest);
    return { result, meta };
  }

  async findOne(_id: string) {
    return await this.fiscalYear.findOne({ _id });
  }

  update(id: number, updateFiscalYearDto: UpdateFiscalYearDto) {
    return `This action updates a #${id} ${updateFiscalYearDto}fiscalYear`;
  }

  remove(id: number) {
    return `This action removes a #${id} fiscalYear`;
  }
}
