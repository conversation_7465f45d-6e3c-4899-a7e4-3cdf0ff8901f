import { Module } from '@nestjs/common';
import { FiscalYearService } from './fiscal-year.service';
import { FiscalYearController } from './fiscal-year.controller';
import { FiscalYear } from './schema/fiscal-year.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { SequelizeModule } from '@nestjs/sequelize';
import { FiscalYearModel } from './model/fiscal-year.model';

@Module({
  imports: [
    SequelizeModule.forFeature([FiscalYearModel]),
    MongooseModule.forFeature([{ name: FiscalYear.name, schema: FiscalYear }]),
  ],
  controllers: [FiscalYearController],
  providers: [FiscalYearService],
})
export class FiscalYearModule {}
