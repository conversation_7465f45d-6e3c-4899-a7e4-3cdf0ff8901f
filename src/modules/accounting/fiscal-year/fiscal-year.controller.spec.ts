import { Test, TestingModule } from '@nestjs/testing';
import { FiscalYearController } from './fiscal-year.controller';
import { FiscalYearService } from './fiscal-year.service';
import { getModelToken } from '@nestjs/mongoose';
import { FiscalYear } from './schema/fiscal-year.schema';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { CaslAbilityFactory } from '../../casl/casl-ability.factory';

describe('FiscalYearController', () => {
  let controller: FiscalYearController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FiscalYearController],
      providers: [
        FiscalYearService,
        { provide: getModelToken(FiscalYear.name), useValue: {} },
        { provide: UserRpcService, useValue: {} },
        { provide: CaslAbilityFactory, useValue: {} },
      ],
    }).compile();

    controller = module.get<FiscalYearController>(FiscalYearController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
