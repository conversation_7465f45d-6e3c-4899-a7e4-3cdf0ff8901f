import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsNotEmpty, IsString } from 'class-validator';

export class CreateFiscalYearDto {
  @ApiProperty({
    example: 'test name',
    description: 'name of fiscal Year',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  @ApiProperty({
    example: '2022-11-11',
    description: 'start-Date of fiscal Year',
    required: true,
  })
  start_date: string;

  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  @ApiProperty({
    example: '2022-11-11',
    description: 'end-Date of fiscal Year',
    required: true,
  })
  end_date: string;
}
