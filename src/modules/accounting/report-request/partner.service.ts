import { Injectable } from '@nestjs/common';
import { reportRequestSummaries } from './helper';
import { statusEnum } from './enum/status.enum';
import { Model, Types } from 'mongoose';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { reportRequestNameEnum } from './enum/report-request-name.enum';
import { ReportRequest } from './schema/report-request.schema';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { ReportsService } from '../reports/reports.service';
import { InjectModel } from '@nestjs/mongoose';
import { PartnerType } from '../reports/enums/partner-type.enum';

@Injectable()
export class PartnerService {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly notificationRpcService: NotificationRpcService,
    @InjectModel(ReportRequest.name)
    private readonly reportRequestModel: Model<ReportRequest>,
  ) {}

  public async generateReportItems(
    id: Types.ObjectId,
    request: RequestWithUser,
  ) {
    const reportRequest = await this.reportRequestModel.findOne({ _id: id });

    if (
      reportRequest.name !== reportRequestNameEnum.customer_report &&
      reportRequest.name !== reportRequestNameEnum.vendor_report
    ) {
      return;
    }

    if (reportRequest.status !== statusEnum.generating) {
      return;
    }

    let partnerIds = [];
    let partnerType, partnerId;
    switch (reportRequest.name) {
      case reportRequestNameEnum.customer_report:
        partnerType = PartnerType.customer;
        partnerId = !reportRequest.filters.is_all_customers
          ? reportRequest.filters.customer
          : null;
        break;

      case reportRequestNameEnum.vendor_report:
        partnerType = PartnerType.vendor;
        partnerId = !reportRequest.filters.is_all_vendors
          ? reportRequest.filters.vendor
          : null;
        break;
    }

    const partnerResult = await this.reportsService.findPartnerAccounts(
      request,
      partnerType,
      partnerId,
    );

    partnerIds = partnerResult.map(
      (partner: any) => new Types.ObjectId(partner._id),
    );

    const query: any = {
      partners: partnerIds,
      from_date: reportRequest.filters.from_date,
      to_date: reportRequest.filters.to_date,
      branch: reportRequest.filters.branch,
    };

    if (reportRequest.filters.journal_type !== undefined) {
      query.type = reportRequest.filters.journal_type;
    }

    const journals = await this.reportsService.findPartnerLedger(
      query,
      undefined,
    );

    const journalData = journals.result.map((journal) => ({
      ...journal,
      transactions: journal?.transactions.filter((transaction) =>
        partnerIds.some(
          (partnerId) => String(transaction.partner_id) === String(partnerId),
        ),
      ),
    }));

    const itemArray = [];
    journalData.forEach((journal) => {
      journal?.transactions.forEach((transaction) => {
        itemArray.push({
          document_number: journal?.document_code,
          document_name: journal.type,
          description: transaction.description,
          document: journal?.document,
          date: journal?.transaction_date,
          debit: transaction?.debit,
          credit: transaction?.credit,
          balance: reportRequest.filters.including_balance
            ? transaction.current_balance
            : null,
        });
      });
    });

    if (itemArray.length > 0) {
      reportRequest.items = itemArray;

      reportRequest.summaries = await reportRequestSummaries(
        this.reportsService,
        request,
        journalData,
        reportRequest,
        partnerResult,
      );
    }

    reportRequest.status = statusEnum.generated;
    await reportRequest.save();

    return;
  }
}
