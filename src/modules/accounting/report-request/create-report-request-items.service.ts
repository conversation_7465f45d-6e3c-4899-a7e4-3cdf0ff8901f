import { Injectable } from '@nestjs/common';
import { ReportRequest } from './schema/report-request.schema';
import { Model, Types } from 'mongoose';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { ReportsService } from '../reports/reports.service';
import { InjectModel } from '@nestjs/mongoose';
import { statusEnum } from './enum/status.enum';
import { reportRequestNameEnum } from './enum/report-request-name.enum';
import { UserRpcService } from '../../rpc/user-rpc.service';
import * as _ from 'lodash';
import { Calculator } from 'src/utils/calculator';

@Injectable()
export class CreateReportRequestItemsService {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly notificationRpcService: NotificationRpcService,
    @InjectModel(ReportRequest.name)
    private readonly reportRequestModel: Model<ReportRequest>,
    private readonly userRpcService: UserRpcService,
  ) {}

  public async generateJournalReport(
    id: Types.ObjectId,
    request: RequestWithUser,
  ) {
    const reportRequest = await this.reportRequestModel.findOne({ _id: id });

    if (reportRequest.name !== reportRequestNameEnum.journal_report) {
      return;
    }
    if (reportRequest.status !== statusEnum.generating) {
      return;
    }
    const { branch } = reportRequest.filters;

    // eslint-disable-next-line @typescript-eslint/naming-convention,@typescript-eslint/no-unused-vars
    const { from_date, to_date, from_number, to_number } =
      reportRequest.filters;
    const journalData = await this.reportsService.findGeneralJournal(
      {
        from_date: from_date,
        to_date: to_date,
        from_number,
        to_number,
        branch,
      },
      undefined,
      undefined,
    );
    let branchData;
    if (branch) {
      branchData = await this.userRpcService.getOneBranch(
        request.user.code,
        branch,
      );
    }
    reportRequest.summaries = {
      journal_meta: {
        from_date: new Date(from_date),
        to_date: new Date(to_date),
        branch: branchData?.general_information?.name || {},
      },
    };
    const itemArray = [];
    journalData.result.forEach((journal) => {
      journal.transactions.forEach((tr) => {
        itemArray.push({
          account_code: tr.accountingNode['code'],
          account_name: tr.accountingNode['name'],
          debit: tr.debit,
          credit: tr.credit,
          date: journal.createdAt,
          description: tr.description,
          document: journal._id,
          document_number: journal.number,
        });
      });
    });
    reportRequest.items = itemArray;
    reportRequest.status = statusEnum.generated;

    await reportRequest.save();

    return journalData;
  }

  public async generateVoucherReport(
    id: Types.ObjectId,
    request: RequestWithUser,
  ) {
    const reportRequest = await this.reportRequestModel.findOne({ _id: id });

    if (reportRequest.name !== reportRequestNameEnum.voucher_report) {
      return;
    }
    if (reportRequest.status !== statusEnum.generating) {
      return;
    }
    const { branch } = reportRequest.filters;

    // eslint-disable-next-line @typescript-eslint/naming-convention,@typescript-eslint/no-unused-vars
    const pickedFilter = _.pick({ branch, ...reportRequest.filters }, [
      'voucher_type',

      'voucher_party_type',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'from_number',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'to_number',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'branch',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'credit_account',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'from_date',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'to_date',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'debit_account',
    ]);

    const voucherData =
      await this.reportsService.voucherFindAllRaw(pickedFilter);

    const ids = voucherData.map((voucher) => {
      return new Types.ObjectId(String(voucher.journal._id));
    });

    const rs = await this.reportsService.findJournals(
      {
        from_date: pickedFilter.from_date,
        to_date: pickedFilter.to_date,
        branch: new Types.ObjectId(branch),
      },
      undefined,
      ids,
    );
    const calAmount = new Calculator(0);

    const itemArray = [];
    voucherData.forEach((voucher) => {
      calAmount.plus(voucher.amount);
      rs.result.forEach((element) => {
        if (String(voucher.journal._id) == String(element._id)) {
          element.transactions.forEach((transaction) => {
            itemArray.push({
              code: voucher.number,
              account_code: transaction.accountingNode['code'],
              type: transaction.accountingNode['type'],
              invoice_party_type:
                transaction.accountingNode['invoice_party_type'],
              name: transaction.accountingNode['name'],
              amount: voucher.amount,
              date: voucher.createdAt,
              note: element.note,
              document: voucher._id,
              document_type: element.type,
            });
          });
        }
      });
    });
    let branchData;
    if (branch) {
      branchData = await this.userRpcService.getOneBranch(
        request.user.code,
        branch,
      );
    }
    reportRequest.summaries = {
      voucher_meta: {
        from_date: new Date(pickedFilter.from_date),
        to_date: new Date(pickedFilter.to_date),
        branch: branchData?.general_information?.name || {},
        total_amount: calAmount.round().number(),
      },
    };

    reportRequest.items = itemArray;
    reportRequest.status = statusEnum.generated;

    await reportRequest.save();

    return;
  }
}
