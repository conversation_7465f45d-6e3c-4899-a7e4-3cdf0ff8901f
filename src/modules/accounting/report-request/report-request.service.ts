import { Injectable } from '@nestjs/common';

import { GetReportRequestDto } from './dto/get-report-request.dto';

import { ReportRequest } from './schema/report-request.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { statusEnum } from './enum/status.enum';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { CreateReportRequestItemsService } from './create-report-request-items.service';
import { IncomeStatementService } from './income-statement.service';
import { BalanceSheetService } from './balance-sheet.service';
import { PartnerService } from './partner.service';
import { AccountActivityService } from './account-activity.service';
import { TrailBalanceService } from './trail-balance.service';
import {
  paginate,
  paginateNestedArray,
  PaginationDto,
} from 'src/utils/dto/pagination.dto';

@Injectable()
export class ReportRequestService {
  constructor(
    private readonly userRpcService: UserRpcService,
    private readonly notificationRpcService: NotificationRpcService,
    private readonly createReportRequestItemsService: CreateReportRequestItemsService,
    private readonly incomeStatementService: IncomeStatementService,
    private readonly balanceSheetService: BalanceSheetService,
    private readonly partnerService: PartnerService,
    private readonly accountActivityService: AccountActivityService,
    private readonly trailBalanceService: TrailBalanceService,
    @InjectModel(ReportRequest.name)
    private readonly reportRequestModel: Model<ReportRequest>,
  ) {}

  public async find(dto: GetReportRequestDto, request: RequestWithUser) {
    const { limit, page, status } = dto;
    const generationDate = dto.generation_date;

    let query: any = {
      direct_generation: false,
      'filters.branch': new Types.ObjectId(request?.headers['branch']),
    };
    if (generationDate) {
      const date = new Date(generationDate);
      const startOfDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
      );
      const endOfDate = new Date(
        startOfDate.getTime() + 24 * 60 * 60 * 1000 - 1,
      );

      query = {
        generation_date: {
          $gte: startOfDate,
          $lt: endOfDate,
        },
      };
    }

    if (status) {
      query['status'] = status;
    }

    const result = await this.reportRequestModel
      .find(query)
      .select('id name generation_date status by_user')
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    for (const report of result) {
      const date = new Date(report.generation_date);

      (report as any).time = date.toTimeString().substring(0, 8);
      (report as any).date = date.toISOString().split('T')[0];

      report.by_user = await this.userRpcService.getUser(
        request.user.code,
        report.by_user,
      );
    }

    const meta = await paginate(limit, page, this.reportRequestModel, query);
    return { meta, result };
  }

  public async findOne(
    id: Types.ObjectId,
    dto: PaginationDto,
    request: RequestWithUser,
  ) {
    const documentId = id;
    const { page, limit } = dto;

    await this.createReportRequestItemsService.generateJournalReport(
      documentId,
      request,
    );

    await this.createReportRequestItemsService.generateVoucherReport(
      documentId,
      request,
    );

    await this.partnerService.generateReportItems(documentId, request);

    await this.accountActivityService.generateReportItems(documentId, request);

    await this.trailBalanceService.generateReportItems(documentId, request);

    await this.incomeStatementService.generateReportItems(documentId, request);

    await this.balanceSheetService.generateReportItems(documentId, request);

    const result = await this.reportRequestModel.aggregate([
      { $match: { _id: documentId } },
      {
        $project: {
          items: { $slice: [`$items`, (page - 1) * limit, limit] },
          object: 1,
          generation_date: 1,
          name: 1,
          filters: 1,
          summaries: 1,
          status: 1,
        },
      },
    ]);

    const meta = await paginateNestedArray(
      id,
      dto.page,
      dto.limit,
      this.reportRequestModel,
      'items',
    );

    return { meta, result: result[0] };
  }

  public async save(id: string) {
    return await this.reportRequestModel.findOneAndUpdate(
      { _id: id },
      { status: statusEnum.saved },
      { new: true },
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async download(id: string) {}

  public async delete(id: string) {
    return await this.reportRequestModel.findOneAndDelete({ _id: id });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async deleteTemporaryRequests(code: number) {
    const reports = await this.reportRequestModel.find({
      status: statusEnum.generated,
    });

    if (reports.length > 0) {
      const idsToDelete = reports.map((report) => report._id);
      await this.reportRequestModel.deleteMany({ _id: { $in: idsToDelete } });
      // for (const report of reports) {
      // if (report.pdf_name) {
      //     report.pdf_name,
      //   await this.notificationRpcService.deleteGeneratedFile(
      //     code,
      //   );
      // }
      // }
    }
  }
}
