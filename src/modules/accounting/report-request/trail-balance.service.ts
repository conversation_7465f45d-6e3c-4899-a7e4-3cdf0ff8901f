import { Injectable } from '@nestjs/common';
import { statusEnum } from './enum/status.enum';
import { Model, Types } from 'mongoose';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { reportRequestNameEnum } from './enum/report-request-name.enum';
import { ReportRequest } from './schema/report-request.schema';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { ReportsService } from '../reports/reports.service';
import { InjectModel } from '@nestjs/mongoose';
import { TrailBalanceDto } from '../reports/dto/trail-balance.dto';
import { trailBalanceTypesEnum } from '../reports/enums/trail-balance-types.enum';
import { Calculator } from 'src/utils/calculator';

@Injectable()
export class TrailBalanceService {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly notificationRpcService: NotificationRpcService,
    @InjectModel(ReportRequest.name)
    private readonly reportRequestModel: Model<ReportRequest>,
  ) {}

  public async generateReportItems(
    id: Types.ObjectId,
    request: RequestWithUser,
  ) {
    const reportRequest = await this.reportRequestModel.findOne({ _id: id });

    if (reportRequest.name !== reportRequestNameEnum.trail_balance_report) {
      return;
    }

    if (reportRequest.status !== statusEnum.generating) {
      return;
    }

    const branch = reportRequest.filters.branch;

    const dto = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      showBeginningBalance: reportRequest.filters.show_beginning_balance,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      showEndingBalance: reportRequest.filters.show_ending_balance,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      showTransactions: reportRequest.filters.show_transactions,
      type: reportRequest.filters.trial_balance_type,
      date: reportRequest.filters.from_date,
    } as TrailBalanceDto;

    const leafAccounts = await this.reportsService.findAccountingNodeAllRaw(
      null,
      null,
      null,
      null,
      null,
      branch,
      reportRequest.filters.is_all_branches,
    );

    const result = await this.reportsService.getTrailBalance(
      leafAccounts,
      dto,
      reportRequest.filters.is_all_branches ? null : branch,
    );

    const data = result.flat();

    let itemsData;

    if (
      reportRequest.filters.trial_balance_type ===
      trailBalanceTypesEnum.detailed
    ) {
      const itemArray = [];

      data.forEach((account) => {
        itemArray.push({
          account_code: account.code,
          account_name: account.name,
          document: account._id,
          debit: reportRequest.filters.show_transactions
            ? account?.debit
            : null,
          credit: reportRequest.filters.show_transactions
            ? account?.credit
            : null,
          beginning_balance: reportRequest.filters.show_beginning_balance
            ? account.beginning_balance
            : null,
          ending_balance: reportRequest.filters.show_ending_balance
            ? account.ending_balance
            : null,
        });
      });

      itemsData = itemArray;
    }

    if (
      reportRequest.filters.trial_balance_type === trailBalanceTypesEnum.all
    ) {
      const accountingTree = await this.reportsService.accountingTree(
        {
          show_hidden_account: true,
          branch: branch,
        },
        reportRequest.filters.is_all_branches,
        request,
      );

      const filteredTree = this.trailBalanceMapFinancialDetailsToAccountingTree(
        accountingTree.docs,
        data,
        dto,
      );

      itemsData = this.flattenNodes(filteredTree, [], reportRequest);
    }

    const branchData: any = {};
    if (reportRequest.filters.is_all_branches) {
      branchData.branch = {
        general_information: {
          name: {
            en: 'All Branches',
            ar: 'كل الفروع',
          },
        },
      };
    } else {
      branchData.branch = await this.reportsService.findBranch(
        request,
        reportRequest.filters.branch,
      );
    }

    if (itemsData.length > 0) {
      reportRequest.items = itemsData;

      reportRequest.summaries = {
        trail_balance_data: branchData,
        calculations: this.trailBalanceCalculation(itemsData),
      };

      reportRequest.status = statusEnum.generated;
      await reportRequest.save();
    }

    reportRequest.status = statusEnum.generated;
    await reportRequest.save();

    return;
  }

  private trailBalanceCalculation(data: any[]) {
    const totalCredits = new Calculator(0);
    const totalDebits = new Calculator(0);
    const totalBeginningBalances = new Calculator(0);
    const totalEndingBalances = new Calculator(0);

    data.forEach((record) => {
      if (record.credit) {
        totalCredits.plus(record.credit);
      }
      if (record.debit) {
        totalDebits.plus(record.debit);
      }
      if (record.ending_balance_raw) {
        totalEndingBalances.plus(record.ending_balance_raw);
      }
      if (record.beginning_balance) {
        totalBeginningBalances.plus(record.beginning_balance);
      }
    });
    return {
      total_credits: totalCredits.round().number(),
      total_debits: totalDebits.round().number(),
      total_beginning_balances: totalBeginningBalances.round().number(),
      total_ending_balances: totalEndingBalances.round().number(),
    };
  }

  private trailBalanceMapFinancialDetailsToAccountingTree(
    accountingTree: any[],
    financialNodes: any[],
    dto: TrailBalanceDto,
  ) {
    const financialMap = new Map(
      financialNodes.map((node) => [String(node._id), node]),
    );

    const assignFinancialDetails = (node: any) => {
      const newNode = {
        _id: node._id,
        code: node.code,
        name: node.name,

        credit: dto.showTransactions ? node.credit || 0 : undefined,
        debit: dto.showTransactions ? node.debit || 0 : undefined,
        beginning_balance: dto.showBeginningBalance
          ? (node.beginning_balance ?? 0)
          : undefined,
        ending_balance: dto.showEndingBalance
          ? (node.ending_balance ?? 0)
          : undefined,
        nodes: [],
      };

      if (financialMap.has(String(node._id))) {
        const financialDetails = financialMap.get(String(node._id));
        newNode.credit = financialDetails.credit;
        newNode.debit = financialDetails.debit;
        newNode.beginning_balance = financialDetails.beginning_balance;
        newNode.ending_balance = financialDetails.ending_balance;
      }

      if (node.nodes) {
        node.nodes.forEach((child: any) => {
          newNode.nodes.push(assignFinancialDetails(child));
        });
      }

      return newNode;
    };

    return accountingTree.map((node) => assignFinancialDetails(node));
  }

  private flattenNodes(nodes, flatArray = [], reportRequest) {
    nodes.forEach((node) => {
      flatArray.push({
        account_code: node.code,
        account_name: node.name,
        document: node._id,
        debit: reportRequest.filters.show_transactions ? node?.debit : null,
        credit: reportRequest.filters.show_transactions ? node?.credit : null,
        beginning_balance: reportRequest.filters.show_beginning_balance
          ? node.beginning_balance
          : null,
        ending_balance: reportRequest.filters.show_ending_balance
          ? node.ending_balanc
          : null,
      });

      // Recursively process child nodes if they exist
      if (node.nodes && node.nodes.length > 0) {
        this.flattenNodes(node.nodes, flatArray, reportRequest);
      }
    });

    return flatArray;
  }
}
