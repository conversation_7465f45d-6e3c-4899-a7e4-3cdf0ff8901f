import { Injectable } from '@nestjs/common';
import { ReportRequest } from './schema/report-request.schema';
import { Model, Types } from 'mongoose';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { ReportsService } from '../reports/reports.service';
import { InjectModel } from '@nestjs/mongoose';
import { statusEnum } from './enum/status.enum';
import { reportRequestNameEnum } from './enum/report-request-name.enum';
import { UserRpcService } from '../../rpc/user-rpc.service';
import {
  directCostsEnum,
  incomeTaxExpensesEnum,
  operatingExpensesEnum,
  otherIncomeAndExpensesEnum,
  revenueEnum,
} from '../accounting-node/enums/node-type.enum';
import {
  ctaActions,
  ctaSubjects,
  eventType,
  severity,
} from '../../rpc/enum/notification.enum';
import {
  applyNegativeMultiplierToCategory,
  assignToAccountingNodeTypesToCategory,
  getNodeTypes,
  getTransactionStartingDate,
  sumParentNodeTypeCategory,
} from './helper';
import { Calculator } from 'src/utils/calculator';

@Injectable()
export class IncomeStatementService {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly notificationRpcService: NotificationRpcService,
    @InjectModel(ReportRequest.name)
    private readonly reportRequestModel: Model<ReportRequest>,
    private readonly userRpcService: UserRpcService,
  ) {}

  public async generateReportItems(
    id: Types.ObjectId,
    request: RequestWithUser,
  ) {
    const reportRequest = await this.reportRequestModel.findOne({ _id: id });

    if (reportRequest.name !== reportRequestNameEnum.income_statement_report) {
      return;
    }

    if (reportRequest.status !== statusEnum.generating) {
      return;
    }

    const nodeTypes = getNodeTypes();

    const accountingNodeResult =
      await this.reportsService.findAccountingNodeAllRaw(
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        !reportRequest.filters.is_all_branches
          ? reportRequest.filters.branch
          : undefined,
        undefined,
        nodeTypes,
      );

    const nodeIds = accountingNodeResult.map(
      (node) => new Types.ObjectId(node.id),
    );

    let startOfDate;
    if (reportRequest.filters?.from_date) {
      const fromDate = new Date(reportRequest.filters.from_date);
      startOfDate = new Date(
        fromDate.getFullYear(),
        fromDate.getMonth(),
        fromDate.getDate(),
      );
    } else {
      startOfDate = getTransactionStartingDate(
        request.policyData.year_beginning_date,
      );
    }

    const endOfDate = new Date(
      new Date(reportRequest.filters.to_date).getTime() +
        24 * 60 * 60 * 1000 -
        1,
    );

    const query: any = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      nodeIds: nodeIds,
      from_equal_transaction_date: startOfDate,
      to_equal_transaction_date: endOfDate,
    };

    if (!reportRequest.filters.is_all_branches) {
      query.branch = reportRequest.filters.branch;
    }

    if (reportRequest.filters.journal_type !== undefined) {
      query.type = reportRequest.filters.journal_type;
    }

    const journals = await this.reportsService.findJournals(
      query,
      null,
      undefined,
    );

    const nodeIdsAndNodeTypes = accountingNodeResult.map((node) => ({
      id: new Types.ObjectId(node._id),
      node_type: node.node_type,
    }));

    const result = this.calculateSummations(
      nodeIdsAndNodeTypes,
      journals.result,
    );

    reportRequest.object = this.categorizeNodes(result);

    const branch = await this.userRpcService.getOneBranch(
      request?.user?.code,
      reportRequest.filters.branch,
    );

    const company = await this.userRpcService.getCompany(request?.user?.code);

    reportRequest.summaries = {
      income_statement_data: {
        branch: reportRequest.filters.is_all_branches ? null : branch,
        company: reportRequest.filters.is_all_branches ? company.name : null,
        from_date: reportRequest.filters.from_date,
        to_date: reportRequest.filters.to_date,
      },
    };

    reportRequest.status = statusEnum.generated;
    await reportRequest.save();

    await this.notificationRpcService.createNotification(request?.user?.code, {
      user: new Types.ObjectId(request?.user?.user_id),
      title: 'report_finish_notification',
      message: 'report_request_finished_successfully',
      severity: severity.low,
      event_type: eventType.reports,
      cta: {
        action: ctaActions.view,
        subject: ctaSubjects.income_statement_report,
        feature_id: reportRequest._id,
        query: '',
      },
    });
    return;
  }

  private calculateSummations(accountingNodesIds, journals) {
    const results = [];

    for (const node of accountingNodesIds) {
      let totalSum = 0;

      for (const journal of journals) {
        for (const transaction of journal.transactions) {
          if (String(transaction.accountingNode._id) === String(node.id)) {
            totalSum += transaction.debit - transaction.credit;
          }
        }
      }

      results.push({
        id: node.id,
        node_type: node.node_type,
        total_sum: totalSum,
      });
    }

    return results;
  }

  private categorizeNodes(data: any[]) {
    const categoryEnums = {
      revenue: revenueEnum,
      direct_costs: directCostsEnum,
      operating_expenses: operatingExpensesEnum,
      other_income_and_expenses: otherIncomeAndExpensesEnum,
      income_tax_expenses: incomeTaxExpensesEnum,
    };

    const categorizedData: any = {
      gross_profit: 0,
      operating_profit: 0,
      profit_before_tax: 0,
      income_tax_expenses: 0,
      net_profit: 0,
    };

    // Loop through the nodes and categorize them dynamically
    for (const node of data) {
      assignToAccountingNodeTypesToCategory(
        node,
        categorizedData,
        categoryEnums,
      );
    }

    // Calculate totals for each parent category
    if (categorizedData.revenue) {
      applyNegativeMultiplierToCategory(categorizedData.revenue.types);
      categorizedData.revenue.total = sumParentNodeTypeCategory(
        categorizedData.revenue.types,
      );
    }

    if (categorizedData.direct_costs) {
      applyNegativeMultiplierToCategory(categorizedData.direct_costs.types);
      categorizedData.direct_costs.total = sumParentNodeTypeCategory(
        categorizedData.direct_costs.types,
      );
    }

    if (categorizedData.operating_expenses) {
      applyNegativeMultiplierToCategory(
        categorizedData.operating_expenses.types,
      );
      categorizedData.operating_expenses.total = sumParentNodeTypeCategory(
        categorizedData.operating_expenses.types,
      );
    }

    if (categorizedData.other_income_and_expenses) {
      applyNegativeMultiplierToCategory(
        categorizedData.other_income_and_expenses.types,
      );
      categorizedData.other_income_and_expenses.total =
        sumParentNodeTypeCategory(
          categorizedData.other_income_and_expenses.types,
        );
    }

    categorizedData.gross_profit = Calculator.sum(
      categorizedData.revenue?.total || 0,
      categorizedData.direct_costs?.total || 0,
    )
      .round()
      .number();

    categorizedData.operating_profit = Calculator.sum(
      categorizedData.gross_profit,
      categorizedData.operating_expenses?.total || 0,
    )
      .round()
      .number();

    categorizedData.profit_before_tax = Calculator.sum(
      categorizedData.operating_profit,
      categorizedData.other_income_and_expenses?.total || 0,
    )
      .round()
      .number();

    if (categorizedData.income_tax_expenses) {
      categorizedData.income_tax_expenses =
        new Calculator(
          sumParentNodeTypeCategory(categorizedData.income_tax_expenses.types),
        )
          .round()
          .number() || 0;
    }

    categorizedData.net_profit = Calculator.sum(
      categorizedData.profit_before_tax,
      categorizedData.income_tax_expenses,
    )
      .round()
      .number();

    return categorizedData;
  }
}
