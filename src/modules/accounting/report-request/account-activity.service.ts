import { Injectable } from '@nestjs/common';
import { reportRequestSummaries } from './helper';
import { statusEnum } from './enum/status.enum';
import { Model, Types } from 'mongoose';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { reportRequestNameEnum } from './enum/report-request-name.enum';
import { ReportRequest } from './schema/report-request.schema';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { ReportsService } from '../reports/reports.service';
import { InjectModel } from '@nestjs/mongoose';
import { accountingNodeType } from '../accounting-node/interfaces/accounting-node-type.enum';

@Injectable()
export class AccountActivityService {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly notificationRpcService: NotificationRpcService,
    @InjectModel(ReportRequest.name)
    private readonly reportRequestModel: Model<ReportRequest>,
  ) {}

  public async generateReportItems(
    id: Types.ObjectId,
    request: RequestWithUser,
  ) {
    const reportRequest = await this.reportRequestModel.findOne({ _id: id });

    if (
      reportRequest.name !== reportRequestNameEnum.cashier_activity_report &&
      reportRequest.name !== reportRequestNameEnum.account_activity_report
    ) {
      return;
    }

    if (reportRequest.status !== statusEnum.generating) {
      return;
    }

    const accountingNodeResult = await this.reportsService.findAccountingNode(
      {
        _id: reportRequest.filters.general_account,
      },
      reportRequest.filters.branch,
    );

    const query: any = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      nodeIds: [reportRequest.filters.general_account],
      from_date: reportRequest.filters.from_date,
      to_date: reportRequest.filters.to_date,
      branch: reportRequest.filters.branch,
    };

    if (reportRequest.filters.journal_type !== undefined) {
      query.type = reportRequest.filters.journal_type;
    }

    const journals = await this.reportsService.findJournals(
      query,
      null,
      undefined,
    );

    console.log('journals', journals, 'journals');

    const journalData = journals.result.map((journal) => ({
      ...journal,
      transactions: journal?.transactions.filter(
        (transaction) =>
          String((transaction.accountingNode as any)._id) ===
          String(reportRequest.filters.general_account),
      ),
    }));

    const itemArray = [];
    journalData.forEach((journal) => {
      journal?.transactions.forEach((transaction) => {
        if (accountingNodeResult.balance_type === accountingNodeType.credit) {
          transaction.current_balance *= -1;
        }
        itemArray.push({
          document_number: journal?.document_code,
          document_name: journal.type,
          description: transaction.description,
          document: journal?.document,
          date: journal?.transaction_date,
          debit: transaction?.debit,
          credit: transaction?.credit,
          balance: reportRequest.filters.including_balance
            ? transaction.current_balance
            : null,
        });
      });
    });

    if (itemArray.length > 0) {
      reportRequest.items = itemArray;

      reportRequest.summaries = await reportRequestSummaries(
        this.reportsService,
        request,
        journalData,
        reportRequest,
        undefined,
        accountingNodeResult,
      );
    }

    reportRequest.status = statusEnum.generated;
    await reportRequest.save();

    return;
  }
}
