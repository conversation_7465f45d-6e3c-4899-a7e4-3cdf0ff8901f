import { Injectable } from '@nestjs/common';
import {
  applyNegativeMultiplierToCategory,
  assignToAccountingNodeTypesToCategory,
  getTransactionStartingDate,
  sumParentNodeTypeCategory,
} from './helper';
import {
  ctaActions,
  ctaSubjects,
  eventType,
  severity,
} from '../../rpc/enum/notification.enum';
import { statusEnum } from './enum/status.enum';
import { Model, Types } from 'mongoose';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { reportRequestNameEnum } from './enum/report-request-name.enum';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { ReportRequest } from './schema/report-request.schema';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { ReportsService } from '../reports/reports.service';
import { InjectModel } from '@nestjs/mongoose';
import {
  currentAssetsEnum,
  currentLiabilitiesEnum,
  equityEnum,
  nonCurrentAssetsEnum,
  nonCurrentLiabilitiesEnum,
} from '../accounting-node/enums/node-type.enum';
import { GeneralLedger } from '../general-ledger/schema/general-ledger.schema';

@Injectable()
export class BalanceSheetService {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly notificationRpcService: NotificationRpcService,
    @InjectModel(ReportRequest.name)
    private readonly reportRequestModel: Model<ReportRequest>,
    private readonly userRpcService: UserRpcService,
    @InjectModel(GeneralLedger.name)
    private generalLedger: Model<GeneralLedger>,
  ) {}

  public async generateReportItems(
    id: Types.ObjectId,
    request: RequestWithUser,
  ) {
    const reportRequest = await this.reportRequestModel.findOne({ _id: id });

    if (reportRequest.name !== reportRequestNameEnum.balance_sheet_report) {
      return;
    }

    if (reportRequest.status !== statusEnum.generating) {
      return;
    }

    const nodeTypes = this.getNodeTypes();

    const accountingNodeResult =
      await this.reportsService.findAccountingNodeAllRaw(
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        reportRequest.filters.branch,
        undefined,
        nodeTypes,
      );

    const nodeIds = accountingNodeResult.map(
      (node) => new Types.ObjectId(node.id),
    );

    const startOfDate = getTransactionStartingDate(
      request.policyData.year_beginning_date,
    );

    const endOfDate = new Date(
      new Date(reportRequest.filters.to_date).getTime() +
        24 * 60 * 60 * 1000 -
        1,
    );

    const query: any = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      nodeIds: nodeIds,
      from_equal_transaction_date: startOfDate,
      to_equal_transaction_date: endOfDate,
    };

    if (!reportRequest.filters.is_all_branches) {
      query.branch = reportRequest.filters.branch;
    }

    if (reportRequest.filters.journal_type !== undefined) {
      query.type = reportRequest.filters.journal_type;
    }

    // await this.getJournals(endOfDate, nodeIds);

    const journals = await this.reportsService.findJournals(
      query,
      null,
      undefined,
    );

    const nodeIdsAndNodeTypes = accountingNodeResult.map((node) => ({
      id: new Types.ObjectId(node._id),
      node_type: node.node_type,
      balance_type: node.balance_type,
    }));

    const result = this.calculateSummations(
      nodeIdsAndNodeTypes,
      journals.result,
    );

    reportRequest.object = this.categorizeNodes(result);

    const branch = await this.userRpcService.getOneBranch(
      request?.user?.code,
      reportRequest.filters.branch,
    );

    const company = await this.userRpcService.getCompany(request?.user?.code);

    reportRequest.summaries = {
      income_statement_data: {
        branch: reportRequest.filters.is_all_branches ? null : branch,
        company: reportRequest.filters.is_all_branches ? company.name : null,
        from_date: reportRequest.filters.from_date,
        to_date: reportRequest.filters.to_date,
      },
    };

    reportRequest.status = statusEnum.generated;
    await reportRequest.save();

    await this.notificationRpcService.createNotification(request?.user?.code, {
      user: new Types.ObjectId(request?.user?.user_id),
      title: 'report_finish_notification',
      message: 'report_request_finished_successfully',
      severity: severity.low,
      event_type: eventType.reports,
      cta: {
        action: ctaActions.view,
        subject: ctaSubjects.balance_sheet_report,
        feature_id: reportRequest._id,
        query: '',
      },
    });
    return;
  }

  private getNodeTypes() {
    return [
      currentAssetsEnum.cash_on_hand,
      currentAssetsEnum.cash_on_banks,
      currentAssetsEnum.accounts_receivable,
      currentAssetsEnum.inventories,
      currentAssetsEnum.other_financial_assets,

      nonCurrentAssetsEnum.investments_in_associates,
      nonCurrentAssetsEnum.investments_in_jointly_controlled_entities,
      nonCurrentAssetsEnum.other_financial_assets_non_current,
      nonCurrentAssetsEnum.property_plant_and_equipment,
      nonCurrentAssetsEnum.investment_property,
      nonCurrentAssetsEnum.intangible_assets,
      nonCurrentAssetsEnum.biological_assets,

      currentLiabilitiesEnum.accounts_payable,
      currentLiabilitiesEnum.provisions_current,
      currentLiabilitiesEnum.other_financial_liabilities_current,

      nonCurrentLiabilitiesEnum.provisions_non_current,
      nonCurrentLiabilitiesEnum.other_financial_liabilities_non_current,
      nonCurrentLiabilitiesEnum.deferred_tax_liabilities,

      equityEnum.share_capital,
      equityEnum.unallocated_earnings,
      equityEnum.retained_earnings,
      equityEnum.non_controlling_interest,
      equityEnum.other_reserves,
    ];
  }

  private calculateSummations(accountingNodesIds, journals) {
    const results = [];

    for (const node of accountingNodesIds) {
      let totalSum = 0;

      for (const journal of journals) {
        for (const transaction of journal.transactions) {
          if (String(transaction.accountingNode._id) === String(node.id)) {
            totalSum += transaction.debit - transaction.credit;
          }
        }
      }

      results.push({
        id: node.id,
        node_type: node.node_type,
        balance_type: node.balance_type,
        total_sum: totalSum,
      });
    }

    return results;
  }

  private categorizeNodes(data: any[]) {
    const categoryEnums = {
      assets: {
        current_assets: currentAssetsEnum,
        non_current_assets: nonCurrentAssetsEnum,
      },
      equity: equityEnum,
      liability: {
        current_liability: currentLiabilitiesEnum,
        non_current_liability: nonCurrentLiabilitiesEnum,
      },
    };

    const categorizedData: any = {
      liability_equity: 0,
    };

    // Loop through the nodes and categorize them dynamically
    for (const node of data) {
      assignToAccountingNodeTypesToCategory(
        node,
        categorizedData,
        categoryEnums,
      );
    }

    // Calculate totals for each parent and sub-parent category
    if (categorizedData.assets) {
      categorizedData.assets.total = sumParentNodeTypeCategory(
        categorizedData.assets.types,
      );
    }

    if (categorizedData.liability) {
      applyNegativeMultiplierToCategory(categorizedData.liability.types);
      categorizedData.liability.total = sumParentNodeTypeCategory(
        categorizedData.liability.types,
      );
    }

    if (categorizedData.equity) {
      applyNegativeMultiplierToCategory(categorizedData.equity.types);
      categorizedData.equity.total = sumParentNodeTypeCategory(
        categorizedData.equity.types,
      );
    }

    categorizedData.liability_equity =
      (categorizedData.liability?.total || 0) +
      (categorizedData.equity?.total || 0);

    return categorizedData;
  }

  private async getJournals(
    transactionDate: Date,
    accountingNodes: Types.ObjectId[],
  ) {
    const result = await this.generalLedger.aggregate([
      {
        $match: {
          'transactions.accountingNode': { $in: accountingNodes },
          transaction_date: { $lte: transactionDate },
        },
      },
      {
        $unwind: '$transactions',
      },
      {
        $match: {
          'transactions.accountingNode': { $in: accountingNodes },
        },
      },
      {
        $group: {
          _id: '$transactions.accountingNode',
          latest_transaction_date: { $max: '$transaction_date' },
          latest_transaction: { $last: '$transactions' },
          latest_current_balance: { $last: '$transactions.current_balance' },
        },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: '_id',
          foreignField: '_id',
          as: 'accountingNodeDetails',
        },
      },
      {
        $unwind: '$accountingNodeDetails',
      },
      {
        $project: {
          _id: 0,
          accountingNode: '$_id',
          latest_transaction: 1,
          latest_transaction_date: 1,
          latest_current_balance: 1,
          node_type: '$accountingNodeDetails.node_type',
          balance_type: '$accountingNodeDetails.balance_type',
        },
      },
    ]);
    return result;
  }
}
