import {
  directCostsEnum,
  incomeTaxExpensesEnum,
  operatingExpensesEnum,
  otherIncomeAndExpensesEnum,
  revenueEnum,
} from '../accounting-node/enums/node-type.enum';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { ReportRequest } from './schema/report-request.schema';
import { PartnerType } from '../reports/enums/partner-type.enum';
import { Calculator } from '../../../utils/calculator';

export function getNodeTypes() {
  return [
    revenueEnum.sales_revenue,
    revenueEnum.other_operating_revenue,

    directCostsEnum.cost_of_goods_sold,

    operatingExpensesEnum.salaries_and_wages,
    operatingExpensesEnum.rent_and_utilities,
    operatingExpensesEnum.marketing_and_advertising_expenses,
    operatingExpensesEnum.office_supplies_and_other_general_expenses,
    operatingExpensesEnum.depreciation,

    otherIncomeAndExpensesEnum.interest_income,
    otherIncomeAndExpensesEnum.interest_expense,
    otherIncomeAndExpensesEnum.other_income,
    otherIncomeAndExpensesEnum.other_losses,

    incomeTaxExpensesEnum.income_tax_expense,
  ];
}

export function sumParentNodeTypeCategory(
  category: Record<string, any>,
): number {
  return Object.keys(category).reduce((parentSum, key) => {
    const subCategory = category[key];
    if (subCategory.types) {
      // Sub-parent level: recursively sum child categories
      subCategory.total = sumParentNodeTypeCategory(subCategory.types);
      return Calculator.sum(parentSum, subCategory.total).round().number();
    } else {
      // Child level: directly sum the values
      return Calculator.sum(parentSum, subCategory).round().number();
    }
  }, 0);
}

export function assignToAccountingNodeTypesToCategory(
  node: any,
  categorizedData: any,
  categoryEnums: Record<string, any>,
) {
  for (const [parentCategoryName, enumTypeOrSubCategory] of Object.entries(
    categoryEnums,
  )) {
    if (isObjectOfObjects(enumTypeOrSubCategory)) {
      // If this is a sub-category, handle nested structure
      if (!categorizedData[parentCategoryName]) {
        categorizedData[parentCategoryName] = { total: 0, types: {} };
      }

      for (const [subCategoryName, subEnumType] of Object.entries(
        enumTypeOrSubCategory,
      )) {
        if (!categorizedData[parentCategoryName].types[subCategoryName]) {
          categorizedData[parentCategoryName].types[subCategoryName] = {
            total: 0,
            types: {},
          };
        }

        // Check if the node belongs to this sub-category
        if (Object.values(subEnumType).includes(node.node_type)) {
          const sum = Calculator.sum(
            categorizedData[parentCategoryName].types[subCategoryName].types[
              node.node_type
            ] || 0,
            node.total_sum,
          )
            .round()
            .number();

          categorizedData[parentCategoryName].types[subCategoryName].types[
            node.node_type
          ] = sum;

          return;
        }
      }
    } else {
      // This is a top-level category (no sub-categories)
      if (!categorizedData[parentCategoryName]) {
        categorizedData[parentCategoryName] = { total: 0, types: {} };
      }

      // Add the node's total sum to the relevant type in the category
      if (Object.values(enumTypeOrSubCategory).includes(node.node_type)) {
        categorizedData[parentCategoryName].types[node.node_type] =
          Calculator.sum(
            categorizedData[parentCategoryName].types[node.node_type] || 0,
            node.total_sum,
          )
            .round()
            .number();

        return;
      }
    }
  }
}

function isObjectOfObjects(obj: any): boolean {
  return Object.values(obj).every(
    (value) => typeof value === 'object' && !Array.isArray(value),
  );
}

export function getTransactionStartingDate(fiscalYear: string) {
  if (fiscalYear === '' || fiscalYear === undefined) {
    return new Date(new Date().getFullYear(), 0, 1, 0, 0, 0, 0);
  }

  return new Date(fiscalYear);
}

export function applyNegativeMultiplierToCategory(
  category: Record<string, any>,
): void {
  Object.keys(category).forEach((key) => {
    const subCategory = category[key];
    if (subCategory.types) {
      applyNegativeMultiplierToCategory(subCategory.types);
    } else {
      category[key] = subCategory * -1;
    }
  });
}

export async function reportRequestSummaries(
  reportsService,
  request: RequestWithUser,
  journalData: any,
  reportRequest: ReportRequest,
  partnerResult?: any,
  accountResult?: any,
) {
  const endBalance = journalData[journalData.length - 1]?.transactions[0];
  const beginBalance = journalData[0]?.transactions[0];

  const beforeBalance = beginBalance?.before_balance || 0;

  let beginningBalanceDebit = 0;
  let beginningBalanceCredit = 0;

  if (parseFloat(String(beforeBalance)) > 0) {
    if (
      (journalData[0]?.transactions[0]?.accountingNode as any)?.balance_type ===
      'credit'
    ) {
      beginningBalanceCredit = Calculator.abs(beforeBalance).round().number();
    }
    if (
      (journalData[0]?.transactions[0]?.accountingNode as any)?.balance_type ===
      'debit'
    ) {
      beginningBalanceDebit = Calculator.abs(beforeBalance).round().number();
    }
  } else {
    if (
      (journalData[0]?.transactions[0]?.accountingNode as any)?.balance_type ===
      'credit'
    ) {
      beginningBalanceDebit = Calculator.abs(beforeBalance).round().number();
    }
    if (
      (journalData[0]?.transactions[0]?.accountingNode as any)?.balance_type ===
      'debit'
    ) {
      beginningBalanceCredit = Calculator.abs(beforeBalance).round().number();
    }
  }

  const calDebit = new Calculator();
  const calCredit = new Calculator();

  journalData.forEach((journal) => {
    journal?.transactions.forEach((transaction) => {
      calCredit.plus(transaction.credit).valueOf();
      calDebit.plus(transaction.debit).valueOf();
    });
  });

  const partnerData = {} as any;

  if (partnerResult !== undefined) {
    partnerData.branch = await reportsService.findBranch(
      request,
      reportRequest.filters.branch,
    );

    if (partnerResult.length > 1 || !partnerResult[0]?.name) {
      partnerData.name =
        partnerResult[0]?.type === PartnerType.customer
          ? {
              en: 'All Customers',
              ar: 'كل الزبائن',
            }
          : {
              en: 'All Vendors',
              ar: 'كل الموردين',
            };
    } else {
      partnerData.number = partnerResult[0]?.number;
      partnerData.name = partnerResult[0]?.name;
      partnerData.address = partnerResult[0]?.national_address?.short_address;
      partnerData.phone = partnerResult[0]?.mobile;
      partnerData.fax = '';
      partnerData.sales_representative = partnerResult[0]?.sales_representative;
      partnerData.note = '';
    }
  }

  const accountData = {} as any;
  if (accountResult !== undefined) {
    accountData.number = accountResult.code;
    accountData.name = accountResult.name;
    accountData.branch = await reportsService.findBranch(
      request,
      reportRequest.filters.branch,
    );
  }

  return {
    partner_data: partnerData,
    account_data: accountData,
    calculations: {
      total_debit_activities: calDebit.round().number(),
      total_credit_activities: calCredit.round().number(),
      beginning_balance_debit: reportRequest.filters.including_balance
        ? beginningBalanceDebit
        : null,
      beginning_balance_credit: reportRequest.filters.including_balance
        ? beginningBalanceCredit
        : null,
      total_balance_activities_debit: Calculator.sum(
        calDebit.valueOf(),
        beginningBalanceDebit,
      )
        .round()
        .number(),
      total_balance_activities_credit: Calculator.sum(
        calCredit.valueOf(),
        beginningBalanceCredit,
      )
        .round()
        .number(),
      ending_balance: reportRequest.filters.including_balance
        ? endBalance?.current_balance
        : null,
    },
  };
}
