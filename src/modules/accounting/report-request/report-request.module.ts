import { Module } from '@nestjs/common';

import { ReportRequestController } from './report-request.controller';
import { ReportRequestService } from './report-request.service';
import { MongooseModule } from '@nestjs/mongoose';

import {
  ReportRequest,
  reportRequestSchema,
} from './schema/report-request.schema';
import { CreateReportRequestItemsService } from './create-report-request-items.service';
import { ReportsModule } from '../reports/reports.module';
import { IncomeStatementService } from './income-statement.service';
import { BalanceSheetService } from './balance-sheet.service';
import {
  GeneralLedger,
  generalLedgerSchema,
} from '../general-ledger/schema/general-ledger.schema';
import { PartnerService } from './partner.service';
import { AccountActivityService } from './account-activity.service';
import { TrailBalanceService } from './trail-balance.service';
import { RpcModule } from '../../rpc/rpc.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: GeneralLedger.name, schema: generalLedgerSchema },
    ]),

    MongooseModule.forFeature([
      { name: ReportRequest.name, schema: reportRequestSchema },
    ]),
    RpcModule,
    ReportsModule,
  ],
  controllers: [ReportRequestController],
  providers: [
    ReportRequestService,
    CreateReportRequestItemsService,
    IncomeStatementService,
    BalanceSheetService,
    PartnerService,
    AccountActivityService,
    TrailBalanceService,
  ],
  exports: [
    ReportRequestService,
    CreateReportRequestItemsService,
    IncomeStatementService,
    BalanceSheetService,
    PartnerService,
    AccountActivityService,
    TrailBalanceService,
  ],
})
export class ReportRequestModule {}
