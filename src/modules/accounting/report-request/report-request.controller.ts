import {
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ReportRequestService } from './report-request.service';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';
import { GetReportRequestDto } from './dto/get-report-request.dto';

import { RequestWithUser } from '../../auth/interfaces/authorize.interface';

import { EventPattern, Payload } from '@nestjs/microservices';
import { publicRpc } from '../../auth/guards/public-event.decorator';
import { Types } from 'mongoose';
import { PolicyInterceptor } from '../../policy/policy.interceptor';
import { serviceName } from '../../policy/service-name.decorator';
import { PaginationDto } from '../../../utils/dto/pagination.dto';
import { accountingMessagePattern } from '../../../utils/queues.enum';

@Controller('report-request')
@ApiTags('report-request')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
export class ReportRequestController {
  constructor(private readonly reportRequestService: ReportRequestService) {}

  @Get()
  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'report_request' })
  public async find(
    @Query() query: GetReportRequestDto,
    @Req() request: RequestWithUser,
  ) {
    return await this.reportRequestService.find(query, request);
  }

  @Get(':id')
  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'report_request' })
  @UseInterceptors(PolicyInterceptor)
  @serviceName(['year_beginning_date'])
  public async findOne(
    @Param('id') id: string,
    @Query() query: PaginationDto,
    @Req() request: RequestWithUser,
  ) {
    return await this.reportRequestService.findOne(
      new Types.ObjectId(id),
      query,
      request,
    );
  }

  @Patch(':id')
  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'report_request' })
  public async save(@Param('id') id: string) {
    return await this.reportRequestService.save(id);
  }

  @Delete(':id')
  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'report_request' })
  public async delete(@Param('id') id: string) {
    return await this.reportRequestService.delete(id);
  }

  @publicRpc()
  @EventPattern({
    cmd: accountingMessagePattern.delete_temporary_report_request,
  })
  public async deleteTemporaryRequests(@Payload() payload) {
    return await this.reportRequestService.deleteTemporaryRequests(
      payload.code,
    );
  }

  @Get(':id/download')
  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'report_request' })
  public async download(@Param('id') id: string) {
    await this.reportRequestService.download(id);
  }
}
