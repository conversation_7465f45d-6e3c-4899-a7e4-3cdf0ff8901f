import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { statusEnum } from '../enum/status.enum';
import { ReportRequestItemsDto } from '../dto/report-request-items.dto';
import { ReportRequestFiltersDto } from '../dto/report-request-filters.dto';
import { ReportRequestSummariesDto } from '../dto/report-request-summaries.dto';

@Schema({ timestamps: true })
export class ReportRequest {
  @Prop({ type: String, required: true })
  name: string;

  @Prop({ required: true })
  generation_date: Date;

  @Prop({ type: String, enum: statusEnum })
  status: statusEnum;

  @Prop({
    required: true,
  })
  by_user: Types.ObjectId;

  @Prop({
    required: true,
  })
  filters: ReportRequestFiltersDto;

  @Prop({
    required: false,
  })
  summaries: ReportRequestSummariesDto;

  @Prop({
    required: false,
  })
  items: ReportRequestItemsDto[];

  @Prop({
    required: false,
    default: null,
    type: Object,
  })
  object: object;

  @Prop({ default: true })
  direct_generation: boolean;
}

export const reportRequestSchema = SchemaFactory.createForClass(ReportRequest);
