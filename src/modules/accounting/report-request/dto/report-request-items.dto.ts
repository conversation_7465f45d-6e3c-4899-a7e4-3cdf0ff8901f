import {
  IsDate,
  Is<PERSON>num,
  IsMongoId,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';
import { Types } from 'mongoose';
import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';

export class ReportRequestItemsDto {
  @IsNumber()
  @IsOptional()
  document_number?: number;

  @IsEnum(journalTypes)
  @IsOptional()
  document_name?: journalTypes;

  @IsMongoId()
  @IsOptional()
  document?: Types.ObjectId;

  @IsNumber()
  debit: number = 0;

  @IsNumber()
  credit: number = 0;

  @IsNumber()
  @IsOptional()
  balance?: number = 0;

  @IsDate()
  @IsOptional()
  date?: Date;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  account_code?: string;

  @IsString()
  @IsOptional()
  account_name_en?: string;

  @IsString()
  @IsOptional()
  account_name_ar?: string;

  @IsNumber()
  @IsOptional()
  beginning_balance?: number = 0;

  @IsNumber()
  @IsOptional()
  ending_balance?: number = 0;
}
