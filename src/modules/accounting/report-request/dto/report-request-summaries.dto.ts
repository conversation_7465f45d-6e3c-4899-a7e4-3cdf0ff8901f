import { IsArray, IsObject, IsOptional } from 'class-validator';

export class ReportRequestSummariesDto {
  @IsArray()
  @IsOptional()
  partner_data?: any;

  @IsObject()
  @IsOptional()
  account_data?: any;

  @IsObject()
  @IsOptional()
  trail_balance_data?: any;

  @IsObject()
  @IsOptional()
  income_statement_data?: any;

  @IsArray()
  @IsOptional()
  calculations?: any;

  @IsOptional()
  journal_meta?: any;

  @IsOptional()
  voucher_meta?: any;
}
