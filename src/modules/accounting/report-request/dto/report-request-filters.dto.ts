import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsMongoId,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { Types } from 'mongoose';
import { voucherType } from '../../voucher/dto/voucher-type.enum';
import { Transform } from 'class-transformer';
import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';
import { trailBalanceTypesEnum } from '../../reports/enums/trail-balance-types.enum';
import { voucherPartyType } from '../../voucher/dto/voucher-party.enum';
export class ReportRequestFiltersDto {
  @IsMongoId()
  @IsOptional()
  branch?: Types.ObjectId;

  @IsDate()
  from_date: string;

  @IsDate()
  @IsOptional()
  to_date?: string;

  @IsEnum(journalTypes)
  @IsOptional()
  journal_type?: journalTypes;

  @IsBoolean()
  @IsOptional()
  is_all_customers?: boolean;

  @IsMongoId()
  @IsOptional()
  customer?: Types.ObjectId;

  @IsBoolean()
  @IsOptional()
  is_all_vendors?: boolean;

  @IsMongoId()
  @IsOptional()
  vendor?: Types.ObjectId;

  @IsMongoId()
  @IsOptional()
  general_account?: Types.ObjectId;

  @Transform(({ value }) => {
    return value === 'true' || value === true;
  })
  @IsBoolean()
  @IsOptional()
  including_balance?: boolean;

  @IsString()
  @IsOptional()
  note?: string;

  @IsNumber()
  @IsOptional()
  from_journal_no?: number;

  @IsNumber()
  @IsOptional()
  to_journal_no?: number;

  @IsNumber()
  @IsOptional()
  from_number?: number;

  @IsNumber()
  @IsOptional()
  to_number?: number;

  @IsEnum(voucherType)
  @IsOptional()
  voucher_type?: voucherType;

  @IsEnum(voucherPartyType)
  @IsOptional()
  voucher_party_type?: voucherPartyType;
  @IsMongoId()
  @IsOptional()
  credit_account?: Types.ObjectId;

  @IsMongoId()
  @IsOptional()
  debit_account?: Types.ObjectId;

  @IsEnum(trailBalanceTypesEnum)
  @IsOptional()
  trial_balance_type?: trailBalanceTypesEnum;

  @Transform(({ value }) => {
    return value === 'true' || value === true;
  })
  @IsBoolean()
  @IsOptional()
  show_beginning_balance?: boolean;

  @Transform(({ value }) => {
    return value === 'true' || value === true;
  })
  @IsBoolean()
  @IsOptional()
  show_ending_balance?: boolean;

  @Transform(({ value }) => {
    return value === 'true' || value === true;
  })
  @IsBoolean()
  @IsOptional()
  show_transactions?: boolean;

  @Transform(({ value }) => {
    return value === 'true' || value === true;
  })
  @IsBoolean()
  @IsOptional()
  is_all_branches?: boolean;

  sort?;
}
