import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { statusEnum } from '../enum/status.enum';
import { PaginationDto } from '../../../../utils/dto/pagination.dto';

export class GetReportRequestDto extends PaginationDto {
  @ApiProperty({
    description: 'status',
    required: false,
    enum: statusEnum,
  })
  @IsOptional()
  @IsEnum(statusEnum)
  @IsOptional()
  status?: statusEnum;

  @ApiProperty({
    description: 'generation date',
    required: false,
    example: '2024-10-03',
  })
  @IsDateString()
  @IsOptional()
  generation_date?: string;
}
