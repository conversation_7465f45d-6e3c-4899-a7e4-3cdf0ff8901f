import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsMongoId,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Types } from 'mongoose';
import { statusEnum } from '../enum/status.enum';
import { reportRequestNameEnum } from '../enum/report-request-name.enum';
import { ReportRequestItemsDto } from './report-request-items.dto';
import { ReportRequestFiltersDto } from './report-request-filters.dto';

export class CreateReportRequestDto {
  @Transform(({ value }) =>
    typeof value === 'boolean' ? value : value === 'true',
  )
  @IsBoolean()
  direct_generation: boolean;

  @IsDate()
  generation_date: string;

  @IsEnum(reportRequestNameEnum)
  name: reportRequestNameEnum;

  @IsEnum(statusEnum)
  status: statusEnum = statusEnum.generating;

  @IsMongoId()
  by_user: Types.ObjectId;

  @IsArray()
  @ValidateNested()
  @Type(() => ReportRequestFiltersDto)
  filters: ReportRequestFiltersDto;

  @IsArray()
  @ValidateNested()
  @Type(() => ReportRequestItemsDto)
  @IsOptional()
  items?: ReportRequestItemsDto[];
}
