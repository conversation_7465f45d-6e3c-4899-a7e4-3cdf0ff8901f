import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { GeneralLedgerModule } from './general-ledger/general-ledger.module';
import { AccountingNodeModule } from './accounting-node/accounting-node.module';
import { VoucherModule } from './voucher/voucher.module';
import { AcountingTemplateModule } from './acounting-template/acounting-template.module';
import { AccountingNodeTemplateModule } from './accounting-node-template/accounting-node-template.module';
import { FiscalYearModule } from './fiscal-year/fiscal-year.module';
import { APP_FILTER, RouterModule } from '@nestjs/core';
import { DummyRoutesModule } from './dummy-routes/dummy-routes.module';
import { FlexibleExceptionFilter } from '../../filters/flexible-exception.filter';
import { ReportsModule } from './reports/reports.module';
import { GeneralJournalModule } from './general-journal/general-journal.module';
import { AutoNumberingModule } from './auto-numbering/auto-numbering.module';
import { TransactionsModule } from './transactions/transactions.module';
import { TempBeginningBalanceModule } from './temp-beginning-balance/temp-beginning-balance.module';
import { WorkerModule } from './worker/worker.module';
import { ReportRequestModule } from './report-request/report-request.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { PartnerGeneralLedgerModule } from './partner-general-ledger/partner-general-ledger.module';
import { ProxyPatternModule } from './proxy-pattern/proxy-pattern.module';
import { GlobalConfigMiddleware } from '../../middleware/global-config.middleware';
import AppConfig from '../../config/app.config';

@Module({
  imports: [
    GeneralLedgerModule,
    GeneralJournalModule,
    AccountingNodeModule,
    VoucherModule,
    ReportRequestModule,
    TransactionsModule,
    AcountingTemplateModule,
    AccountingNodeTemplateModule,
    FiscalYearModule,
    DummyRoutesModule,
    ReportsModule,
    AutoNumberingModule,
    TempBeginningBalanceModule,
    WorkerModule,
    DashboardModule,
    PartnerGeneralLedgerModule,
    ProxyPatternModule,
    RouterModule.register([
      { path: 'accounting', module: GeneralLedgerModule },
      { path: 'accounting', module: GeneralJournalModule },
      { path: 'accounting', module: AccountingNodeModule },
      { path: 'accounting', module: VoucherModule },
      { path: 'accounting', module: ReportRequestModule },
      { path: 'accounting', module: TransactionsModule },
      { path: 'accounting', module: AcountingTemplateModule },
      {
        path: 'accounting',
        module: AccountingNodeTemplateModule,
      },
      { path: 'accounting', module: FiscalYearModule },
      { path: 'accounting', module: ReportsModule },
      { path: 'accounting', module: AutoNumberingModule },
      { path: 'accounting', module: ProxyPatternModule },
      {
        path: 'accounting',
        module: TempBeginningBalanceModule,
      },
      {
        path: 'accounting',
        module: PartnerGeneralLedgerModule,
      },
    ]),
  ],
  providers: [
    AppConfig,
    {
      provide: APP_FILTER,
      useClass: FlexibleExceptionFilter,
    },
  ],
})
export class AccountingModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(GlobalConfigMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
