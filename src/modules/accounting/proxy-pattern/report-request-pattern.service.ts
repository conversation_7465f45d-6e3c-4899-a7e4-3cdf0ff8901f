import { Injectable } from '@nestjs/common';

import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { CreateReportRequestDto } from '../report-request/dto/create-report-request.dto';
import { ReportRequest } from '../report-request/schema/report-request.schema';

@Injectable()
export class ReportRequestProxyPatternService {
  constructor(
    @InjectModel(ReportRequest.name)
    private readonly reportRequestModel: Model<ReportRequest>,
  ) {}
  public async create(dto: CreateReportRequestDto) {
    return await this.reportRequestModel.create(dto);
  }
}
