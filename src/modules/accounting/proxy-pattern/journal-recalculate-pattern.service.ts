import { Injectable } from '@nestjs/common';
import { SchedulerRpcService } from '../../rpc/scheduler-rpc.service';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { GeneralLedger } from '../general-ledger/schema/general-ledger.schema';
import { AccountingNode } from '../accounting-node/schema/accounting-node.schema';
import { Calculator } from '../../../utils/calculator';
@Injectable()
export class JournalRecalculateProxyPatternService {
  constructor(
    private readonly schedulerRpcService: SchedulerRpcService,
    @InjectModel(GeneralLedger.name) private journalModel: Model<GeneralLedger>,
    @InjectModel(AccountingNode.name)
    private accountingNodeModel: Model<AccountingNode>,
  ) {}
  public async dispatchRecalculateJob(
    code: number,
    accountingNodes: string[] | Types.ObjectId[],
    branch: Types.ObjectId,
  ) {
    this.schedulerRpcService.dispatchRecalculateJob(
      code,
      accountingNodes,
      branch,
    );

    return true;
  }

  public async hasJournals(nodeId: Types.ObjectId, gtDate: string = null) {
    const query: any = { 'transactions.accountingNode': nodeId };

    if (gtDate) {
      query.transaction_date = {
        $gt: new Date(gtDate),
      };
    }
    return await this.journalModel.findOne(query);
  }

  async calculateCurrentState(document_id) {
    const documentData = await this.journalModel.find({
      document: new Types.ObjectId(document_id),
    });
    const acNodeMap = new Map();
    for (const doc of documentData) {
      for (let index = 0; index < doc.transactions.length; index++) {
        const element = doc.transactions[index];

        if (!acNodeMap.has(String(element.accountingNode))) {
          //init Ac node
          acNodeMap.set(String(element.accountingNode), {
            accountingNode: String(element.accountingNode),
            total_credit: 0, //element.credit,
            total_debit: 0, //element.debit,
            net_balance: 0, //Calculator.subtract(element.credit, element.debit), // Positive means net credit, negative means net debit
            partner_type: undefined,
            partner_id: undefined,
          });
        }
        const nodeData = acNodeMap.get(String(element.accountingNode));
        if (element.credit !== 0) {
          nodeData.total_credit = Calculator.sum(
            nodeData.total_credit,
            element.credit,
          ).valueOf();
        } else if (element.debit !== 0) {
          nodeData.total_debit = Calculator.sum(
            nodeData.total_debit,
            element.debit,
          ).valueOf();
        }

        nodeData.net_balance = Calculator.subtract(
          nodeData.total_credit,
          nodeData.total_debit,
        ).valueOf();
        nodeData.partner_type = element.partner_type;
        nodeData.partner_id = element.partner_id;
      }
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const [key, value] of acNodeMap) {
      value.accountingNode = await this.accountingNodeModel.findOne({
        _id: new Types.ObjectId(value.accountingNode),
      });
      /*       if (value.net_balance == 0) {
        acNodeMap.delete(key);
      } */
    }
    const result = Object.fromEntries(acNodeMap);
    return this.convertToArray(result);
  }

  private convertToArray(inputObj) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    return Object.entries(inputObj).map(([id, value]: [string, any]) => {
      return {
        accountingNode: value.accountingNode,
        code: value.accountingNode.code, // You might want to dynamically generate this or get it from somewhere
        credit: value.net_balance > 0 ? Math.abs(value.net_balance) : 0,
        debit: value.net_balance < 0 ? Math.abs(value.net_balance) : 0,
        description: 'description',
        note: 'note',
      };
    });
  }
}
