import { Modu<PERSON> } from '@nestjs/common';
import { AccountingNodeProxyPatternService } from './accounting-node-pattern.service';
import {
  AccountingNode,
  accountingNodeSchema,
} from '../accounting-node/schema/accounting-node.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { JournalRecalculateProxyPatternService } from './journal-recalculate-pattern.service';
import {
  GeneralLedger,
  generalLedgerSchema,
} from '../general-ledger/schema/general-ledger.schema';
import {
  ReportRequest,
  reportRequestSchema,
} from '../report-request/schema/report-request.schema';
import { ReportRequestProxyPatternService } from './report-request-pattern.service';
import { RpcModule } from '../../rpc/rpc.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AccountingNode.name, schema: accountingNodeSchema },
      { name: GeneralLedger.name, schema: generalLedgerSchema },
      { name: ReportRequest.name, schema: reportRequestSchema },
    ]),
    RpcModule,
  ],
  providers: [
    AccountingNodeProxyPatternService,
    JournalRecalculateProxyPatternService,
    ReportRequestProxyPatternService,
  ],
  exports: [
    AccountingNodeProxyPatternService,
    JournalRecalculateProxyPatternService,
    ReportRequestProxyPatternService,
  ],
})
export class ProxyPatternModule {}
