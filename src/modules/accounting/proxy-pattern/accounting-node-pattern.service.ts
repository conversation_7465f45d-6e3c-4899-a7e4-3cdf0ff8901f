import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  AccountingNode,
  AccountingNodeDocument,
} from '../accounting-node/schema/accounting-node.schema';
import { Model, Types } from 'mongoose';
import { IAccountingNode } from '../accounting-node/interfaces/accounting.interface';
import { accountingTypeEnum } from '../accounting-node/interfaces/accounting-type.enum';
import { invoicePartyType } from '../accounting-node/interfaces/invoice-party-type.enum';
import { FindOneAccountingNodeDto } from '../accounting-node/dto/find-one-accounting-node.dto';

@Injectable()
export class AccountingNodeProxyPatternService {
  constructor(
    @InjectModel(AccountingNode.name)
    private accountingNodeModel: Model<AccountingNode>,
  ) {}

  async findAllRawId(
    ids: any[],
    branch: Types.ObjectId,
  ): Promise<IAccountingNode[]> {
    const query: {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      is_parent: boolean;
      _id?: any;
      type?: accountingTypeEnum[];
      // eslint-disable-next-line @typescript-eslint/naming-convention
      invoice_party_type?: invoicePartyType;
      group?: string | Types.ObjectId;
      search?: string;
      branch?: Types.ObjectId;
    } = {
      is_parent: false,
    };

    if (ids && ids.length > 0) {
      query._id = { $in: ids };
    }

    const orActions = [];
    orActions.push({
      $or: [{ branch: { $exists: false } }, { branch }],
    });

    return (await this.accountingNodeModel.find({
      ...query,
      ...orActions[0],
    })) as IAccountingNode[];
  }

  async findOne(
    query: FindOneAccountingNodeDto,
    branch?: Types.ObjectId,
  ): Promise<AccountingNodeDocument> {
    const filters: any = { ...query };

    if (branch) {
      filters.$or = [{ branch: { $exists: false } }, { branch }];
    }

    return await this.accountingNodeModel.findOne(filters).populate(['group']);
  }

  public async bulkUpdate(bulkOperations: any[]) {
    await this.accountingNodeModel.bulkWrite(bulkOperations);
  }
}
