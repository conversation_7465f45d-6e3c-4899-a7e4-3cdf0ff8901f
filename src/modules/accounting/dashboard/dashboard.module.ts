import { Modu<PERSON> } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';
import { AccountingNodeModule } from '../accounting-node/accounting-node.module';
import { ReportsModule } from '../reports/reports.module';

@Module({
  imports: [ReportsModule, AccountingNodeModule],
  providers: [DashboardService],
  controllers: [DashboardController],
})
export class DashboardModule {}
