import { Test, TestingModule } from '@nestjs/testing';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { ReportsService } from '../reports/reports.service';
import { PolicyService } from '../../policy/policy.service';
import { AccountingNodeService } from '../accounting-node/accounting-node.service';

describe('DashboardController', () => {
  let controller: DashboardController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardController],
      providers: [
        DashboardService,
        {
          provide: ReportsService,
          useValue: {},
        },
        {
          provide: AccountingNodeService,
          useValue: {},
        },
        {
          provide: PolicyService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<DashboardController>(DashboardController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
