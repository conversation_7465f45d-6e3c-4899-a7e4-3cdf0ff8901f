import { Injectable } from '@nestjs/common';
import {
  assignToAccountingNodeTypesToCategory,
  getNodeTypes,
  getTransactionStartingDate,
  sumParentNodeTypeCategory,
} from '../report-request/helper';
import { ReportsService } from '../reports/reports.service';
import { Types } from 'mongoose';
import {
  directCostsEnum,
  incomeTaxExpensesEnum,
  operatingExpensesEnum,
  otherIncomeAndExpensesEnum,
  revenueEnum,
} from '../accounting-node/enums/node-type.enum';
import { DashboardIncomeStatementDto } from './dto/dashboard-income-statement.dto';
import { Calculator } from '../../../utils/calculator';

@Injectable()
export class DashboardService {
  constructor(private readonly reportsService: ReportsService) {}

  public async incomeStatement(dto: DashboardIncomeStatementDto) {
    const nodeTypes = getNodeTypes();

    const accountingNodeResult =
      await this.reportsService.findAccountingNodeAllRaw(
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        nodeTypes,
      );

    const nodeIds = accountingNodeResult.map(
      (node) => new Types.ObjectId(node.id),
    );

    const startOfDate = getTransactionStartingDate(
      dto.policyData.year_beginning_date,
    );

    let endOfDate;
    if (dto.to_date) {
      endOfDate = new Date(
        new Date(dto.to_date).getTime() + 24 * 60 * 60 * 1000 - 1,
      );
    } else {
      endOfDate = new Date();
      endOfDate.setHours(23, 59, 59, 999);
    }

    const query: any = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      nodeIds: nodeIds,
      from_equal_transaction_date: startOfDate,
      to_equal_transaction_date: endOfDate,
    };

    const journals = await this.reportsService.findJournals(
      query,
      null,
      undefined,
    );

    const nodeIdsAndNodeTypes = accountingNodeResult.map((node) => ({
      id: new Types.ObjectId(node._id),
      node_type: node.node_type,
    }));

    const result = this.calculateSummations(
      nodeIdsAndNodeTypes,
      journals.result,
    );

    return this.categorizeNodes(result);
  }

  private calculateSummations(accountingNodesIds, journals) {
    const results = [];

    for (const node of accountingNodesIds) {
      let totalSum = 0;

      for (const journal of journals) {
        for (const transaction of journal.transactions) {
          if (String(transaction.accountingNode._id) === String(node.id)) {
            totalSum += transaction.debit - transaction.credit;
          }
        }
      }

      results.push({
        id: node.id,
        node_type: node.node_type,
        total_sum: totalSum,
      });
    }

    return results;
  }

  private categorizeNodes(data: any[]) {
    const categoryEnums = {
      revenue: revenueEnum,
      direct_costs: directCostsEnum,
      operating_expenses: operatingExpensesEnum,
      other_income_and_expenses: otherIncomeAndExpensesEnum,
      income_tax_expenses: incomeTaxExpensesEnum,
    };

    const categorizedData: any = {
      gross_profit: 0,
      operating_profit: 0,
      profit_before_tax: 0,
      income_tax_expenses: 0,
      net_profit: 0,
    };

    // Loop through the nodes and categorize them dynamically
    for (const node of data) {
      assignToAccountingNodeTypesToCategory(
        node,
        categorizedData,
        categoryEnums,
      );
    }

    if (categorizedData.revenue) {
      this.applyNegativeMultiplierToCategory(categorizedData.revenue.types);
      categorizedData.revenue.total = sumParentNodeTypeCategory(
        categorizedData.revenue.types,
      );
    }

    if (categorizedData.direct_costs) {
      this.applyNegativeMultiplierToCategory(
        categorizedData.direct_costs.types,
      );
      categorizedData.direct_costs.total = sumParentNodeTypeCategory(
        categorizedData.direct_costs.types,
      );
    }

    if (categorizedData.operating_expenses) {
      this.applyNegativeMultiplierToCategory(
        categorizedData.operating_expenses.types,
      );
      categorizedData.operating_expenses.total = sumParentNodeTypeCategory(
        categorizedData.operating_expenses.types,
      );
    }

    if (categorizedData.other_income_and_expenses) {
      this.applyNegativeMultiplierToCategory(
        categorizedData.other_income_and_expenses.types,
      );
      categorizedData.other_income_and_expenses.total =
        sumParentNodeTypeCategory(
          categorizedData.other_income_and_expenses.types,
        );
    }

    categorizedData.gross_profit = Calculator.sum(
      categorizedData.revenue?.total || 0,
      categorizedData.direct_costs?.total || 0,
    )
      .round()
      .number();

    categorizedData.operating_profit = Calculator.sum(
      categorizedData.gross_profit,
      categorizedData.operating_expenses?.total || 0,
    )
      .round()
      .number();

    categorizedData.profit_before_tax = Calculator.sum(
      categorizedData.operating_profit,
      categorizedData.other_income_and_expenses?.total || 0,
    )
      .round()
      .number();

    if (categorizedData.income_tax_expenses) {
      categorizedData.income_tax_expenses =
        new Calculator(
          sumParentNodeTypeCategory(categorizedData.income_tax_expenses.types),
        )
          .round()
          .number() || 0;
    }

    categorizedData.net_profit = Calculator.sum(
      categorizedData.profit_before_tax,
      categorizedData.income_tax_expenses,
    )
      .round()
      .number();

    return categorizedData;
  }

  private applyNegativeMultiplierToCategory(
    category: Record<string, any>,
  ): void {
    Object.keys(category).forEach((key) => {
      const subCategory = category[key];
      if (subCategory.types) {
        this.applyNegativeMultiplierToCategory(subCategory.types);
      } else {
        category[key] = subCategory * -1;
      }
    });
  }
}
