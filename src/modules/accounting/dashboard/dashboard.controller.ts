import { Controller, UseInterceptors } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { publicRpc } from '../../auth/guards/public-event.decorator';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { PolicyInterceptor } from '../../policy/policy.interceptor';
import { serviceName } from '../../policy/service-name.decorator';
import { DashboardIncomeStatementDto } from './dto/dashboard-income-statement.dto';
import { AccountingNodeService } from '../accounting-node/accounting-node.service';

import { nodeTypeEnum } from '../accounting-node/enums/node-type.enum';
import { accountingMessagePattern } from '../../../utils/queues.enum';
import { RpcDto } from '../../../utils/dto/rpc.dto';

@Controller('dashboard')
export class DashboardController {
  constructor(
    private readonly dashboardService: DashboardService,
    private readonly accountingNodeService: AccountingNodeService,
  ) {}

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.get_income_statement })
  @UseInterceptors(PolicyInterceptor)
  @serviceName(['year_beginning_date'])
  async incomeStatement(
    @Payload()
    dto: DashboardIncomeStatementDto,
  ) {
    return await this.dashboardService.incomeStatement(dto);
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.get_bank_accounts })
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async bankAccounts(@Payload() dto: RpcDto) {
    const result = await this.accountingNodeService.findAll({
      node_type: [nodeTypeEnum.cash_on_banks],
      branch: undefined,
    });

    return result.result;
  }
}
