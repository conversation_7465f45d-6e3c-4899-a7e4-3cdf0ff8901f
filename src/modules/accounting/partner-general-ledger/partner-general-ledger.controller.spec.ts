import { Test, TestingModule } from '@nestjs/testing';
import { PartnerGeneralLedgerController } from './partner-general-ledger.controller';
import { PartnerGeneralLedgerService } from './partner-general-ledger.service';
import { getModelToken } from '@nestjs/mongoose';
import { PartnerGeneralLedger } from './schema/partner-general-ledger.schema';
import { TradeRpcService } from '../../rpc/trade-rpc.service';

describe('PartnerGeneralLedgerController', () => {
  let controller: PartnerGeneralLedgerController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PartnerGeneralLedgerController],
      providers: [
        PartnerGeneralLedgerService,
        { provide: getModelToken(PartnerGeneralLedger.name), useValue: {} },
        { provide: TradeRpcService, useValue: {} },
      ],
    }).compile();

    controller = module.get<PartnerGeneralLedgerController>(
      PartnerGeneralLedgerController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
