import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { partnerType } from '../enum/partner-type.enum';

import { Transform, Type } from 'class-transformer';

import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

import { RpcDto } from '../../../../utils/dto/rpc.dto';

export class PartnerDto extends RpcDto {
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  accounting_node?: Types.ObjectId;

  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  id: Types.ObjectId;

  @IsNotEmpty()
  @IsEnum(partnerType)
  type: partnerType;

  @IsOptional()
  @IsNumber()
  balance?: number;

  @IsOptional()
  @IsNumber()
  beginning_balance?: number;
}

export class GetPartnerGeneralLedgerDto extends RpcDto {
  @IsNotEmpty()
  @IsArray()
  @ValidateNested()
  @Type(() => PartnerDto)
  partners: PartnerDto[];
}
