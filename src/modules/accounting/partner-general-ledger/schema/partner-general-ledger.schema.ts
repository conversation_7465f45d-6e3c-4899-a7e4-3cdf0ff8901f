import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';
import { ITransaction } from '../../general-ledger/dto/transaction.dto';
import {
  SoftDeleteDocument,
  softDeletePlugin,
} from '../../../../utils/schemas';

export type PartnerGeneralLedgerDocument =
  HydratedDocument<PartnerGeneralLedger> & SoftDeleteDocument;

@Schema({ timestamps: true })
export class PartnerGeneralLedger {
  @Prop()
  transactions: ITransaction[];

  @Prop({ type: String, enum: journalTypes })
  type: journalTypes;

  @Prop()
  note: string;

  @Prop({ type: Types.ObjectId })
  by_user: Types.ObjectId;

  @Prop()
  reference: string;

  @Prop({ type: Date, default: Date.now() })
  transaction_date: Date;

  @Prop({ type: Types.ObjectId })
  Inventory_journal: Types.ObjectId;

  @Prop({ type: Types.ObjectId })
  document: Types.ObjectId;

  @Prop()
  document_code: string;

  @Prop({ type: Types.ObjectId })
  branch: Types.ObjectId;

  createdAt?: Date;

  @Prop({ type: Boolean, default: false })
  is_beginning_balance: boolean;
}

export const partnerGeneralLedgerSchema =
  SchemaFactory.createForClass(PartnerGeneralLedger);
partnerGeneralLedgerSchema.plugin(softDeletePlugin);
