import { Controller } from '@nestjs/common';
import { PartnerGeneralLedgerService } from './partner-general-ledger.service';
import { publicRpc } from '../../auth/guards/public-event.decorator';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { PartnerDto } from './dto/get-partner-general-ledger.dto';
import { accountingMessagePattern } from '../../../utils/queues.enum';

@Controller('partner-general-ledger')
export class PartnerGeneralLedgerController {
  constructor(
    private readonly partnerGeneralLedgerService: PartnerGeneralLedgerService,
  ) {}

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.partner_general_ledger })
  public async partnerGeneralLedgers(@Payload() data: PartnerDto) {
    return await this.partnerGeneralLedgerService.partnerLedgerDetailsByPartnerId(
      data,
    );
  }

  @publicRpc()
  @MessagePattern({
    cmd: accountingMessagePattern.get_single_partner_general_ledgers,
  })
  public async getPartnerGeneralLedgers(@Payload() data: PartnerDto) {
    return await this.partnerGeneralLedgerService.singlePartnerLedgers(data);
  }
}
