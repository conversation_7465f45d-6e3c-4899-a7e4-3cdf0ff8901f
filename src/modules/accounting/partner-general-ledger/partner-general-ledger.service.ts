import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  PartnerGeneralLedger,
  PartnerGeneralLedgerDocument,
} from './schema/partner-general-ledger.schema';
import { isValidObjectId, Types } from 'mongoose';
import { PartnerDto } from './dto/get-partner-general-ledger.dto';
import { partnerType } from './enum/partner-type.enum';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { GetGeneralLedgerDto } from '../general-ledger/dto/get-general-ledger.dto';
import { abstractTypeEnum } from '../accounting-node/dto/find-abstract.dto';
import { CreateGeneralLedgerDto } from '../general-ledger/dto/create-general-ledger.dto';
import { results } from '../general-ledger/interface/result.interface';
import { sortByBeginningBalanceAndDate } from '../../../utils/ledger-sort';
import { Calculator } from '../../../utils/calculator';
import { paginate } from '../../../utils/dto/pagination.dto';
import { SoftDeleteModel } from '../../../utils/schemas';

@Injectable()
export class PartnerGeneralLedgerService {
  constructor(
    @InjectModel(PartnerGeneralLedger.name)
    private partnerGeneralLedger: SoftDeleteModel<PartnerGeneralLedgerDocument>,
    private readonly tradeRpcService: TradeRpcService,
  ) {}

  public async create(dto: any, code: number) {
    const ledger = await this.partnerGeneralLedger.create(dto);

    for (const transaction of ledger.transactions) {
      await this.tradeRpcService.updatePartnerBalance({
        code: code,
        id: transaction.partner_id,
        type: transaction.partner_type as partnerType,
        balance:
          transaction.credit !== 0 ? -transaction.credit : transaction.debit,
      });
    }
  }

  public async update(ledgerData: any, code: number) {
    const ledger = await this.partnerGeneralLedger.findOne({
      _id: ledgerData._id,
    });

    if (ledger) {
      ledger.transactions = ledgerData.transactions;
      ledger.save();
    } else {
      await this.create(ledgerData, code);
    }
  }

  public async findOne(
    query: any,
  ): Promise<PartnerGeneralLedgerDocument | null> {
    return await this.partnerGeneralLedger.findOne(query);
  }

  public async delete(id: Types.ObjectId) {
    await this.partnerGeneralLedger.findOneAndDelete({
      _id: id,
    });
  }

  async partnerLedgerDetailsByPartnerId(dto: PartnerDto) {
    const partnerId = dto.id;

    const result = await this.partnerGeneralLedger.aggregate([
      {
        $match: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'transactions.partner_id': partnerId,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'transactions.partner_type': dto.type,
        },
      },
      { $unwind: '$transactions' },
      {
        $project: {
          month: { $month: '$transaction_date' },
          year: { $year: '$transaction_date' },
          credit: {
            $cond: {
              if: { $in: ['$transactions.partner_id', [partnerId]] },
              then: '$transactions.credit',
              else: 0,
            },
          },
          debit: {
            $cond: {
              if: { $in: ['$transactions.partner_id', [partnerId]] },
              then: '$transactions.debit',
              else: 0,
            },
          },
          transaction_date: '$transaction_date',
        },
      },
      {
        $group: {
          _id: { month: '$month', year: '$year' },
          credit: { $sum: '$credit' },
          debit: { $sum: '$debit' },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          lastTransactionDate: { $max: '$transaction_date' },
        },
      },
      {
        $addFields: {
          credit: { $round: ['$credit', 2] },
          debit: { $round: ['$debit', 2] },
        },
      },
      { $sort: { 'date.year': 1, 'date.month': 1 } },
    ]);

    let beforeRecordBalance = 0;
    let totalCredit = 0;
    let totalDebit = 0;
    result.forEach((record) => {
      record.balance = record.debit - record.credit + beforeRecordBalance;
      beforeRecordBalance = record.balance;
      totalCredit += record.credit;
      totalDebit += record.debit;

      if (dto.type === partnerType.customer) {
        record.balance = +Number(record.balance * 1).toFixed(2);
      }
      if (dto.type === partnerType.vendor) {
        record.balance = +Number(record.balance * -1).toFixed(2);
      }
    });
    const outStandingBalance = totalDebit - totalCredit;

    return {
      result: result,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      outStandingBalance:
        dto.type === partnerType.customer
          ? +Number(outStandingBalance * 1).toFixed(2)
          : +Number(beforeRecordBalance * -1).toFixed(2),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      totalDebit: +Number(totalDebit).toFixed(2),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      totalCredit: +Number(totalCredit).toFixed(2),
    };
  }

  async singlePartnerLedgers(dto: PartnerDto) {
    let query: any = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.partner_id': dto.id,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.partner_type': dto.type,
    };

    if (dto.accounting_node) {
      query = { ...query, 'transactions.accountingNode': dto.accounting_node };
    }

    return await this.partnerGeneralLedger.find(query);
  }

  public async recalculate(
    updatePartnerGeneraLedgersData: any,
    branch: Types.ObjectId,
    code: number,
  ) {
    const updatePartnerBalanceData = [];

    const partners = await this.tradeRpcService.getPartnersAbstract(code, [
      abstractTypeEnum.customer,
      abstractTypeEnum.vendor,
    ]);

    const allPartnersIds = partners.map((partner) => ({
      id: partner._id,
      type: partner.type,
    }));

    const journals = await this.partnerGeneralLedger
      .find({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'transactions.partner_id': {
          $in: allPartnersIds.map((partner) => new Types.ObjectId(partner.id)),
        },
      })
      .select('transactions.partner_id')
      .lean();

    const partnersWithTransactions = new Set(
      journals.flatMap((journal) =>
        journal.transactions.map((transaction) =>
          String(transaction.partner_id),
        ),
      ),
    );

    const partnersWithoutTransactions = allPartnersIds.filter(
      (partner) => !partnersWithTransactions.has(String(partner.id)),
    );

    for (const partner of partnersWithoutTransactions) {
      updatePartnerBalanceData.push({
        id: partner.id,
        type: partner.type,
        balance: 0,
      });
    }

    const partnersIdsSet = new Set<string>();

    const convertedPartnerUpdateOperations = updatePartnerGeneraLedgersData.map(
      (operation) => {
        const { filter, update } = operation.updateOne;

        const convertedFilter = {
          _id: filter._id,
          branch: branch,
        };

        const convertedUpdate = {
          $set: {},
        };

        for (const key in update.$set) {
          const value = update.$set[key];
          const updatedValue = { ...value };

          if (isValidObjectId(value.accountingNode)) {
            updatedValue.accountingNode = new Types.ObjectId(
              value.accountingNode,
            );
          }
          if (isValidObjectId(value.by_user)) {
            updatedValue.by_user = new Types.ObjectId(value.by_user);
          }
          if (value.partner_id && isValidObjectId(value.partner_id)) {
            const uniqueKey = `${value.partner_id}_${value.partner_type}`;
            partnersIdsSet.add(uniqueKey);
            updatedValue.partner_id = new Types.ObjectId(value.partner_id);
          }

          convertedUpdate.$set[key] = updatedValue;
        }

        const arrayFilters = [
          {
            'elem.accountingNode':
              convertedUpdate.$set[Object.keys(convertedUpdate.$set)[0]]
                .accountingNode,
          },
        ];

        return {
          updateOne: {
            filter: convertedFilter,
            update: convertedUpdate,
            arrayFilters,
          },
        };
      },
    );
    if (convertedPartnerUpdateOperations.length > 0) {
      await this.partnerGeneralLedger.bulkWrite(
        convertedPartnerUpdateOperations,
      );
    }

    const partnersIds = Array.from(partnersIdsSet).map((key) => {
      const [id, type] = key.split('_');
      return { id, type }; // Return an object with id and type
    });

    const partnersJournals = await this.partnerGeneralLedger
      .find({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'transactions.partner_id': {
          $in: partnersIds.map((partner) => new Types.ObjectId(partner.id)),
        },
      })
      .lean();

    for (const partner of partnersIds) {
      let filteredJournals = partnersJournals.filter((journal) => {
        return journal.transactions.some(
          (transaction) =>
            String(transaction.partner_id) === String(partner.id),
        );
      });

      if (filteredJournals.length < 1) {
        updatePartnerBalanceData.push({
          id: partner.id,
          type: partner.type,
          balance: 0,
        });
      }

      if (filteredJournals.length > 0) {
        filteredJournals = filteredJournals.map((journal) => ({
          ...journal,
          transaction_date: new Date(
            journal.transaction_date.toISOString().split('T')[0],
          ),
        }));

        filteredJournals.sort(sortByBeginningBalanceAndDate);

        let accountNodeBalanceCalculate = 0;

        for (let index = 0; index < filteredJournals.length; index++) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/naming-convention
          const { createdAt, updatedAt, __v, accountingNodePUP, ...journal } =
            filteredJournals[index] as any;

          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          journal.transactions.map((transaction: any, index) => {
            if (String(transaction.partner_id) === String(partner.id)) {
              accountNodeBalanceCalculate = Calculator.subtract(
                accountNodeBalanceCalculate,
                transaction.credit,
              )
                .round()
                .number();

              accountNodeBalanceCalculate = Calculator.sum(
                accountNodeBalanceCalculate,
                transaction.debit,
              )
                .round()
                .number();
            }
          });
        }
        updatePartnerBalanceData.push({
          id: new Types.ObjectId(partner.id),
          type: partner.type,
          balance: +Number(accountNodeBalanceCalculate).toFixed(2),
        });
      }
    }

    if (updatePartnerBalanceData.length > 0) {
      this.tradeRpcService.updatePartnersBalances(
        code,
        updatePartnerBalanceData,
      );
    }
  }
  public async deleteByDocument(id: Types.ObjectId) {
    const rs = await this.partnerGeneralLedger.findOne({
      document: id,
    });
    await rs.softDelete();
  }

  async findAll(
    dto: GetGeneralLedgerDto,
    ids: Types.ObjectId[],
  ): Promise<results> {
    let query: any = {};

    if (Array.isArray(ids)) {
      query._id = { $in: ids };
    }
    if (dto.code) {
      query = { ...query, 'transactions.code': dto.code };
    }
    if (dto.codes) {
      query = { ...query, 'transactions.code': { $in: dto.codes } };
    }
    if (dto.nodeIds) {
      query = { ...query, 'transactions.accountingNode': { $in: dto.nodeIds } };
    }
    if (dto.partners) {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      query = { ...query, 'transactions.partner_id': { $in: dto.partners } };
    }
    if (dto.from_number) {
      query.number = { $gte: dto.from_number };
    }
    if (dto.to_number) {
      query.number = { $lte: dto.to_number };
    }
    if (dto.from_date || dto.to_date) {
      query.createdAt = {};
      if (dto.from_date) {
        query.createdAt.$gte = new Date(dto.from_date);
      }
      if (dto.to_date) {
        query.createdAt.$lte = new Date(dto.to_date);
      }
    }
    if (dto.from_transaction_date) {
      query.transaction_date = {};
      query.transaction_date.$gt = new Date(dto.from_transaction_date);
    }

    if (dto.from_equal_transaction_date) {
      query.transaction_date = {};
      query.transaction_date.$gte = new Date(dto.from_equal_transaction_date);

      if (dto.to_equal_transaction_date) {
        query.transaction_date.$lte = new Date(dto.to_equal_transaction_date);
      }
    }

    if (dto.queries) {
      const searchText = { $regex: new RegExp(dto.queries), $options: 'i' };
      query = {
        ...query,
        $or: [
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$number' },
                regex: new RegExp(dto.queries),
                options: 'i',
              },
            },
          },
          {
            note: searchText,
          },
          {
            type: searchText,
          },
        ],
      } as any;
    }

    const aggrigateQuery = [
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNodePUP',
        },
      },
    ] as any;
    if (dto.page && dto.limit) {
      aggrigateQuery.push(
        { $skip: (dto.page - 1) * dto.limit },
        { $limit: dto.limit },
      );
    }
    const data = await this.partnerGeneralLedger.aggregate(aggrigateQuery);
    const meta = await paginate(
      dto.limit,
      dto.page,
      this.partnerGeneralLedger,
      query,
    );

    data.forEach((object) => {
      object.transactions.forEach((transaction) => {
        const matchingNode = object.accountingNodePUP.find(
          (accountingNode) =>
            accountingNode._id.toString() ===
            transaction.accountingNode.toString(),
        );

        if (matchingNode) {
          transaction.accountingNode = matchingNode;
        }
      });
      object.accountingNodePUP = object.accountingNodePUP.map(() => null);
    });

    return { result: data, meta };
  }

  public async addPartnerBeginningBalance(
    id: Types.ObjectId,
    data: CreateGeneralLedgerDto,
  ) {
    const beginningBalanceJournal = await this.partnerGeneralLedger.findOne({
      _id: id,
      is_beginning_balance: true,
    });

    const filteredData = {
      ...data,
      _id: id,
      transactions: data.transactions.filter(
        (transaction) => transaction.partner_id !== undefined,
      ),
    };

    if (beginningBalanceJournal) {
      if (filteredData.transactions.length > 0) {
        return await this.partnerGeneralLedger
          .findOneAndUpdate(
            {
              _id: id,
              is_beginning_balance: true,
            },
            filteredData,
            { new: true },
          )
          .exec();
      }
    } else {
      return await this.partnerGeneralLedger.create(filteredData);
    }
  }
}
