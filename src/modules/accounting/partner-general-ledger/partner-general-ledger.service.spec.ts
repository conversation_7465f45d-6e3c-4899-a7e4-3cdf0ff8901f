import { Test, TestingModule } from '@nestjs/testing';
import { PartnerGeneralLedgerService } from './partner-general-ledger.service';
import { getModelToken } from '@nestjs/mongoose';
import { PartnerGeneralLedger } from './schema/partner-general-ledger.schema';
import { TradeRpcService } from '../../rpc/trade-rpc.service';

describe('PartnerGeneralLedgerService', () => {
  let service: PartnerGeneralLedgerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PartnerGeneralLedgerService,
        { provide: getModelToken(PartnerGeneralLedger.name), useValue: {} },
        { provide: TradeRpcService, useValue: {} },
      ],
    }).compile();

    service = module.get<PartnerGeneralLedgerService>(
      PartnerGeneralLedgerService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
