import { Modu<PERSON> } from '@nestjs/common';
import { PartnerGeneralLedgerController } from './partner-general-ledger.controller';
import { PartnerGeneralLedgerService } from './partner-general-ledger.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  PartnerGeneralLedger,
  partnerGeneralLedgerSchema,
} from './schema/partner-general-ledger.schema';
import { RpcModule } from '../../rpc/rpc.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PartnerGeneralLedger.name, schema: partnerGeneralLedgerSchema },
    ]),
    RpcModule,
  ],
  controllers: [PartnerGeneralLedgerController],
  providers: [PartnerGeneralLedgerService],
  exports: [PartnerGeneralLedgerService],
})
export class PartnerGeneralLedgerModule {}
