import { Test, TestingModule } from '@nestjs/testing';
import { AccountingNodeTemplateService } from './accounting-node-template.service';
import { AccountingNodeTemplate } from './schema/accounting-node-template.schema';
import { getModelToken } from '@nestjs/mongoose';

describe('AccountingNodeTemplateService', () => {
  let service: AccountingNodeTemplateService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AccountingNodeTemplateService,
        {
          provide: getModelToken(AccountingNodeTemplate.name, 'commonDb'),
          useValue: {},
        },
      ],
    }).compile();

    service = await module.resolve<AccountingNodeTemplateService>(
      AccountingNodeTemplateService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
