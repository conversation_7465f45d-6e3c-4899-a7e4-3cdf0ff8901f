import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  <PERSON><PERSON>num,
  IsMongoId,
  IsOptional,
  IsString,
  Validate,
} from 'class-validator';
import { Types } from 'mongoose';
import { accountingTypeEnum } from '../../accounting-node/interfaces/accounting-type.enum';
import { reportingTypeEnum } from '../../accounting-node/interfaces/reporting-type.enum';
import { Transform } from 'class-transformer';

import { ApiProperty } from '@nestjs/swagger';
import {
  NameDto,
  validateEnLanguage,
  validateLanguage,
} from '../../../../utils/dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';
import { nodeTypeEnum } from '../../accounting-node/enums/node-type.enum';

export class CreateAccountingNodeTemplateDto {
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  template?: Types.ObjectId;

  @IsString()
  code: string;

  @ApiProperty({
    description: 'name of account node',
    example: 'test',
    required: true,
  })
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @IsOptional()
  group?: Types.ObjectId | string | number;

  @IsEnum(accountingTypeEnum)
  type?: accountingTypeEnum;

  @IsEnum(reportingTypeEnum)
  reporting_type?: reportingTypeEnum;

  @IsString()
  invoice_party_type?: string;

  @IsMongoId()
  invoice_party_id?: Types.ObjectId;

  @IsOptional()
  @IsString()
  @IsEnum(nodeTypeEnum)
  node_type?: nodeTypeEnum;

  @IsBoolean()
  report_type?: string;

  @IsArray()
  related_partners?: Types.ObjectId[] = [];

  is_parent?: boolean;

  @IsString()
  @IsOptional()
  balance_type?: string;

  static?: boolean;

  hidden?: boolean;

  active?: boolean;

  _id?: Types.ObjectId;
}
