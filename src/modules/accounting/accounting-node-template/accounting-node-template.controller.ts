import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Header,
  <PERSON>s,
  UseGuards,
} from '@nestjs/common';
import { createReadStream } from 'fs';
import { unlink } from 'fs/promises';

import { AccountingNodeTemplateService } from './accounting-node-template.service';
import { CreateAccountingNodeTemplateDto } from './dto/create-accounting-node-template.dto';
import { UpdateAccountingNodeTemplateDto } from './dto/update-accounting-node-template.dto';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';
import { publicRoute } from '../../casl/guards/public-route.decorator';
import { PaginationDto } from '../../../utils/dto';
import { skipSchemaTransaction } from '../../postgres-database/schema-transaction.decorator';

@ApiTags('accounting-node-template')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: false,
})
@Controller('accounting-node-template')
export class AccountingNodeTemplateController {
  constructor(
    private readonly accountingNodeTemplateService: AccountingNodeTemplateService,
  ) {}

  @UseGuards(CaslGuard)
  @abilities({ action: 'create', subject: 'accounting_node_template' })
  @Post()
  create(
    @Body() createAccountingNodeTemplateDto: CreateAccountingNodeTemplateDto,
  ) {
    return this.accountingNodeTemplateService.create(
      createAccountingNodeTemplateDto,
    );
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'accounting_node_template' })
  @Get()
  findAll(@Query() query: PaginationDto) {
    return this.accountingNodeTemplateService.findAll(query);
  }

  @skipSchemaTransaction()
  @publicRoute()
  @Get('/static')
  getStatic() {
    return this.accountingNodeTemplateService.findStatic();
  }

  @skipSchemaTransaction()
  @publicRoute()
  @Get('/static/tree')
  getStaticTree() {
    return this.accountingNodeTemplateService.getStaticTree();
  }
  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'accounting_node_template' })
  @Header(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @Header('Content-Disposition', 'attachment; filename="acNode.xlsx"')
  @Get('/export')
  async exportAll(@Res() res) {
    const rs = await this.accountingNodeTemplateService.exportExcel({});
    const file = createReadStream(rs);
    file.pipe(res);
    file.on('end', async () => {
      await unlink(rs);
    });
    file.on('error', async () => {
      try {
        await unlink(rs);
      } catch (error) {
        console.log(error);
      }
    });
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'accounting_node_template' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.accountingNodeTemplateService.findOne(id);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'edit', subject: 'accounting_node_template' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateAccountingNodeTemplateDto: UpdateAccountingNodeTemplateDto,
  ) {
    return this.accountingNodeTemplateService.update(
      id,
      updateAccountingNodeTemplateDto,
    );
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'delete', subject: 'accounting_node_template' })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.accountingNodeTemplateService.remove(id);
  }
}
