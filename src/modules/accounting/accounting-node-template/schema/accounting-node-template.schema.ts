import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { AcountingTemplate } from '../../acounting-template/schema/acounting-template.schema';
import { NameDto } from '../../../../utils/dto';

@Schema({ timestamps: true })
export class AccountingNodeTemplate {
  @Prop({ type: Types.ObjectId, ref: AcountingTemplate.name })
  template: Types.ObjectId;
  @Prop()
  code: string;
  @Prop({ type: NameDto, required: true })
  name: NameDto;
  @Prop({ type: Types.ObjectId, ref: AccountingNodeTemplate.name })
  parent: Types.ObjectId;
  @Prop()
  type: string;
  @Prop()
  invoice_party_type: string;
  @Prop()
  is_parent: boolean;
  @Prop()
  balance_type: string;
}
export const accountingNodeTemplateSchema = SchemaFactory.createForClass(
  AccountingNodeTemplate,
);
