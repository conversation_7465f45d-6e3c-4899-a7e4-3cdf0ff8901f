import { HttpException, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateAccountingNodeTemplateDto } from './dto/create-accounting-node-template.dto';
import { UpdateAccountingNodeTemplateDto } from './dto/update-accounting-node-template.dto';
import * as XLSX from 'xlsx';
import * as path from 'path';
import * as fsp from 'fs/promises';

import { AccountingNodeTemplate } from './schema/accounting-node-template.schema';

import { IAccountingNode } from '../accounting-node/interfaces/accounting.interface';
import { paginate, PaginationDto } from '../../../utils/dto/pagination.dto';
import { nodeSeeder } from '../../../utils/seeders/accounting-node-seeder';
import { arrayToTree } from '../../../utils/array-to-tree';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class AccountingNodeTemplateService {
  constructor(
    @InjectModel(AccountingNodeTemplate.name, 'commonDb')
    private accountingNodeTemplateModel: Model<AccountingNodeTemplate>,
  ) {}
  create(createAccountingNodeTemplateDto: CreateAccountingNodeTemplateDto) {
    return new this.accountingNodeTemplateModel(
      createAccountingNodeTemplateDto,
    ).save();
  }
  async insertManyclean(
    createAccountingNodeTemplateDto: CreateAccountingNodeTemplateDto[],
  ) {
    await this.accountingNodeTemplateModel.deleteMany({});
    return await this.accountingNodeTemplateModel.insertMany(
      createAccountingNodeTemplateDto,
    );
  }

  async findAll(query: PaginationDto) {
    const { page, limit } = query;
    const result = this.accountingNodeTemplateModel
      .find()
      .skip((page - 1) * limit)
      .limit(limit);
    const meta = await paginate(
      limit,
      page,
      this.accountingNodeTemplateModel,
      {},
    );
    return { result, meta };
  }
  async findStatic() {
    return nodeSeeder(undefined);
  }
  async createNodeRo(nodes: IAccountingNode[]) {
    //1. no parents
    //2.find childs of this parent
    //3 if dont has parent its leaves

    const resp = [];
    for (let index = 0; index < nodes.length; index++) {
      nodes[index].id = nodes[index]._id;
      const element = nodes[index];
      //element.createdAt
      delete element.updatedAt;
      delete element.__v;
      if (!element.group) {
        resp.push(element);
      }
    }
    for (let index = 0; index < nodes.length; index++) {
      const element = nodes[index];
      if (element.group?._id) {
        console.log(element.group._id);

        const tree = arrayToTree(nodes, element.group._id);
        console.log(tree);

        const indexParent = resp.findIndex((data) => {
          return String(data._id) == String(element.group._id);
        });
        if (indexParent > -1) {
          resp[indexParent].nodes = tree.length > 0 ? tree : null;
        }
      }
    }
    // const rs = arrayToTree(nodes, 0);
    // accountingNodes .push(rs)

    // eslint-disable-next-line @typescript-eslint/naming-convention
    return { docs: resp, nodeCount: nodes.length };
  }
  async getStaticTree() {
    return this.createNodeRo(nodeSeeder(undefined));
  }
  async findAllRaw(query = {}): Promise<any[]> {
    const result = await this.accountingNodeTemplateModel
      .find(query)
      .select('-template')
      .lean();
    return result;
  }
  findOne(_id: string) {
    return this.accountingNodeTemplateModel.findOne({ _id });
  }

  update(
    _id: string,
    updateAccountingNodeTemplateDto: UpdateAccountingNodeTemplateDto,
  ) {
    return this.accountingNodeTemplateModel.updateOne(
      { _id },
      { $set: { ...updateAccountingNodeTemplateDto } },
    );
  }
  async remove(_id: string) {
    const isDeleteAble = !(await this.hasChildren(_id));
    if (isDeleteAble) {
      return this.accountingNodeTemplateModel.deleteOne({ _id });
    }
    throw new HttpException(
      nameOf(erpExceptionCode, (x) => x.notEmptyNode),
      erpExceptionCode.notEmptyNode,
    );
  }
  async hasChildren(_id: string): Promise<boolean> {
    const nodes = await this.accountingNodeTemplateModel.find({ parent: _id });
    if (nodes.length > 0) {
      return true;
    }
    return false;
  }

  async exportExcel(data): Promise<any> {
    const nodes = await this.findAllRaw(data);
    try {
      nodes.forEach((el) => {
        el._id = String(el._id);
        el.parent = String(el.parent || '');
        delete el.__v;
        delete el.createdAt;
        delete el.updatedAt;
        el.is_parent = String(el.is_parent);
      });
      const wscols = Array(Object.keys(nodes[0]).length).fill({ wch: 1 });
      Object.keys(nodes[0]).forEach((ObjName, index) => {
        nodes.forEach((data) => {
          const colSize = Math.max(
            String(data[ObjName]).length,
            String(ObjName).length,
          );
          wscols[index] = { wch: colSize };
        });
      });

      const workbook = XLSX.utils.book_new();
      const filename = 'mySheet';
      const ws = XLSX.utils.json_to_sheet(nodes);
      ws['!cols'] = wscols;
      XLSX.utils.book_append_sheet(workbook, ws, filename.replace('/', ''));

      // eslint-disable-next-line @typescript-eslint/naming-convention
      const files = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      const time = new Date().toISOString();
      const dest = path.join(process.cwd(), 'temp', time);
      await fsp.writeFile(`${dest}.xlsx`, files, { encoding: 'utf-8' });
      // await XLSX.writeFile(files,`${dest}.xls`, );
      return `${dest}.xlsx`;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.createFile),
        erpExceptionCode.createFile,
      );
    }
  }
}
