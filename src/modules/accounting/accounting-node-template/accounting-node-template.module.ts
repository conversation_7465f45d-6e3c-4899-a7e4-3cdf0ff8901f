import { Module } from '@nestjs/common';
import { AccountingNodeTemplateService } from './accounting-node-template.service';
import { AccountingNodeTemplateController } from './accounting-node-template.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AccountingNodeTemplate,
  accountingNodeTemplateSchema,
} from './schema/accounting-node-template.schema';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: AccountingNodeTemplate.name,
          schema: accountingNodeTemplateSchema,
        },
      ],
      'commonDb',
    ),
  ],
  controllers: [AccountingNodeTemplateController],
  providers: [AccountingNodeTemplateService],
  exports: [AccountingNodeTemplateService],
})
export class AccountingNodeTemplateModule {}
