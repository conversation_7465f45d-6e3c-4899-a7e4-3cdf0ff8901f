import { Test, TestingModule } from '@nestjs/testing';
import { AccountingNodeTemplateController } from './accounting-node-template.controller';
import { AccountingNodeTemplateService } from './accounting-node-template.service';
import { getModelToken } from '@nestjs/mongoose';
import { AccountingNodeTemplate } from './schema/accounting-node-template.schema';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { CaslAbilityFactory } from '../../casl/casl-ability.factory';

describe('AccountingNodeTemplateController', () => {
  let controller: AccountingNodeTemplateController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AccountingNodeTemplateController],
      providers: [
        AccountingNodeTemplateService,
        {
          provide: getModelToken(AccountingNodeTemplate.name, 'commonDb'),
          useValue: {},
        },
        {
          provide: UserRpcService,
          useValue: {},
        },
        {
          provide: CaslAbilityFactory,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<AccountingNodeTemplateController>(
      AccountingNodeTemplateController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
