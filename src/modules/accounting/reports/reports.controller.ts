import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { AccountingReportsService } from './accounting-reports.service';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';
import { TrailBalanceDto } from './dto/trail-balance.dto';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { PartnerAccountActivityStatementDto } from './dto/partner-account-activity-statement.dto';
import { PartnerType } from './enums/partner-type.enum';
import { AccountActivityStatementDto } from './dto/account-activity-statement.dto';
import { ReportType } from './enums/reportType.enum';
import { GeneralJournalReport } from './dto/journal-reports.dto';
import { GetVoucherActivity } from './dto/voucher-activity.dto';
import { BalanceSheetStatementDto } from './dto/balance-sheet-statement.dto';
import { IncomeStatementDto } from './dto/income-statement.dto';
import { FeatureAccessibilityGuard } from '../../casl/guards/feature-accessibility.guard';
import { feature } from '../../casl/guards/feature.decorator';

@ApiTags('reports')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('reports')
export class ReportsController {
  constructor(
    private readonly accountingReportsService: AccountingReportsService,
  ) {}

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'trail_balance_report' })
  @abilities({ action: 'list', subject: 'trail_balance_report' })
  @Post('/trail-balance')
  public async getTrailBalance(
    @Body() query: TrailBalanceDto,
    @Req() request: RequestWithUser,
  ) {
    return await this.accountingReportsService.getTrailBalance(query, request);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'journal_report' })
  @abilities({ action: 'list', subject: 'journal_report' })
  @Post('/journal')
  public async getJournalReport(
    @Body() journalReport: GeneralJournalReport,
    @Req() request: RequestWithUser,
  ) {
    return await this.accountingReportsService.journalReport(
      journalReport,
      request,
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'voucher_report' })
  @abilities({ action: 'list', subject: 'voucher_report' })
  @Post('/voucher-activity')
  public async genVoucherReport(
    @Body() journalReport: GetVoucherActivity,
    @Req() request: RequestWithUser,
  ) {
    return await this.accountingReportsService.voucherReport(
      journalReport,
      request,
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'customer_report' })
  @abilities({ action: 'list', subject: 'customer_report' })
  @Post('/customer-account-activity')
  async customerAccountActivityList(
    @Body() query: PartnerAccountActivityStatementDto,
    @Req() request: RequestWithUser,
  ) {
    query.partner_type = PartnerType.customer;
    return await this.accountingReportsService.partnerAccountActivityList(
      request,
      query,
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'vendor_report' })
  @abilities({ action: 'list', subject: 'vendor_report' })
  @Post('/vendor-account-activity')
  async vendorAccountActivityList(
    @Body() query: PartnerAccountActivityStatementDto,
    @Req() request: RequestWithUser,
  ) {
    query.partner_type = PartnerType.vendor;
    return await this.accountingReportsService.partnerAccountActivityList(
      request,
      query,
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'account_report' })
  @abilities({ action: 'list', subject: 'account_report' })
  @Post('/account-activity')
  async accountActivityList(
    @Body() query: AccountActivityStatementDto,
    @Req() request: RequestWithUser,
  ) {
    query.report_type = ReportType.general;
    return await this.accountingReportsService.accountActivityList(
      request,
      query,
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'cashier_report' })
  @abilities({ action: 'list', subject: 'cashier_report' })
  @Post('/cashier-account-activity')
  async cashierAccountActivityList(
    @Body() query: AccountActivityStatementDto,
    @Req() request: RequestWithUser,
  ) {
    query.report_type = ReportType.cashier;
    return await this.accountingReportsService.accountActivityList(
      request,
      query,
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'income_statement_report' })
  @abilities({ action: 'list', subject: 'income_statement_report' })
  @Post('/income-statement')
  async incomeStatement(
    @Body() query: IncomeStatementDto,
    @Req() request: RequestWithUser,
  ) {
    return await this.accountingReportsService.incomeStatement(request, query);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'balance_sheet_report' })
  @abilities({ action: 'list', subject: 'balance_sheet_report' })
  @Post('/balance-sheet')
  async balanceSheet(
    @Body() query: BalanceSheetStatementDto,
    @Req() request: RequestWithUser,
  ) {
    return await this.accountingReportsService.balanceSheet(request, query);
  }
}
