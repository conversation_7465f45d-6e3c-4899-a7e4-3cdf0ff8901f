import { HttpException, Injectable } from '@nestjs/common';
import { TrailBalanceDto } from './dto/trail-balance.dto';
import { statusEnum } from '../report-request/enum/status.enum';
import { Types } from 'mongoose';
import { reportRequestNameEnum } from '../report-request/enum/report-request-name.enum';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { PartnerAccountActivityStatementDto } from './dto/partner-account-activity-statement.dto';
import { PartnerType } from './enums/partner-type.enum';
import { AccountActivityStatementDto } from './dto/account-activity-statement.dto';
import { ReportType } from './enums/reportType.enum';

import { ReportsService } from './reports.service';
import { GeneralJournalReport } from './dto/journal-reports.dto';
import { GetVoucherActivity } from './dto/voucher-activity.dto';
import { BalanceSheetStatementDto } from './dto/balance-sheet-statement.dto';
import { IncomeStatementDto } from './dto/income-statement.dto';
import { nodeTypeEnum } from '../accounting-node/enums/node-type.enum';
import { ReportRequestProxyPatternService } from '../proxy-pattern/report-request-pattern.service';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';

@Injectable()
export class AccountingReportsService {
  constructor(
    private readonly reportRequestService: ReportRequestProxyPatternService,
    private readonly reportsService: ReportsService,
  ) {}

  public async getTrailBalance(dto: TrailBalanceDto, request: RequestWithUser) {
    const reportRequest = await this.reportRequestService.create({
      by_user: request?.user?.user_id,
      name: reportRequestNameEnum.trail_balance_report,
      direct_generation: true,
      generation_date: new Date().toISOString(),
      status: statusEnum.generating,
      filters: {
        branch: new Types.ObjectId(request?.headers['branch']),
        from_date: dto.date,
        trial_balance_type: dto.type,
        show_beginning_balance: dto.showBeginningBalance,
        show_ending_balance: dto.showEndingBalance,
        show_transactions: dto.showTransactions,
        is_all_branches: dto.is_all_branches,
      },
    });

    return { report_request: reportRequest._id };
  }

  async partnerAccountActivityList(
    request: RequestWithUser,
    query: PartnerAccountActivityStatementDto,
  ) {
    const {
      partner,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      partner_type,
      type,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      include_balance,
      note,
    } = query;

    let customer,
      vendor = null;
    let isAllCustomers,
      isAllVendors = false;
    let reportName;

    switch (partner_type) {
      case PartnerType.customer:
        customer = partner ? partner : null;
        isAllCustomers = partner ? false : true;
        reportName = reportRequestNameEnum.customer_report;
        break;

      case PartnerType.vendor:
        vendor = partner ? partner : null;
        isAllVendors = partner ? false : true;
        reportName = reportRequestNameEnum.vendor_report;
        break;
    }

    const reportRequest = await this.reportRequestService.create({
      by_user: request?.user?.user_id,
      name: reportName,
      direct_generation: true,
      generation_date: new Date().toISOString(),
      status: statusEnum.generating,
      filters: {
        branch: new Types.ObjectId(request?.headers['branch']),
        from_date: from_date,
        to_date: to_date,
        journal_type: type,
        including_balance: include_balance,
        note: note,
        customer: customer,
        is_all_customers: isAllCustomers,
        vendor: vendor,
        is_all_vendors: isAllVendors,
      },
    });

    return { report_request: reportRequest._id };
  }

  async accountActivityList(
    request: RequestWithUser,
    query: AccountActivityStatementDto,
  ) {
    const {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      account_node,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      include_balance,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      report_type,
      note,
      type,
    } = query;

    const acNodeValidateQuery = { _id: account_node } as any;
    const accountingNode = await this.reportsService.findAccountingNode(
      acNodeValidateQuery,
      new Types.ObjectId(request?.headers['branch']),
    );
    if (
      query.report_type === ReportType.cashier &&
      accountingNode.node_type !== nodeTypeEnum.cash_on_hand
    ) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.accountingNodeCashier),
        erpExceptionCode.accountingNodeCashier,
      );
    }

    let reportName;

    switch (report_type) {
      case ReportType.cashier:
        reportName = reportRequestNameEnum.cashier_activity_report;
        break;

      case ReportType.general:
        reportName = reportRequestNameEnum.account_activity_report;
        break;

      default:
        throw new HttpException('unknown report type', 400);
    }

    const reportRequest = await this.reportRequestService.create({
      by_user: request?.user?.user_id,
      name: reportName,
      direct_generation: true,
      generation_date: new Date().toISOString(),
      status: statusEnum.generating,
      filters: {
        branch: new Types.ObjectId(request?.headers['branch']),
        general_account: accountingNode._id,
        from_date: from_date,
        to_date: to_date,
        journal_type: type,
        including_balance: include_balance,
        note: note,
      },
    });

    return { report_request: reportRequest._id };
  }

  async journalReport(
    journalReportDto: GeneralJournalReport,
    request: RequestWithUser,
  ) {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { from_date, to_date, from_number, to_number } = journalReportDto;
    const filters = {
      from_date,
      to_date,
      from_number,
      to_number,
      branch: new Types.ObjectId(request?.headers['branch']),
    };
    const reportRequest = await this.reportRequestService.create({
      by_user: request?.user?.user_id,
      name: reportRequestNameEnum.journal_report,
      direct_generation: true,
      generation_date: new Date().toISOString(),
      status: statusEnum.generating,
      filters,
    });

    return { report_request: reportRequest._id };
  }

  async voucherReport(query: GetVoucherActivity, request: RequestWithUser) {
    const {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      voucher_party_type,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      voucher_type,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      fromNumber,
      toNumber,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      credit_account,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      debit_account,
    } = query;

    const branch = request.headers['branch'];

    const filters = {
      voucher_type,
      voucher_party_type,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      fromNumber,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      toNumber,
      branch,
      credit_account,
      from_date: from_date,
      to_date: to_date,
      debit_account,
    };
    const reportRequest = await this.reportRequestService.create({
      by_user: request?.user?.user_id,
      name: reportRequestNameEnum.voucher_report,
      direct_generation: true,
      generation_date: new Date().toISOString(),
      status: statusEnum.generating,
      filters,
    });
    return { report_request: reportRequest._id };
  }

  public async incomeStatement(
    request: RequestWithUser,
    dto: IncomeStatementDto,
  ) {
    const reportRequest = await this.reportRequestService.create({
      by_user: request?.user?.user_id,
      name: reportRequestNameEnum.income_statement_report,
      direct_generation: true,
      generation_date: new Date().toISOString(),
      status: statusEnum.generating,
      filters: {
        branch: new Types.ObjectId(request?.headers['branch']),
        from_date: dto.from_date,
        to_date: dto.to_date,
        is_all_branches: dto.is_all_branches,
      },
    });

    return { report_request: reportRequest._id };
  }

  public async balanceSheet(
    request: RequestWithUser,
    dto: BalanceSheetStatementDto,
  ) {
    const reportRequest = await this.reportRequestService.create({
      by_user: request?.user?.user_id,
      name: reportRequestNameEnum.balance_sheet_report,
      direct_generation: true,
      generation_date: new Date().toISOString(),
      status: statusEnum.generating,
      filters: {
        branch: new Types.ObjectId(request?.headers['branch']),
        from_date: dto.from_date,
        to_date: dto.to_date,
        is_all_branches: dto.is_all_branches,
      },
    });

    return { report_request: reportRequest._id };
  }
}
