import { Modu<PERSON> } from '@nestjs/common';
import { ReportsService } from './reports.service';
import { AccountingNodeModule } from '../accounting-node/accounting-node.module';
import { GeneralLedgerModule } from '../general-ledger/general-ledger.module';
import { AccountingReportsService } from './accounting-reports.service';
import { ReportsController } from './reports.controller';
import { GeneralJournalModule } from '../general-journal/general-journal.module';
import { VoucherModule } from '../voucher/voucher.module';
import { PartnerGeneralLedgerModule } from '../partner-general-ledger/partner-general-ledger.module';
import { ProxyPatternModule } from '../proxy-pattern/proxy-pattern.module';
import { RpcModule } from '../../rpc/rpc.module';

@Module({
  imports: [
    AccountingNodeModule,
    GeneralLedgerModule,
    PartnerGeneralLedgerModule,
    ProxyPatternModule,
    GeneralJournalModule,
    VoucherModule,
    RpcModule,
  ],
  controllers: [ReportsController],
  providers: [ReportsService, AccountingReportsService],
  exports: [ReportsService],
})
export class ReportsModule {}
