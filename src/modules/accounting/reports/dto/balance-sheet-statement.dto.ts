import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsDate } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class BalanceSheetStatementDto {
  @ApiProperty({
    description: 'Statment from date',
    example: '2024-03-1',
  })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  from_date?: string;

  @ApiProperty({
    description: 'Statment to date',
    example: '2024-09-30',
    format: 'date',
  })
  @IsDate()
  @Type(() => Date)
  to_date: string;

  @ApiProperty({
    example: true,
  })
  @Transform(({ value }) => {
    return value === 'true' || value === true;
  })
  @IsBoolean()
  @IsOptional()
  is_all_branches: boolean = false;
}
