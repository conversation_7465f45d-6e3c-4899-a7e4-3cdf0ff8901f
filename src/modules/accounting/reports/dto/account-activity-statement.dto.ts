import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { IsObjectId } from 'class-validator-mongo-object-id';
import { ReportsStatementDto } from './reports-statement.dto';
import { ReportType } from '../enums/reportType.enum';
import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';

export class AccountActivityStatementDto extends ReportsStatementDto {
  @ApiProperty({
    description: 'Accounting Node ID',
    example: '63400372c582a633284bf24c',
  })
  @IsObjectId()
  @IsOptional()
  account_node: string;

  @ApiProperty({
    description: 'type of journal',
    enum: journalTypes,
    isArray: false,
    example: journalTypes.sales_invoice,
    required: false,
  })
  @IsOptional()
  @IsEnum(journalTypes)
  type: journalTypes;

  @ApiHideProperty()
  report_type?: ReportType;
}
