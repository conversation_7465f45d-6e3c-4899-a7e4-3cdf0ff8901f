import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsDate, IsIn } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class ReportsStatementDto {
  @ApiProperty({
    description: 'Statment from date',
    example: '2022-2-1',
  })
  @IsDate()
  @Type(() => Date)
  from_date: string;

  @ApiProperty({
    description: 'Statment to date',
    example: '2022-2-30',
    format: 'date',
  })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  to_date?: string;

  @ApiProperty({
    example: true,
  })
  @Transform(({ value }) => {
    return value === 'true' || value === true;
  })
  @IsBoolean()
  @IsOptional()
  include_balance?: boolean;

  @ApiProperty({
    example: true,
  })
  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsIn(['createdAt', '-createdAt', 'number', '-number'])
  @IsOptional()
  sort?: string;

  @IsString()
  @IsOptional()
  queries?: string;
}
