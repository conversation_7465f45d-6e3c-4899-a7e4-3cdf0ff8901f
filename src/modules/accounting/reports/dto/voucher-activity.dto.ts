import { Type } from 'class-transformer';
import { IsDate } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { IsObjectId } from 'class-validator-mongo-object-id';
import { voucherType } from '../../voucher/dto/voucher-type.enum';

import { Types } from 'mongoose';
import { voucherPartyType } from '../../voucher/dto/voucher-party.enum';

export class GetVoucherActivity {
  @ApiProperty({
    description: 'credit_account',
    example: '63400372c582a633284bf24c',
  })
  @IsObjectId()
  @IsOptional()
  credit_account?: Types.ObjectId;

  @ApiProperty({
    description: 'credit_account',
    example: '63400372c582a633284bf24c',
  })
  @IsObjectId()
  @IsOptional()
  debit_account?: Types.ObjectId;

  /*   @IsObjectId()
  @IsOptional()
  branch?: string; */

  @ApiProperty({
    description: 'Statment from date',
    example: '2022-2-1',
  })
  @IsDate()
  @Type(() => Date)
  from_date: string;

  @ApiProperty({
    description: 'Statment to date',
    example: '2022-2-30',
    format: 'date',
  })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  to_date?: string;

  @IsString()
  @IsOptional()
  fromNumber?: string;

  @ApiProperty({
    description: 'Statment to date',
    example: '2022-2-30',
    format: 'date',
  })
  @IsString()
  @IsOptional()
  toNumber?: string;

  @IsEnum(voucherType)
  voucher_type: voucherType;

  @IsOptional()
  @IsEnum(voucherPartyType)
  voucher_party_type?: voucherPartyType;

  /*   @ApiProperty({
    description: 'type of journal',
    enum: journalTypes,
    isArray: false,
    example: journalTypes.sales_invoice,
    required: false,
  })
  @IsOptional()
  @IsEnum(journalTypes)
  type: journalTypes; */
}
