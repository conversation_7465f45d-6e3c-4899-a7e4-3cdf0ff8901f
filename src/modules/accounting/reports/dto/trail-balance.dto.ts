import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDate, IsEnum, IsOptional } from 'class-validator';
import { trailBalanceTypesEnum } from '../enums/trail-balance-types.enum';
import { Transform, Type } from 'class-transformer';

export class TrailBalanceDto {
  @ApiProperty({
    description: 'show Beginning balance',
    default: true,
  })
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  showBeginningBalance: boolean;

  @ApiProperty({
    description: 'show Ending balance',
    default: true,
  })
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  showEndingBalance: boolean;

  @ApiProperty({
    description: 'show transactions',
    default: true,
  })
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  showTransactions: boolean;

  @ApiProperty({
    description: 'for all branches',
    default: true,
  })
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsOptional()
  is_all_branches: boolean;

  @ApiProperty({
    description: 'report type',
    enum: trailBalanceTypesEnum,
    isArray: false,
    example: trailBalanceTypesEnum.all,
    required: true,
  })
  @IsEnum(trailBalanceTypesEnum)
  type: trailBalanceTypesEnum;

  @ApiProperty({
    description: 'date',
    format: 'date',
  })
  @IsDate()
  @Type(() => Date)
  date: string;
}
