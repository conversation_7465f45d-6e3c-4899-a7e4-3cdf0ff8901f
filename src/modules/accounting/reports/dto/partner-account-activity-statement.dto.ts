import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { IsObjectId } from 'class-validator-mongo-object-id';
import { ReportsStatementDto } from './reports-statement.dto';
import { PartnerType } from '../enums/partner-type.enum';
import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';

export class PartnerAccountActivityStatementDto extends ReportsStatementDto {
  @ApiProperty({
    description: 'partner ID',
    example: '63400372c582a633284bf24c',
  })
  @IsObjectId()
  @IsOptional()
  partner?: string;

  @ApiHideProperty()
  partner_type: PartnerType;

  @ApiProperty({
    description: 'type of journal',
    enum: journalTypes,
    isArray: false,
    example: journalTypes.sales_invoice,
    required: false,
  })
  @IsOptional()
  @IsEnum(journalTypes)
  type: journalTypes;
}
