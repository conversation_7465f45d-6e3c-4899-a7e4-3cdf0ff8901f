import { ApiProperty } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import {
  IsOptional,
  IsDate,
  IsString,
  IsIn,
  IsNumber,
  IsNotEmpty,
} from 'class-validator';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class GeneralJournalReport {
  @ApiProperty({
    description: 'from number',
    example: 0,
  })
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  from_number?: number;

  @ApiProperty({
    description: 'to number',
    example: 0,
  })
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  to_number?: number;

  @ApiProperty({
    description: 'brnach of journal',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;

  @ApiProperty({
    description: 'Statment from date',
    example: '2022-2-1',
  })
  @IsDate()
  @Type(() => Date)
  from_date: string;

  @ApiProperty({
    description: 'Statment to date',
    example: '2022-2-30',
    format: 'date',
  })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  to_date?: string;

  @IsString()
  @IsIn(['createdAt', '-createdAt', 'number', '-number'])
  @IsOptional()
  sort?: string;

  @IsOptional()
  @IsString()
  queries?: string;
}

export class BranchHeader {
  @ApiProperty({
    description: 'brnach ',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;
}
