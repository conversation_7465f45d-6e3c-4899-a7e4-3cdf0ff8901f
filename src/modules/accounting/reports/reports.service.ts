import { HttpException, Injectable } from '@nestjs/common';

import { AccountingNodeService } from '../accounting-node/accounting-node.service';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { GetGeneralLedgerDto } from '../general-ledger/dto/get-general-ledger.dto';
import { Types } from 'mongoose';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { PartnerType } from './enums/partner-type.enum';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { accountingTypeEnum } from '../accounting-node/interfaces/accounting-type.enum';
import { invoicePartyType } from '../accounting-node/interfaces/invoice-party-type.enum';
import { AccountingTreeDto } from '../accounting-node/dto/accounting-tree.dto';
import { TrailBalanceDto } from './dto/trail-balance.dto';
import { GeneralJournalService } from '../general-journal/general-journal.service';
import { GetGeneralJournalDto } from '../general-journal/dto/get-general-journal.dto';
import { VoucherService } from '../voucher/voucher.service';
import { ReportRequestFiltersDto } from '../report-request/dto/report-request-filters.dto';
import * as _ from 'lodash';
import { PartnerGeneralLedgerService } from '../partner-general-ledger/partner-general-ledger.service';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { nameOf } from '../../../utils/object-key-name';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';

@Injectable()
export class ReportsService {
  constructor(
    private readonly accountingNodeService: AccountingNodeService,
    private readonly accountingNodeProxyPatternService: AccountingNodeProxyPatternService,
    private readonly journalService: GeneralLedgerService,
    private readonly partnerLedgerService: PartnerGeneralLedgerService,
    private readonly tradeRpcService: TradeRpcService,
    private readonly voucherService: VoucherService,
    private readonly generalJournalService: GeneralJournalService,
    private readonly userRpcService: UserRpcService,
  ) {}

  public async findAccountingNode(
    acNodeValidateQuery: any,
    branch: Types.ObjectId,
  ) {
    const accountingNode = await this.accountingNodeProxyPatternService.findOne(
      acNodeValidateQuery,
      branch,
    );

    if (!accountingNode) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.accountingNodeNotFound),
        erpExceptionCode.accountingNodeNotFound,
      );
    }
    return accountingNode;
  }

  public async accountingTree(
    pagination: AccountingTreeDto,
    isAllBranches: boolean = true,
    req: RequestWithUser,
  ) {
    return await this.accountingNodeService.accountingTree(
      pagination,
      isAllBranches,
      req,
    );
  }

  public async findAccountingNodeAllRaw(
    ids?: any[],
    type?: accountingTypeEnum[],
    invoice_party_type?: invoicePartyType,
    group?: string | Types.ObjectId,
    search?: string,
    branch?: Types.ObjectId,
    allAcNodes: boolean = false,
    nodeTypes?: string[],
  ) {
    return await this.accountingNodeService.findAllRaw(
      ids,
      type,
      invoice_party_type,
      group,
      search,
      branch,
      allAcNodes,
      nodeTypes,
    );
  }

  public async findAccountingNodes(acNodeValidateQuery: any) {
    const accountingNodes =
      await this.accountingNodeService.findByIds(acNodeValidateQuery);

    if (accountingNodes.length < 1) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.accountingNodeNotFound),
        erpExceptionCode.accountingNodeNotFound,
      );
    }
    return accountingNodes;
  }

  public async findJournals(
    querys: GetGeneralLedgerDto,
    sort: string = '',
    ids: Types.ObjectId[],
  ) {
    return await this.journalService.findAll(querys, sort, ids);
  }

  public async findPartnerLedger(
    dto: GetGeneralLedgerDto,
    ids: Types.ObjectId[],
  ) {
    return await this.partnerLedgerService.findAll(dto, ids);
  }

  public async findGeneralJournal(
    querys: GetGeneralJournalDto,
    sort: string = '',
    ids: Types.ObjectId[],
  ) {
    return await this.generalJournalService.findAllReport(querys, sort, ids);
  }

  public async getTrailBalance(
    accountingNodeLeafs: any[],
    dto: TrailBalanceDto,
    branch?: Types.ObjectId,
  ) {
    return await this.journalService.getTrailBalance(
      accountingNodeLeafs,
      dto,
      branch,
    );
  }

  public async findPartnerAccounts(
    request: RequestWithUser,
    partnerType: PartnerType,
    id?: Types.ObjectId | string | null,
  ) {
    const partnersAccounts = await this.tradeRpcService.getPartnersAccounts(
      request.user?.code,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      { id, partnerType },
    );

    if (partnersAccounts.count < 1) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.partnerNotFound),
        erpExceptionCode.partnerNotFound,
      );
    }

    return partnersAccounts;
  }

  public async findBranch(request: RequestWithUser, branchId: Types.ObjectId) {
    const branch = await this.userRpcService.getOneBranch(
      request.user?.code,
      branchId,
    );

    if (!branch) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.branchNotFound),
        erpExceptionCode.branchNotFound,
      );
    }
    return branch;
  }

  public async voucherFindAllRaw(data: ReportRequestFiltersDto) {
    /*     const {
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      voucher_type,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      voucher_party_type,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      from_number,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      to_number,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      branch,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      credit_account,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      from_date,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      to_date,
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      debit_account,
                    } = data; */
    const pickedData = _.pick(data, [
      'voucher_type',

      'voucher_party_type',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'from_number',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'to_number',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'branch',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'credit_account',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'from_date',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'to_date',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'debit_account',
    ]);
    const filters = {
      type: pickedData.voucher_type,
      invoice_party_type: pickedData.voucher_party_type,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      fromNumber: pickedData.from_number
        ? String(pickedData.from_number)
        : undefined,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      toNumber: pickedData.to_number ? String(pickedData.to_number) : undefined,
      branch: pickedData.branch,
      credit_account: pickedData.credit_account
        ? String(pickedData.credit_account)
        : undefined,
      from_date: pickedData.from_date,
      to_date: pickedData.to_date,
      debit_account: pickedData.debit_account
        ? String(pickedData.debit_account)
        : undefined,
    };
    const result = _.omitBy(filters, _.isNil);

    const voucherData = await this.voucherService.findAllRaw(result);
    return voucherData;
  }
}
