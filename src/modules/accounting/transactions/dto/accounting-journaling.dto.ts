import { Types } from 'mongoose';
import { journalTypes } from '../../../../modules/trade/sale/dto';

export class AccountingJournalingDto {
  transactions: any[];
  type: journalTypes;
  by_user: Types.ObjectId;
  branch: Types.ObjectId;
  note: string;
  reference: string;
  transaction_date: string;
  document?: Types.ObjectId;
  document_code?: number;
  related_journal_id?: Types.ObjectId;
  _id?;
}
