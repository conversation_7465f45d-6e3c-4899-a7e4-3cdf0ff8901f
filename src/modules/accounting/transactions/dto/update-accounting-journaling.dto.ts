import { Types } from 'mongoose';
import { IsEnum, IsNotEmpty, IsObject, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';
import { RpcDto } from '../../../../utils/dto/rpc.dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class UpdateAccountingJournalingDto extends RpcDto {
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id: Types.ObjectId;

  @IsNotEmpty()
  @IsEnum(journalTypes)
  type: journalTypes;

  @IsObject()
  @IsOptional()
  updated_transactions?: any[];
}
