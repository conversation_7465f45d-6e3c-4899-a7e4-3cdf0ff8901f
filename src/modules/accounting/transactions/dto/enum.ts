export enum stepStatus {
  init = 'initiated',
  done = 'done',
  roll_backed = 'roll_backed',
  failed_before_creation = 'failed_before_creation',
}

export enum status {
  init = 'initiated',
  done = 'done',
  failed = 'failed',
  roll_backed = 'roll_backed',
}

export enum steps {
  erp_document = 'ERP_document_creation',
  sales_invoice = 'sales_invoice',
  purchase_invoice = 'purchase_invoice',
  accounting_journaling = 'accounting_journaling',
  update_accounting_journaling = 'update_accounting_journaling',
  undo_update_accounting_journaling = 'undo_update_accounting_journaling',
  delete_accounting_journaling = 'delete_accounting_journaling',
  credit_debit_memo_voucher = 'credit_debit_memo_voucher',
  credit_debit_memo_voucher_journaling = 'credit_debit_memo_voucher_journaling',
}

export enum documentType {
  general_journal = 'general_journal',
  voucher = 'voucher',
}

export enum action {
  create = 'create',
  delete = 'delete',
  edit = 'edit',
}
