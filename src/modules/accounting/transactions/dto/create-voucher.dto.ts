import { PartnerType } from '../../reports/enums/partner-type.enum';
import { Types } from 'mongoose';

export enum voucherType {
  receipt = 'receipt',
  payment = 'payment',
  debit_memo = 'debit_memo',
  credit_memo = 'credit_memo',
}

export class CreateVoucherDto {
  code: number;
  type: voucherType;
  payment_type: string;
  amount: number;
  branch: Types.ObjectId;
  description: string;
  discount: number;
  invoice_party_type: PartnerType;
  transaction_date: string;
  invoice: any;
  settlement_type: string;
  debit_account: any;
  credit_account: any;
  note: string;
  reference: string;
  journal?: Types.ObjectId;
  auto_generated: boolean;
  policyData?: any;
}
