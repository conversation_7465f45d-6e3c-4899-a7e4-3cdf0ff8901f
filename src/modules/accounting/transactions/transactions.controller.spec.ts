import { Test, TestingModule } from '@nestjs/testing';
import { TransactionsController } from './transactions.controller';
import { TransactionsService } from './transactions.service';
import { getModelToken } from '@nestjs/mongoose';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { AccountingRpcService } from '../../rpc/accounting-rpc.service';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';

describe('TransactionsController', () => {
  let controller: TransactionsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TransactionsController],
      providers: [
        TransactionsService,
        {
          provide: getModelToken('transactions'),
          useValue: {},
        },
        {
          provide: TradeRpcService,
          useValue: {
            emit: jest.fn(),
          },
        },
        {
          provide: AccountingRpcService,
          useValue: {
            emit: jest.fn(),
          },
        },
        {
          provide: GeneralLedgerService,
          useValue: {
            emit: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<TransactionsController>(TransactionsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
