import { Test, TestingModule } from '@nestjs/testing';
import { TransactionsService } from './transactions.service';
import { getModelToken } from '@nestjs/mongoose';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { AccountingRpcService } from '../../rpc/accounting-rpc.service';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';

describe('TransactionsService', () => {
  let service: TransactionsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TransactionsService,
        {
          provide: getModelToken('transactions'),
          useValue: {},
        },
        {
          provide: TradeRpcService,
          useValue: {
            emit: jest.fn(),
          },
        },
        {
          provide: AccountingRpcService,
          useValue: {
            emit: jest.fn(),
          },
        },
        {
          provide: GeneralLedgerService,
          useValue: {
            emit: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<TransactionsService>(TransactionsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
