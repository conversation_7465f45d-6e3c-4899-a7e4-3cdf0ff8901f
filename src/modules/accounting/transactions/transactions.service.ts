import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { AccountingTransaction } from './schema/transaction.schema';
import { AccountingJournalingDto } from './dto/accounting-journaling.dto';
import {
  stepStatus,
  steps,
  documentType,
  CreateVoucherDto,
  action,
  status,
} from './dto';
import { InvoiceDto } from './dto/invoice.dto';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { AccountingRpcService } from '../../rpc/accounting-rpc.service';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { UpdateAccountingJournalingDto } from './dto/update-accounting-journaling.dto';
import { accountingMessagePattern } from '../../../utils/queues.enum';

let correlationId;

@Injectable()
export class TransactionsService {
  constructor(
    @InjectModel('transactions')
    private readonly transactionModel: Model<AccountingTransaction>,
    private readonly tradeRpcService: TradeRpcService,
    private readonly accountingRpcService: AccountingRpcService,

    private readonly generalLedgerService: GeneralLedgerService,
  ) {}

  async executeERPDocumentSaga(
    branch: Types.ObjectId,
    type: documentType,
    action: action,
    requiredSteps: Array<steps>,
    notRequiredSteps?: Array<steps>,
    store?: Types.ObjectId,
  ) {
    const transactions = requiredSteps.map((step) => ({
      step,
      status: stepStatus.init,
      requirement: 1,
    }));
    if (notRequiredSteps) {
      transactions.push(
        ...notRequiredSteps.map((step) => ({
          step,
          status: stepStatus.init,
          requirement: 0,
        })),
      );
    }
    const creationSteps = {
      transactions,
      type,
      branch,
      action,
      store,
    };
    const newTransaction = new this.transactionModel(creationSteps);
    correlationId = (await newTransaction.save())?._id;
    return correlationId;
  }

  async createdERPDocument(correlationId: string, data: any) {
    return await this.updateStatus(
      correlationId,
      steps.erp_document,
      stepStatus.done,
      data,
    );
  }

  async salesInvoice(invoiceDto: InvoiceDto) {
    const res = await this.tradeRpcService.updateSale(invoiceDto);

    const data = {
      condition: res._id,
      invoice: invoiceDto.invoice,
      reduce: invoiceDto.reduce,
    };

    await this.updateStatus(
      correlationId,
      steps.sales_invoice,
      stepStatus.done,
      data,
    );
    return res;
  }

  async purchaseInvoice(invoiceDto: InvoiceDto) {
    const res = await this.tradeRpcService.updatePurchase(invoiceDto);

    const data = {
      condition: res._id,
      invoice: invoiceDto.invoice,
      reduce: invoiceDto.reduce,
    };

    await this.updateStatus(
      correlationId,
      steps.purchase_invoice,
      stepStatus.done,
      data,
    );
    return res;
  }

  async journalAccounting(
    accountingJournalingDto: AccountingJournalingDto,
    code: number,
  ) {
    const res = await this.accountingRpcService.createJournal(
      accountingJournalingDto,
      code,
    );

    const data = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      accountingId: res._id,
      branch: accountingJournalingDto.branch,
    };
    await this.updateStatus(
      correlationId,
      steps.accounting_journaling,
      stepStatus.done,
      data,
    );
    return res;
  }

  async transactionCompleted(correlationId: string) {
    return await this.transactionModel.findOneAndUpdate(
      { _id: correlationId },
      { $set: { status: status.done } },
    );
  }

  async updateAccountingJournalAccounting(
    updateAccountingJournalingDto: UpdateAccountingJournalingDto,
    request: RequestWithUser,
    oldJournal?: any[],
  ) {
    const branch = new Types.ObjectId(request.headers['branch']);

    const result = await this.generalLedgerService.applyAdjustmentOrOverride(
      {
        _id: updateAccountingJournalingDto._id,
        type: updateAccountingJournalingDto.type,
      },
      updateAccountingJournalingDto.updated_transactions,
      request.policyData,
      request.user.user_id,
      request.user.code,
      branch,
    );

    const data = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      accountingIds: [new Types.ObjectId(result._id), ...oldJournal],
      branch: branch,
    };

    await this.updateStatus(
      correlationId,
      steps.update_accounting_journaling,
      stepStatus.done,
      data,
    );
    return result;
  }

  async voucherJournalAccounting(
    accountingJournalingDto: AccountingJournalingDto,
    code: number,
  ) {
    const res = await this.accountingRpcService.createJournal(
      accountingJournalingDto,
      code,
    );

    const data = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      memoJournalId: res._id,
      branch: accountingJournalingDto.branch,
    };

    await this.updateStatus(
      correlationId,
      steps.credit_debit_memo_voucher_journaling,
      stepStatus.done,
      data,
    );
    return res;
  }

  async creditMemoVoucher(
    createVoucherDto: CreateVoucherDto,
    originalMemo?: any,
  ) {
    const res = await this.accountingRpcService.createMemo(createVoucherDto);

    const data = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      createdMemoId: res._id,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      originalMemo,
    };

    await this.updateStatus(
      correlationId,
      steps.credit_debit_memo_voucher,
      stepStatus.done,
      data,
    );
    return res;
  }

  async createVoucher(
    createVoucherDto: CreateVoucherDto,
    messagePattern: string,
  ) {
    const res = await this.accountingRpcService.createVoucher(
      createVoucherDto,
      messagePattern,
    );
    await this.updateStatus(
      correlationId,
      steps.credit_debit_memo_voucher,
      stepStatus.done,
    );
    return res;
  }

  private async updateStatus(
    correlationId: string,
    step: string,
    status: stepStatus,
    data?: object,
  ) {
    return await this.transactionModel.findOneAndUpdate(
      {
        _id: correlationId,
        'transactions.step': step,
      },
      {
        $set: { 'transactions.$.status': status, 'transactions.$.data': data },
      },
    );
  }

  async handleFailure(code: number, correlationId, error?) {
    Logger.log(`failed transaction ${correlationId} with error:`, error);
    const document = await this.transactionModel.findOne({
      _id: correlationId,
    });
    const rollbackRetry = document.rollback_retry || 0;
    await this.transactionModel.findOneAndUpdate(
      { _id: correlationId },
      {
        $set: {
          status: status.failed,
          rollback_retry: rollbackRetry + 1,
          creation_error: JSON.stringify(error),
        },
      },
    );

    for (const step of document.transactions) {
      const rollbackStep = this.rollbackActions[step.step];

      if (!rollbackStep) {
        Logger.log(`No rollback action found for step: ${step.step}`);
        continue;
      }

      if (step.status === stepStatus.done) {
        await rollbackStep.action(code, correlationId, {
          ...step.data,
          document,
        });
      } else {
        await rollbackStep.fallback(correlationId);
      }
    }
    await this.transactionModel.findOneAndUpdate(
      { _id: correlationId },
      { $set: { status: status.roll_backed } },
    );
  }

  rollbackActions: Record<
    string,
    {
      action: (code: number, correlationId: string, data: any) => Promise<void>;
      fallback: (correlationId: string) => Promise<void>;
    }
  > = {
    [steps.erp_document]: {
      action: async (
        code,
        correlationId,
        { ERPDocumentId, ERPDocumentPattern, ERPDocument, document },
      ) => {
        Logger.log(`Rolling back ERP document for invoiceId: ${ERPDocumentId}`);
        this.accountingRpcService.rollBackAction(
          ERPDocumentPattern,
          code,
          ERPDocumentId,
          document.action,
          ERPDocument,
        );

        await this.updateStatus(
          correlationId,
          steps.erp_document,
          stepStatus.roll_backed,
        );
      },
      fallback: async (correlationId) => {
        Logger.log(
          `ERP rollback failed before creation for invoiceId: ${correlationId}`,
        );
        await this.updateStatus(
          correlationId,
          steps.erp_document,
          stepStatus.failed_before_creation,
        );
      },
    },
    [steps.accounting_journaling]: {
      action: async (
        code,
        correlationId,
        { accountingId, branch, accounting_doc },
      ) => {
        Logger.log(
          `Rolling back accounting journal for invoiceId: ${correlationId}`,
        );

        if (accounting_doc) {
          await this.accountingRpcService.updateJournal(
            code,
            accountingId,
            accounting_doc,
            branch,
          );
        } else {
          await this.accountingRpcService.rollbackJournal(
            code,
            branch,
            accountingId,
          );
        }
        await this.updateStatus(
          correlationId,
          steps.accounting_journaling,
          stepStatus.roll_backed,
        );
        Logger.log(`Rolled back accounting journal`);
      },
      fallback: async (correlationId) => {
        Logger.log(
          `Accounting journaling rollback failed before creation for invoiceId: ${correlationId}`,
        );
        await this.updateStatus(
          correlationId,
          steps.accounting_journaling,
          stepStatus.failed_before_creation,
        );
      },
    },
    [steps.update_accounting_journaling]: {
      action: async (code, correlationId, { accountingIds, branch }) => {
        Logger.log(
          `Rolling back accounting journal for invoiceId: ${correlationId}`,
        );
        if (accountingIds.length > 0) {
          await this.accountingRpcService.deleteAccountingJournalsByIds(
            code,
            accountingIds,
            branch,
          );
        }
        await this.updateStatus(
          correlationId,
          steps.update_accounting_journaling,
          stepStatus.roll_backed,
        );

        Logger.log(
          `Rolled back update accounting journal for : ${correlationId}`,
        );
      },
      fallback: async (correlationId) => {
        Logger.log(
          `update Accounting journaling rollback failed before creation for invoiceId: ${correlationId}`,
        );
        await this.updateStatus(
          correlationId,
          steps.accounting_journaling,
          stepStatus.failed_before_creation,
        );
      },
    },
    [steps.sales_invoice]: {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      action: async (code, correlationId, { document, ...data }) => {
        Logger.log(`Rolling back sale invoice: ${correlationId}`);

        await this.tradeRpcService.updateSale({
          code,
          invoice: data.invoice,
          reduce: !data.reduce,
        });

        await this.updateStatus(
          correlationId,
          steps.sales_invoice,
          stepStatus.roll_backed,
        );
        Logger.log(`Rolled back sale invoice`);
      },
      fallback: async (correlationId) => {
        Logger.log(
          `sale invoice rollback failed before creation for invoiceId: ${correlationId}`,
        );
        await this.updateStatus(
          correlationId,
          steps.sales_invoice,
          stepStatus.failed_before_creation,
        );
      },
    },
    [steps.purchase_invoice]: {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      action: async (code, correlationId, { document, ...data }) => {
        Logger.log(`Rolling back purchase invoice: ${correlationId}`);

        await this.tradeRpcService.updatePurchase({
          code,
          invoice: data.invoice,
          reduce: !data.reduce,
        });

        await this.updateStatus(
          correlationId,
          steps.purchase_invoice,
          stepStatus.roll_backed,
        );
        Logger.log(`Rolled back purchase invoice`);
      },
      fallback: async (correlationId) => {
        Logger.log(
          `purchase invoice rollback failed before creation for invoiceId: ${correlationId}`,
        );
        await this.updateStatus(
          correlationId,
          steps.purchase_invoice,
          stepStatus.failed_before_creation,
        );
      },
    },
    [steps.credit_debit_memo_voucher_journaling]: {
      action: async (code, correlationId, { memoJournalId, branch }) => {
        Logger.log(
          `Rolling back credit debit memo accounting journaling for invoiceId: ${correlationId}`,
        );
        this.accountingRpcService.rollbackJournal(code, branch, memoJournalId);

        await this.updateStatus(
          correlationId,
          steps.credit_debit_memo_voucher_journaling,
          stepStatus.roll_backed,
        );
        Logger.log(`Rolled back  credit debit memo accounting journaling`);
      },
      fallback: async (correlationId) => {
        Logger.log(
          `credit debit memo Accounting journaling rollback failed before creation for invoiceId: ${correlationId}`,
        );
        await this.updateStatus(
          correlationId,
          steps.credit_debit_memo_voucher_journaling,
          stepStatus.failed_before_creation,
        );
      },
    },
    [steps.credit_debit_memo_voucher]: {
      action: async (code, correlationId, { createdMemoId, originalMemo }) => {
        Logger.log(`Rolling back  credit debit memo for : ${correlationId}`);
        await this.accountingRpcService.rollbackVoucher(
          code,
          createdMemoId,
          accountingMessagePattern.rollback_memo_voucher,
        );
        if (originalMemo) {
          await this.accountingRpcService.createMemo({ code, ...originalMemo });
        }

        await this.updateStatus(
          correlationId,
          steps.credit_debit_memo_voucher,
          stepStatus.roll_backed,
        );
        Logger.log(`Rolled back  credit debit memo`);
      },
      fallback: async (correlationId) => {
        Logger.log(
          ` credit debit memo rollback failed before creation for invoiceId: ${correlationId}`,
        );
        await this.updateStatus(
          correlationId,
          steps.credit_debit_memo_voucher,
          stepStatus.failed_before_creation,
        );
      },
    },
    [steps.delete_accounting_journaling]: {
      action: async (code, correlationId, { deletedLedgerData }) => {
        Logger.log(`Rolling back accounting journal for : ${correlationId}`);

        await this.accountingRpcService.createJournal(deletedLedgerData, code);

        await this.updateStatus(
          correlationId,
          steps.delete_accounting_journaling,
          stepStatus.roll_backed,
        );
        Logger.log(`Rolled back  accounting journaling`);
      },
      fallback: async (correlationId) => {
        Logger.log(
          `Accounting journaling rollback failed before creation for : ${correlationId}`,
        );
        await this.updateStatus(
          correlationId,
          steps.delete_accounting_journaling,
          stepStatus.failed_before_creation,
        );
      },
    },
  };

  async handleFailures(code: number, correlationId?) {
    let query: any = {
      $or: [{ status: status.init }, { status: status.failed }],
    };

    if (correlationId) {
      query = {
        ...query,
        _id: correlationId,
      };
    }

    const failedTransactions = await this.transactionModel
      .find(query)
      .select('_id')
      .exec();

    for (const transaction of failedTransactions) {
      await this.handleFailure(code, transaction._id.toString());
    }
  }
}
