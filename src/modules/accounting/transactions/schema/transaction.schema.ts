import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { TransactionItem } from './transaction-items.schema';
import { action, status } from '../dto';
import { HydratedDocument, Types } from 'mongoose';
import { tempdocumentType } from '../../../../modules/trade/transactions/dto/enum';

export type TransactionDocument = HydratedDocument<AccountingTransaction>;

@Schema({ timestamps: true })
export class AccountingTransaction {
  @Prop({ required: false })
  store?: Types.ObjectId;

  @Prop({
    required: true,
  })
  branch: Types.ObjectId;

  @Prop({
    required: true,
    enum: tempdocumentType,
  })
  type: tempdocumentType;

  @Prop({
    required: true,
    enum: action,
  })
  action: action;

  @Prop({ type: Array<TransactionItem>, default: () => ({}) })
  transactions: TransactionItem[];

  @Prop({
    required: true,
    enum: status,
    default: status.init,
  })
  status: status;

  @Prop({
    required: false,
    default: 0,
  })
  rollback_retry: number;

  @Prop()
  creation_error: string;

  @Prop()
  rollback_error: string;
}
export const transactionSchema = SchemaFactory.createForClass(
  AccountingTransaction,
);
