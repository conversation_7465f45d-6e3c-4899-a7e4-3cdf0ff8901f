import { Global, Module } from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import { TransactionsController } from './transactions.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { transactionSchema } from './schema/transaction.schema';
import { GeneralLedgerModule } from '../general-ledger/general-ledger.module';
import { RpcModule } from '../../rpc/rpc.module';

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'transactions', schema: transactionSchema },
    ]),
    RpcModule,
    GeneralLedgerModule,
  ],
  controllers: [TransactionsController],
  providers: [TransactionsService],
  exports: [TransactionsService],
})
export class TransactionsModule {}
