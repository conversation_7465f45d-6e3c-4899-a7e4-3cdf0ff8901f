import { Body, Controller, Post, Req } from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { publicRpc } from '../../auth/guards/public-event.decorator';

import {
  HandleFailureDto,
  HandleFailureRPCDto,
} from './dto/handle-failure.dto';
import { accountingMessagePattern } from '../../../utils/queues.enum';

@Controller('transactions')
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}

  @Post('/handle-failure')
  async handleFailures(
    @Req() req: RequestWithUser,
    @Body() handleFailureDto: HandleFailureDto,
  ): Promise<any> {
    await this.transactionsService.handleFailures(
      req.user?.code,
      handleFailureDto.correlationId,
    );
  }

  @publicRpc()
  @MessagePattern({
    cmd: accountingMessagePattern.check_handle_failures,
  })
  async getItemPrice(@Payload() payload: HandleFailureRPCDto) {
    await this.transactionsService.handleFailures(payload.code);
  }
}
