import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsDate,
  IsOptional,
  IsString,
  IsArray,
  IsInt,
  Min,
  IsNumber,
  IsNotEmpty,
} from 'class-validator';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class GetGeneralJournalDto {
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public from_date?: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public to_date?: string;

  @ApiProperty({
    required: false,
  })
  @IsString()
  @IsOptional()
  public code?: string;

  @ApiProperty({
    required: false,
  })
  @IsArray()
  @IsOptional()
  public codes?: string[];

  @ApiProperty({
    required: false,
  })
  @IsArray()
  @IsOptional()
  public nodeIds?: Types.ObjectId[];

  @IsString()
  @IsOptional()
  public queries?: string;

  @ApiProperty({
    description: 'brnach of journal',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public limit?: number;

  @ApiProperty({
    description: 'from number',
    example: 0,
  })
  @Type(() => Number)
  @IsString()
  @IsOptional()
  from_number?: number;

  @ApiProperty({
    description: 'to number',
    example: 0,
  })
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  to_number?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  number?: number;
}
