import { Types } from 'mongoose';
import { IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

import { RpcDto } from '../../../../utils/dto/rpc.dto';
export class RollbackGeneralJournalRpcDto extends RpcDto {
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id?: Types.ObjectId;
}
