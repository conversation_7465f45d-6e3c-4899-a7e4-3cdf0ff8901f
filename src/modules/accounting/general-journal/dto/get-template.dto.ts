import { ApiProperty } from '@nestjs/swagger';
import { PrintOptionsDto } from '../../voucher/dto/print-options.dto';
import { Transform } from 'class-transformer';

import { Types } from 'mongoose';
import { IsNotEmpty } from 'class-validator';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class GetJounalTemplateDto extends PrintOptionsDto {
  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  journalId: Types.ObjectId;
}
