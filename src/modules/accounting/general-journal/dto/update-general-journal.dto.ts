import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsOptional,
  ValidateNested,
  IsNotEmpty,
  IsString,
  IsEnum,
} from 'class-validator';

import { IGeneralTransaction } from './transaction.dto';
import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class UpdateGeneralJournalDto {
  @ApiProperty({
    description: 'transactions of journal',
    example: [
      {
        credit: 100,
        debit: 0,
        description: 'this is test comment',
        accountingNode: '63400372c582a633284bf24c',
        by_user: '63400372c582a633284bf24c',
      },
      {
        credit: 0,
        debit: 100,
        description: 'this is test comment',
        accountingNode: '63400372c582a633284bf24c',
        by_user: '63400372c582a633284bf24c',
      },
    ],
  })
  @ValidateNested()
  @IsNotEmpty()
  @Type(() => IGeneralTransaction)
  transactions?: IGeneralTransaction[];

  @ApiProperty({
    description: 'type of journal',
    enum: journalTypes,
    isArray: false,
    example: journalTypes.general,
    required: true,
  })
  @ApiProperty({
    description: 'note journal',
    example: 'test note',
  })
  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsOptional()
  reference?: string;

  @ApiHideProperty()
  _id?: Types.ObjectId;
  createdAt;
  transaction_date;

  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  by_user?: Types.ObjectId;

  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  document?: Types.ObjectId;

  @ApiHideProperty()
  @IsOptional()
  document_code?: number;

  @ApiProperty({
    description: 'type of journal',
    enum: journalTypes,
    isArray: false,
    example: journalTypes.general,
    required: true,
  })
  @IsEnum(journalTypes)
  @IsOptional()
  type?: journalTypes;
}
