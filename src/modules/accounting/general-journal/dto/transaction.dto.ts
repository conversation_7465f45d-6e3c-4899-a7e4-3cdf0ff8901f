import { <PERSON>N<PERSON><PERSON> } from '@nestjs/class-validator';
import { Types } from 'mongoose';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Prop } from '@nestjs/mongoose';
import { AccountingNode } from '../../accounting-node/schema/accounting-node.schema';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class IGeneralTransaction {
  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'code of transaction',
    example: '0',
    required: true,
  })
  code?: string;

  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  @ApiProperty({
    description: 'credit of transaction',
    example: 1223,
    required: false,
  })
  credit: number;

  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  @ApiProperty({
    description: 'debit of transaction',
    example: 1223,
    required: false,
  })
  debit: number;

  @ApiProperty({
    description: 'Description of transaction',
    example: 'commands',
    required: false,
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    description: 'accountingNode of transaction',
    example: '63400372c582a633284bf24c',
    required: true,
    type: 'string',
  })
  @Prop({ type: Types.ObjectId, ref: AccountingNode.name })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  accountingNode: Types.ObjectId;

  @ApiProperty({ type: String })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  partner_id?: Types.ObjectId;

  @IsOptional()
  @IsString()
  @ApiProperty({ type: String, example: 'vendor' })
  partner_type?: string;

  @ApiHideProperty()
  by_user?: Types.ObjectId;

  @ApiHideProperty()
  created_at?: any;
}
