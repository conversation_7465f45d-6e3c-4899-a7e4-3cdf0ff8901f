import { IsDate, ValidateNested } from '@nestjs/class-validator';
import { Transform, Type } from 'class-transformer';
import { Types } from 'mongoose';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { IGeneralTransaction } from './transaction.dto';
import { PrintOptionsDto } from '../../voucher/dto/print-options.dto';
import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class CreateGeneralJournalDto {
  @IsOptional()
  @IsNumber()
  number?: number;

  @ValidateNested()
  @IsNotEmpty()
  @Type(() => IGeneralTransaction)
  transactions?: IGeneralTransaction[];

  @ApiProperty({
    description: 'type of journal',
    enum: journalTypes,
    isArray: false,
    example: journalTypes.general,
    required: true,
  })
  @IsEnum(journalTypes)
  type: journalTypes;

  @ApiProperty({
    description: 'userid who made of journal',
    example: '63400372c582a633284bf24c',
    required: true,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  by_user?: Types.ObjectId;

  @ApiProperty({
    description: 'brnach of journal',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;

  @ApiProperty({
    description: 'note journal',
    example: 'test note',
  })
  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsOptional()
  reference?: string;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  @ApiProperty({
    description: 'date journal',
    example: new Date(),
    required: false,
  })
  transaction_date?: Date;

  @ApiHideProperty()
  _id?: Types.ObjectId;

  @ApiProperty()
  @ValidateNested()
  @Type(() => PrintOptionsDto)
  printOptions: PrintOptionsDto;

  @ApiHideProperty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  document?: Types.ObjectId;

  @ApiHideProperty()
  @IsOptional()
  document_code?: number;
}
