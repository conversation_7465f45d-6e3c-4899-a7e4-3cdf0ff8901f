import { Test, TestingModule } from '@nestjs/testing';
import { GeneralJournalService } from './general-journal.service';
import { AccountingNodeService } from '../accounting-node/accounting-node.service';
import { AutoNumberingService } from '../auto-numbering/auto-numbering.service';
import { getModelToken } from '@nestjs/mongoose';
import { GeneralJournal } from './schema/general-journal.schema';
import { REQUEST } from '@nestjs/core';
import { TransactionsService } from '../transactions/transactions.service';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { notificationClientMock } from '../../__mocks__/services/notification-rpc.service';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';

describe('GeneralJournalService', () => {
  let service: GeneralJournalService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GeneralJournalService,
        { provide: getModelToken(GeneralJournal.name), useValue: {} },
        { provide: UserRpcService, useValue: {} },
        { provide: TradeRpcService, useValue: {} },
        { provide: REQUEST, useValue: { user: { user_id: 'testUserId' } } },
        { provide: GeneralLedgerService, useValue: {} },
        { provide: AccountingNodeService, useValue: {} },
        { provide: AutoNumberingService, useValue: {} },
        { provide: TransactionsService, useValue: {} },
        { provide: NotificationRpcService, useValue: notificationClientMock() },
        { provide: AccountingNodeProxyPatternService, useValue: {} },
      ],
    }).compile();

    service = await module.resolve<GeneralJournalService>(
      GeneralJournalService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
