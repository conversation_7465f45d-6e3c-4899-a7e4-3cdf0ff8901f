import {
  Controller,
  Post,
  Body,
  Req,
  HttpException,
  UseGuards,
  Get,
  Query,
  Param,
  Patch,
  Delete,
  UseInterceptors,
} from '@nestjs/common';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { <PERSON><PERSON><PERSON><PERSON>er, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';
import { CreateGeneralJournalDto } from './dto/create-general-journal.dto';
import { GeneralJournalService } from './general-journal.service';
import { GeneralJournal } from './schema/general-journal.schema';
import { journalTypes } from '../general-ledger/schema/general-ledger.schema';
import { GetGeneralJournalDto } from './dto/get-general-journal.dto';

import { UpdateGeneralJournalDto } from './dto/update-general-journal.dto';
import { GetJounalTemplateDto } from './dto/get-template.dto';

import { publicRpc } from '../../auth/guards/public-event.decorator';
import { EventPattern, Payload } from '@nestjs/microservices';

import { PolicyInterceptor } from '../../policy/policy.interceptor';
import { serviceName } from '../../policy/service-name.decorator';
import { GeneralJournalResults } from './dto/response.type';
import { RollbackGeneralJournalRpcDto } from './dto/rollback-general-journal-rpc.dto';
import { FeatureAccessibilityGuard } from '../../casl/guards/feature-accessibility.guard';
import { feature } from '../../casl/guards/feature.decorator';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';
import { RouteRules } from '../../../interceptor/routes-rule.interceptor';
import { accountingMessagePattern } from '../../../utils/queues.enum';

@ApiTags('GeneralJournal')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('general-journal')
export class GeneralJournalController {
  constructor(private readonly generalJournalService: GeneralJournalService) {}

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'journal' })
  @UseInterceptors(PolicyInterceptor)
  @serviceName(['journal'])
  @abilities({ action: 'create', subject: 'journal' })
  @Post()
  async create(
    @Req() request: RequestWithUser,
    @Body() createGeneralJournalDto: CreateGeneralJournalDto,
  ): Promise<GeneralJournal> {
    createGeneralJournalDto.type = journalTypes.general;
    createGeneralJournalDto.by_user = request.user.user_id;
    return this.generalJournalService.create(createGeneralJournalDto, request);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'journal' })
  @abilities({ action: 'list', subject: 'journal' })
  @Get()
  findAll(
    @Query() query: GetGeneralJournalDto,
  ): Promise<GeneralJournalResults> {
    return this.generalJournalService.findAll(query, undefined, undefined);
  }

  @ApiOkResponse({
    type: GeneralJournal,
    isArray: false,
  })
  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'journal' })
  @abilities({ action: 'read', subject: 'journal' })
  @Get(':_id')
  async findOne(
    @Param('_id') _id: string,
    @Req() req: RequestWithUser,
  ): Promise<GeneralJournal> {
    const rs = await this.generalJournalService.findOne(
      {
        _id,
        type: journalTypes.general,
      },
      req,
    );
    if (!rs)
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotFound),
        erpExceptionCode.journalNotFound,
      );
    return rs;
  }

  @ApiOkResponse({
    type: GeneralJournal,
    isArray: false,
  })
  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'journal' })
  @abilities({ action: 'read', subject: 'journal' })
  @Post('print')
  async printOne(
    @Body() getTemplateDto: GetJounalTemplateDto,
    @Req() req: RequestWithUser,
  ): Promise<GeneralJournal> {
    return await this.generalJournalService.printOne(getTemplateDto, req);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'journal' })
  @abilities({ action: 'edit', subject: 'journal' })
  @UseInterceptors(RouteRules, PolicyInterceptor)
  @serviceName([
    'allow_documents_edition',
    'document_policy',
    'document_policy_status',
  ])
  @Patch(':_id')
  update(
    @Param('_id') _id: string,
    @Body() updateGeneralJournalDto: UpdateGeneralJournalDto,
    @Req() req: RequestWithUser,
  ) {
    return this.generalJournalService.update(_id, updateGeneralJournalDto, req);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'journal' })
  @abilities({ action: 'delete', subject: 'journal' })
  @UseInterceptors(RouteRules, PolicyInterceptor)
  @serviceName([
    'allow_documents_deletion',
    'document_policy',
    'document_policy_status',
  ])
  @Delete(':_id')
  remove(@Param('_id') _id: string, @Req() request: RequestWithUser) {
    return this.generalJournalService.remove(_id, request);
  }

  @publicRpc()
  @EventPattern({
    cmd: accountingMessagePattern.rollback_general_journal,
  })
  async deleteById(@Payload() payload: RollbackGeneralJournalRpcDto) {
    return this.generalJournalService.removeDoc(payload?._id);
  }

  @publicRpc()
  @EventPattern({
    cmd: accountingMessagePattern.rollback_general_journal_action,
  })
  async rollbackGeneralJournal(@Payload() payload) {
    return this.generalJournalService.rollback(
      payload._id,
      payload.action,
      payload.erp_document,
    );
  }
}
