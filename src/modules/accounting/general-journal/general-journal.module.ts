import { Module } from '@nestjs/common';
import { GeneralJournalService } from './general-journal.service';
import {
  GeneralJournal,
  generalJournalSchema,
} from './schema/general-journal.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { GeneralLedgerModule } from '../general-ledger/general-ledger.module';
import { GeneralJournalController } from './general-journal.controller';
import { AccountingNodeModule } from '../accounting-node/accounting-node.module';
import { AutoNumberingModule } from '../auto-numbering/auto-numbering.module';
import { ProxyPatternModule } from '../proxy-pattern/proxy-pattern.module';
import { RpcModule } from '../../rpc/rpc.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { GeneralJournalModel } from './model/general-journal.model';
import { GeneralJournalTransactionModel } from './model/general-journal-transaction.model';

@Module({
  imports: [
    AccountingNodeModule,
    AutoNumberingModule,
    ProxyPatternModule,
    SequelizeModule.forFeature([
      GeneralJournalModel,
      GeneralJournalTransactionModel,
    ]),
    MongooseModule.forFeature([
      { name: GeneralJournal.name, schema: generalJournalSchema },
    ]),
    GeneralLedgerModule,
    RpcModule,
  ],
  controllers: [GeneralJournalController],
  providers: [GeneralJournalService],
  exports: [GeneralJournalService],
})
export class GeneralJournalModule {}
