import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { ITransaction } from '../../general-ledger/dto/transaction.dto';
import { journalTypes } from '../../general-ledger/schema/general-ledger.schema';
import {
  SoftDeleteDocument,
  softDeletePlugin,
} from '../../../../utils/schemas';

export type GeneralJournalDocument = HydratedDocument<GeneralJournal> &
  SoftDeleteDocument;

@Schema({ timestamps: true })
export class GeneralJournal {
  @Prop({ type: Number, unique: true, sparse: true })
  number: number;

  @Prop()
  transactions: ITransaction[];

  @Prop({ type: String, enum: journalTypes })
  type: journalTypes;

  @Prop()
  note: string;

  @Prop({ type: Types.ObjectId })
  by_user: Types.ObjectId;

  @Prop()
  reference: string;

  @Prop({ type: Date, default: Date.now() })
  transaction_date: Date;

  @Prop({ type: Types.ObjectId })
  Inventory_journal: Types.ObjectId;

  @Prop({ type: Types.ObjectId })
  accounting_journal: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true })
  branch: Types.ObjectId;

  createdAt?: Date;
}

export const generalJournalSchema =
  SchemaFactory.createForClass(GeneralJournal);
generalJournalSchema.index({ number: 1, branch: 1 }, { unique: true });

generalJournalSchema.plugin(softDeletePlugin);
