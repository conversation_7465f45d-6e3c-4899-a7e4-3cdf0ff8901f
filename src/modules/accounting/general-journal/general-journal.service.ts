import { HttpException, Inject, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  GeneralJournal,
  GeneralJournalDocument,
} from './schema/general-journal.schema';
import { Types } from 'mongoose';

import { REQUEST } from '@nestjs/core';
import { CreateGeneralJournalDto } from './dto/create-general-journal.dto';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { GetGeneralLedgerDto } from '../general-ledger/dto/get-general-ledger.dto';

import { GetGeneralJournalDto } from './dto/get-general-journal.dto';
import { journalTypes } from '../general-ledger/schema/general-ledger.schema';
import { UpdateGeneralJournalDto } from './dto/update-general-journal.dto';
import { extend } from 'lodash';
import { IGeneralTransaction } from './dto/transaction.dto';

import { AutoNumberingService } from '../auto-numbering/auto-numbering.service';

import { GetJounalTemplateDto } from './dto/get-template.dto';
import {
  PrintOptionsDto,
  templateType,
} from '../voucher/dto/print-options.dto';

import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { TransactionsService } from '../transactions/transactions.service';
import { action, documentType, steps } from '../transactions/dto';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { GeneralJournalResults } from './dto/response.type';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import * as _ from 'lodash';
import { documentPolicyEnum } from '../../policy/enum/document-policy.enum';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';
import { accountingMessagePattern } from '../../../utils/queues.enum';
import { Calculator } from '../../../utils/calculator';
import { paginate } from '../../../utils/dto';
import { handleGlobalEditRestriction } from '../../../utils/global-edit-restriction';
import { SoftDeleteModel } from '../../../utils/schemas';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class GeneralJournalService {
  constructor(
    @InjectModel(GeneralJournal.name)
    private generalJournalModel: SoftDeleteModel<GeneralJournalDocument>,
    private readonly userRpcService: UserRpcService,
    private readonly tradeRpcService: TradeRpcService,
    @Inject(REQUEST) private readonly request: any,
    public readonly notificationRpcService: NotificationRpcService,

    private readonly accountingNodeProxyPatternService: AccountingNodeProxyPatternService,
    @Inject(AutoNumberingService)
    private readonly autoNumberingService: AutoNumberingService,
    private readonly transactionsService: TransactionsService,
    private readonly generalLedgerService: GeneralLedgerService,
  ) {}

  async create(
    generalJournals: CreateGeneralJournalDto,
    request: RequestWithUser,
  ): Promise<any> {
    if (
      generalJournals.transaction_date === undefined ||
      generalJournals.transaction_date === null
    ) {
      generalJournals.transaction_date = new Date();
    }
    const journal = request.policyData.journal;
    const correlationId = await this.transactionsService.executeERPDocumentSaga(
      new Types.ObjectId(request.headers['branch']),
      documentType.general_journal,
      action.create,
      [steps.erp_document, steps.accounting_journaling],
    );
    let generalJournal;
    let documentId;
    try {
      const rest = generalJournals;
      if (journal.auto_serial == true) {
        if (journal.same_serial == true) {
          generalJournals.number =
            await this.autoNumberingService.getNextSequence(
              this.generalJournalModel,
              ['journal.cash_serial', 'journal.credit_serial'],
              generalJournals.branch,
            );
        } else {
          generalJournals.number =
            await this.autoNumberingService.getNextSequence(
              this.generalJournalModel,
              ['journal.cash_serial'],
              generalJournals.branch,
            );
        }
      } else {
        if (!generalJournals.number) {
          throw new HttpException(
            nameOf(erpExceptionCode, (x) => x.numberNotExist),
            erpExceptionCode.numberNotExist,
          );
        }

        const numberExist = await this.generalJournalModel.findOne({
          number: generalJournals.number,
        });
        if (numberExist) {
          throw new HttpException(
            nameOf(erpExceptionCode, (x) => x.numberDuplicated),
            erpExceptionCode.numberDuplicated,
          );
        }
        await this.autoNumberingService.updateSequence(
          generalJournals.number,
          'journal.cash_serial',
          generalJournals.branch,
        );
      }
      const preparedJournal = await this.prepareJournal(rest);
      generalJournal = await new this.generalJournalModel({
        ...preparedJournal,
        number: generalJournals.number,
      }).save();
      documentId = generalJournal._id;
      await this.transactionsService.createdERPDocument(correlationId, {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentId: documentId,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocument: generalJournal,

        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentPattern:
          accountingMessagePattern.rollback_general_journal_action,
      });

      preparedJournal.document = generalJournal._id;
      preparedJournal.document_code = generalJournal.number;
      // handle side effects and create internal journal
      const generalLedger = await this.transactionsService.journalAccounting(
        preparedJournal as any,
        request?.user?.code,
      );

      generalJournal.accounting_journal = generalLedger._id;
      generalJournal.save();

      let renderedTemplate = { template: null };
      if (generalJournals.printOptions?.templateLang) {
        renderedTemplate = await this.printOne(
          {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            journalId: generalJournal._id,
            ...generalJournals.printOptions,
          },
          request,
        );
      }
      await this.transactionsService.transactionCompleted(correlationId);

      return {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        generalJournal,
        template: generalJournals?.printOptions?.print
          ? renderedTemplate.template
          : null,
      };
    } catch (err) {
      await this.transactionsService.handleFailure(
        request.user?.code,
        correlationId,
        err,
      );
      throw err;
    }
  }

  async findOne(query, req: RequestWithUser): Promise<any> {
    if (query._id) {
      query._id = new Types.ObjectId(query._id);
    }

    const rs = await this.generalJournalModel.aggregate([
      {
        $match: {
          ...query,
        },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNodePUP',
        },
      },

      {
        $project: {
          balance_type: 1,
          reference: 1,
          note: 1,
          number: 1,
          name: 1,
          createdAt: 1,
          transactions: 1,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          accountingNodePUP: 1,
          by_user: 1,
          branch: 1,
        },
      },
    ]);

    for (let index = 0; index < rs[0].transactions.length; index++) {
      const matchingNode = rs[0].accountingNodePUP.find(
        (accountingNode) =>
          accountingNode._id.toString() ===
          rs[0].transactions[index].accountingNode.toString(),
      );

      if (matchingNode) {
        // Clone the matching node to avoid mutating the original object
        // which would affect subsequent iterations with the same accounting node
        const nodeData = { ...matchingNode };

        if (
          rs[0].transactions[index].partner_id &&
          (rs[0].transactions[index].partner_type === 'customer' ||
            rs[0].transactions[index].partner_type === 'vendor')
        ) {
          const partner = await this.tradeRpcService.getPartnersAccounts(
            req.user.code,
            {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              partnerType: rs[0].transactions[index].partner_type,
              id: rs[0].transactions[index].partner_id,
            },
          );
          // Modify the cloned object instead of the original
          nodeData.name = partner[0]?.name;
          nodeData.code = partner[0]?.number;
          nodeData._id = partner[0]?._id;
          rs[0].transactions[index].code = partner[0]?.number;
        }

        // eslint-disable-next-line @typescript-eslint/naming-convention
        const { name, code, balance_type, _id } = nodeData;

        rs[0].transactions[index].accountingNode = {
          name,
          code,
          balance_type,
          _id,
        };
      }
    }

    rs[0].accountingNodePUP = rs[0].accountingNodePUP.map(() => null);
    if (rs.length == 1) {
      return rs[0];
    } else {
      return undefined;
    }
  }

  async prepareJournal(
    createGeneralJournalDto: CreateGeneralJournalDto | UpdateGeneralJournalDto,
  ) {
    const { ...rest } = createGeneralJournalDto;
    const transactionDupMerged = this.parseTransactions(rest.transactions);
    rest.transactions = transactionDupMerged;
    let journalSum = 0;

    const accoutingNodeIds = [];
    if (!rest.note) {
      rest.note = rest.transactions[0].description;
    }
    for (let i = 0; i < rest.transactions.length; i++) {
      if (rest.transactions[i].partner_id) {
        const partner = await this.tradeRpcService.getPartner(
          this.request.tenantId,
          {
            _id: rest.transactions[i].partner_id,
            type: rest.transactions[i].partner_type,
          },
        );
        if (!partner) {
          throw new HttpException(
            nameOf(erpExceptionCode, (x) => x.partnerNotFound),
            erpExceptionCode.partnerNotFound,
          );
        }
        rest.transactions[i].accountingNode = new Types.ObjectId(
          partner.accounting_info.general_account._id,
        );
      }
      rest.transactions[i].description =
        rest.transactions[i].description || rest.note;
      rest.transactions[i].credit = +Number(
        rest.transactions[i].credit,
      ).toFixed(2);
      rest.transactions[i].debit = +Number(rest.transactions[i].debit).toFixed(
        2,
      );
      journalSum = Calculator.sum(
        journalSum,
        +Number(rest.transactions[i].credit),
      )
        .round()
        .number();

      journalSum = Calculator.subtract(
        journalSum,
        +Number(rest.transactions[i].debit).toFixed(2),
      )
        .round()
        .number();
      rest.transactions[i].by_user = rest.by_user;
      accoutingNodeIds.push(rest.transactions[i].accountingNode);
      if (rest.transactions[i].debit > 0 && rest.transactions[i].credit > 0) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.onlyCreditOrDebit),
          erpExceptionCode.onlyCreditOrDebit,
        );
      }
    }

    // Get unique accounting node IDs to avoid duplicate validation issues
    // when the same account appears multiple times in transactions
    const uniqueAccountingNodeIds = [
      ...new Set(accoutingNodeIds.map((id) => String(id))),
    ];

    const acNodeResult =
      await this.accountingNodeProxyPatternService.findAllRawId(
        uniqueAccountingNodeIds.map((id) => new Types.ObjectId(id)),
        rest.branch,
      );
    if (+Number(journalSum).toFixed(2) !== parseFloat('0')) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotBalanced),
        erpExceptionCode.journalNotBalanced,
      );
    }
    if (acNodeResult.length !== uniqueAccountingNodeIds.length) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.notValidAccount),
        erpExceptionCode.notValidAccount,
      );
    }
    if (!rest.note && !rest.transactions[0].description) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.noteAndDescriptionNotfound),
        erpExceptionCode.noteAndDescriptionNotfound,
      );
    }
    return rest;
  }

  private parseTransactions(data: IGeneralTransaction[]) {
    return data.reduce(
      (
        r: IGeneralTransaction[],
        { accountingNode, credit, debit, description },
        index: number,
      ) => {
        let temp;
        if (credit > 0) {
          temp = r.find(
            (o) =>
              String(o.accountingNode) === String(accountingNode) &&
              o.credit > 0,
          );
        }
        if (debit > 0) {
          temp = r.find(
            (o) =>
              String(o.accountingNode) === String(accountingNode) &&
              o.debit > 0,
          );
        }
        if (!temp) {
          r.push(data[index]);
        } else {
          temp.credit += credit;
          temp.debit += debit;
          temp.description = `${temp.description} - ${description} `;
        }
        return r;
      },
      [],
    );
  }

  async findAll(
    querys: GetGeneralJournalDto,
    sortBy: string = '',
    ids: Types.ObjectId[],
  ): Promise<GeneralJournalResults> {
    const temp: GetGeneralJournalDto = querys;
    const {
      limit,
      page,
      // comes from endpoint
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_date,
      queries,
      code,
      codes,
      nodeIds,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_number,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_number,
      ...rest
    } = temp;

    interface GetJournal extends GetGeneralLedgerDto {
      createdAt?: any;
      transactions?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.code'?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.accountingNode'?: any;
      $sort?: any;
      sortBy?: string;
      _id?;
      number?;
      code?;
    }

    let query = rest as GetJournal;
    if (sortBy) {
      query = { ...query, $sort: sortBy };
    }
    if (Array.isArray(ids)) {
      query._id = { $in: ids };
    }
    if (code) {
      query = { ...query, 'transactions.code': code };
    }
    if (codes) {
      query = { ...query, 'transactions.code': { $in: codes } };
    }
    if (nodeIds) {
      query = { ...query, 'transactions.accountingNode': { $in: nodeIds } };
    }
    if (from_number) {
      query.number = { $gte: from_number };
    }
    if (to_number) {
      query.number = { $lte: to_number };
    }
    if (from_date || to_date) {
      query.createdAt = {};
      if (from_date) {
        query.createdAt.$gte = new Date(from_date);
      }
      if (to_date) {
        query.createdAt.$lte = to_date;
      }
    }
    query.type = journalTypes.general;
    if (queries) {
      const searchText = {
        $regex: new RegExp(`(^|\\s)${queries}`),
        $options: 'i',
      };

      query = {
        ...query,
        $or: [
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$number' },
                regex: new RegExp(`^${queries}`),
                options: 'i',
              },
            },
          },
          {
            note: searchText,
          },
          {
            type: searchText,
          },
        ],
      } as any;
    }

    const aggrigateQuery = [
      {
        $match: query,
      },
      /*       {
        $lookup: {
          from: 'accountingnodes',
          localField: 'transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNodePUP',
        },
      }, */
      {
        $project: {
          balance_type: 1,
          reference: 1,
          note: 1,
          number: 1,
          createdAt: 1,

          // eslint-disable-next-line @typescript-eslint/naming-convention
        },
      },
    ] as any;
    if (page && limit) {
      aggrigateQuery.push({ $skip: (page - 1) * limit }, { $limit: limit });
    }
    const data = await this.generalJournalModel.aggregate(aggrigateQuery);
    const meta = await paginate(limit, page, this.generalJournalModel, query);
    /*
    data.forEach((object) => {
      object.transactions.forEach((transaction) => {
        const matchingNode = object.accountingNodePUP.find(
          (accountingNode) =>
            accountingNode._id.toString() ===
            transaction.accountingNode.toString(),
        );

        if (matchingNode) {
          transaction.accountingNode = matchingNode;
        }
      });
      object.accountingNodePUP = object.accountingNodePUP.map(() => null);
    });
 */
    return { result: data, meta };
  }

  async findAllReport(
    querys: GetGeneralJournalDto,
    sortBy: string = '',
    ids: Types.ObjectId[],
  ): Promise<GeneralJournalResults> {
    const temp: GetGeneralJournalDto = querys;
    const {
      limit,
      page,
      // comes from endpoint
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_date,
      queries,
      code,
      codes,
      nodeIds,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_number,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_number,
      ...rest
    } = temp;

    interface GetJournal extends GetGeneralLedgerDto {
      createdAt?: any;
      transactions?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.code'?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.accountingNode'?: any;
      $sort?: any;
      sortBy?: string;
      _id?;
      number?;
      code?;
    }

    let query = rest as GetJournal;
    if (sortBy) {
      query = { ...query, $sort: sortBy };
    }
    if (Array.isArray(ids)) {
      query._id = { $in: ids };
    }
    if (code) {
      query = { ...query, 'transactions.code': code };
    }
    if (codes) {
      query = { ...query, 'transactions.code': { $in: codes } };
    }
    if (nodeIds) {
      query = { ...query, 'transactions.accountingNode': { $in: nodeIds } };
    }
    if (from_number) {
      query.number = { $gte: from_number };
    }
    if (to_number) {
      query.number = { $lte: to_number };
    }
    if (from_date || to_date) {
      query.createdAt = {};
      if (from_date) {
        query.createdAt.$gte = new Date(from_date);
      }
      if (to_date) {
        query.createdAt.$lte = to_date;
      }
    }
    query.type = journalTypes.general;
    if (queries) {
      const searchText = {
        $regex: new RegExp(`(^|\\s)${queries}`),
        $options: 'i',
      };

      query = {
        ...query,
        $or: [
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$number' },
                regex: new RegExp(`^${queries}`),
                options: 'i',
              },
            },
          },
          {
            note: searchText,
          },
          {
            type: searchText,
          },
        ],
      } as any;
    }

    const aggrigateQuery = [
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNodePUP',
        },
      },
    ] as any;
    if (page && limit) {
      aggrigateQuery.push({ $skip: (page - 1) * limit }, { $limit: limit });
    }
    const data = await this.generalJournalModel.aggregate(aggrigateQuery);
    const meta = await paginate(limit, page, this.generalJournalModel, query);

    data.forEach((object) => {
      object.transactions.forEach((transaction) => {
        const matchingNode = object.accountingNodePUP.find(
          (accountingNode) =>
            accountingNode._id.toString() ===
            transaction.accountingNode.toString(),
        );

        if (matchingNode) {
          // Clone the matching node to avoid mutating the original object
          // which would affect subsequent iterations with the same accounting node
          transaction.accountingNode = { ...matchingNode };
        }
      });
      object.accountingNodePUP = object.accountingNodePUP.map(() => null);
    });

    return { result: data, meta };
  }

  async update(
    _id: string,
    updateGeneralJournalDto: UpdateGeneralJournalDto,
    req: RequestWithUser,
  ) {
    const allowDocumentsEditionDocument =
      req.policyData.allow_documents_edition;
    const generalJorunal = await this.generalJournalModel.findOne({
      _id,
    });
    const changedLedger = [];
    const originalDoc = _.cloneDeep(generalJorunal);
    const editObject = allowDocumentsEditionDocument;
    handleGlobalEditRestriction(
      editObject.type,
      editObject.parameter || 0,
      generalJorunal.createdAt,
    );
    if (!generalJorunal)
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotFound),
        erpExceptionCode.journalNotFound,
      );
    const correlationId = await this.transactionsService.executeERPDocumentSaga(
      generalJorunal.branch,
      documentType.general_journal,
      action.edit,
      [steps.erp_document, steps.update_accounting_journaling],
    );

    try {
      updateGeneralJournalDto.by_user = new Types.ObjectId(
        generalJorunal.by_user,
      );
      updateGeneralJournalDto.document = generalJorunal._id;
      updateGeneralJournalDto.document_code = generalJorunal.number;
      updateGeneralJournalDto.transaction_date =
        generalJorunal.transaction_date;

      const preparedJournal = await this.prepareJournal(
        updateGeneralJournalDto,
      );

      extend(generalJorunal, preparedJournal);
      generalJorunal.save();
      await this.transactionsService.createdERPDocument(correlationId, {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentId: originalDoc._id,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocument: originalDoc,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentPattern:
          accountingMessagePattern.rollback_general_journal_action,
      });

      if (req.policyData.document_policy === documentPolicyEnum.override) {
        changedLedger.push(generalJorunal.accounting_journal);
      }

      preparedJournal.type = journalTypes.general;

      const result =
        await this.transactionsService.updateAccountingJournalAccounting(
          {
            _id: generalJorunal._id,
            type: generalJorunal.type,
            updated_transactions: preparedJournal.transactions,
            code: req.user.code,
          },
          req,
          changedLedger,
        );

      generalJorunal.accounting_journal = result._id;
      await generalJorunal.save();

      await this.transactionsService.transactionCompleted(correlationId);
      return generalJorunal;
    } catch (err) {
      await this.transactionsService.handleFailure(
        req.user?.code,
        correlationId,
        err,
      );
      throw err;
    }
  }

  async remove(_id: string, request: RequestWithUser): Promise<any> {
    const generalJournal = await this.generalJournalModel.findOne({
      _id,
    });
    const changedLedger = [];

    const correlationId = await this.transactionsService.executeERPDocumentSaga(
      generalJournal.branch,
      documentType.general_journal,
      action.delete,
      [steps.erp_document, steps.update_accounting_journaling],
    );

    try {
      if (!generalJournal) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.journalNotFound),
          erpExceptionCode.journalNotFound,
        );
      }

      await generalJournal.softDelete();
      await this.transactionsService.createdERPDocument(correlationId, {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentId: generalJournal._id,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocument: generalJournal,

        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentPattern:
          accountingMessagePattern.rollback_general_journal_action,
      });

      if (request.policyData.document_policy === documentPolicyEnum.override) {
        changedLedger.push(generalJournal.accounting_journal);
      }

      await this.transactionsService.updateAccountingJournalAccounting(
        {
          _id: generalJournal._id,
          type: generalJournal.type,
          code: request.user.code,
        },
        request,
        changedLedger,
      );

      await this.transactionsService.transactionCompleted(correlationId);

      return generalJournal;
    } catch (err) {
      await this.transactionsService.handleFailure(
        request.user?.code,
        correlationId,
        err,
      );
      throw err;
    }
  }

  async removeDoc(_id: Types.ObjectId): Promise<GeneralJournal> {
    const generalJournal = await this.generalJournalModel.findOneAndDelete({
      _id,
    });

    if (!generalJournal) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotFound),
        erpExceptionCode.journalNotFound,
      );
    }
    return generalJournal;
  }

  async printOne(
    getTemplateDto: GetJounalTemplateDto,
    req: RequestWithUser,
  ): Promise<any> {
    const { journalId, ...sendOptions } = getTemplateDto;
    const renderedTemplate = await this.createTemplate(
      req,
      journalId,
      sendOptions.templateLang,
    );

    await this.sendTemplate(renderedTemplate, sendOptions);

    return { template: renderedTemplate };
  }

  async createTemplate(req, journalId, lang) {
    const rs = await this.findOne(
      {
        _id: journalId,
        type: journalTypes.general,
      },
      req,
    );
    if (!rs)
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotFound),
        erpExceptionCode.journalNotFound,
      );
    const branch = await this.userRpcService.getBranches(req.user?.code, [
      rs.branch,
    ]);
    const user = await this.userRpcService.getUser(req.user?.code, rs.by_user);
    const totalCredit = rs.transactions.reduce(
      (sum, { credit }) => sum + credit,
      0,
    );
    const totalDebit = rs.transactions.reduce(
      (sum, { debit }) => sum + debit,
      0,
    );
    rs.current_date = new Date();
    rs.by_user = user;
    rs.branch = branch[0];
    rs.total_credit = totalCredit;
    rs.total_debit = totalDebit;
    rs.logo = 'https://svgur.com/i/wKH.svg';
    const company = await this.userRpcService.getCompany(req.user?.code);
    rs.company_name = company.name;
    rs.company_info = {
      logo: rs.logo,
      name: company.name,
      phone: company.phone,
      email: company.email,
      address: company.address,
      contact_person: company.contact_person,
      branch: {
        general_information: {
          name: branch[0]?.general_information?.name,
          tax_code: branch[0]?.general_information?.tax_code,
        },
      },
    };
    return await this.tradeRpcService.getRendered(this.request.tenantId, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      templateType: templateType.account_journal,
      data: rs,
      lang: lang,
    });
  }

  async sendTemplate(renderedTemplate: string, sendOptions: PrintOptionsDto) {
    if (sendOptions.sendEmail) {
      this.notificationRpcService.sendReport({
        template: renderedTemplate,
        email: sendOptions.email,
        subject: 'General Journal',
        phone: sendOptions.phoneNumber,
        whatsapp: false,
      });
    }
    if (sendOptions.sendSms) {
      // send template via sms
    }
  }

  async rollback(id: Types.ObjectId, action_type: action, erp_document) {
    switch (action_type) {
      case action.create:
        return this.removeDoc(id);
      case action.delete:
        const generalJournal =
          await this.generalJournalModel.findByIdWithDeleted(String(id));
        if (generalJournal) {
          return generalJournal.restore();
        }
      case action.edit:
        await this.removeDoc(id);
        const newDoc = new this.generalJournalModel({
          ...erp_document,
          branch: new Types.ObjectId(erp_document.branch),
        });
        return await newDoc.save();
      default:
      /*         throw new HttpException(
          nameOf(tradeExceptionCode, (exception) => exception.actionNotFound),
          tradeExceptionCode.actionNotFound,
        ); */
    }
  }
}
