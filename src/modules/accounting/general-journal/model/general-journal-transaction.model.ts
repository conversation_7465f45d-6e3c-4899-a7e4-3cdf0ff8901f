import {
  Table,
  Column,
  Model,
  DataType,
  CreatedAt,
  UpdatedAt,
  DeletedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { AccountingNodeModel } from '../../accounting-node/model/accounting-node.model';
import { UserModel } from '../../../users/users/model/users.model';
//import { PartnerModel } from '../../../trade/partners/model/partner.model';
import { GeneralJournalModel } from './general-journal.model';

@Table({
  tableName: 'general_journal_transactions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class GeneralJournalTransactionModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => GeneralJournalModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  general_journal_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'type of transaction - credit or debit',
  })
  transaction_type: string;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
  })
  credit: number;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
  })
  debit: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description: string;

  @ForeignKey(() => AccountingNodeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  accounting_node_id: number;

  // @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  partner_id: number;

  @ForeignKey(() => UserModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  by_user_id: number;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @DeletedAt
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // Relations
  @BelongsTo(() => AccountingNodeModel, 'accounting_node_id')
  accountingNode: AccountingNodeModel;

  /*   @BelongsTo(() => PartnerModel, 'partner_id')
  partner: PartnerModel;
 */
  @BelongsTo(() => UserModel, 'by_user_id')
  user: UserModel;

  @BelongsTo(() => GeneralJournalModel, 'general_journal_id')
  generalJournal: GeneralJournalModel;
}
