import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { UserModel } from '../../../users/users/model/users.model';
import { GeneralLedgerModel } from '../../general-ledger/model/general-ledger.model';
import { GeneralJournalTransactionModel } from './general-journal-transaction.model';

@Table({
  tableName: 'general_journals',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class GeneralJournalModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  number: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  note: string;

  @ForeignKey(() => UserModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  by_user_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  reference: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
    defaultValue: DataType.NOW,
  })
  transaction_date: Date;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  branch_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // Relations
  @BelongsTo(() => UserModel, 'by_user_id')
  user: UserModel;

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;

  @HasMany(() => GeneralLedgerModel, {
    foreignKey: 'referenceable_id',
    constraints: false,
    scope: {
      referenceable_type: 'general_journal',
    },
  })
  generalLedgers: GeneralLedgerModel[];

  @HasMany(() => GeneralJournalTransactionModel, 'general_journal_id')
  generalJournalTransactions: GeneralJournalTransactionModel[];
}
