import { HttpException, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ApiOkResponse } from '@nestjs/swagger';
import { Types } from 'mongoose';

import { CreateGeneralLedgerDto } from './dto/create-general-ledger.dto';
import { GetGeneralLedgerDto } from './dto/get-general-ledger.dto';
import { ITransaction } from './dto/transaction.dto';
import { UpdateGeneralLedgerDto } from './dto/update-general-ledger.dto';

import {
  GeneralLedger,
  GeneralLedgerDocument,
  journalTypes,
} from './schema/general-ledger.schema';

import * as _ from 'lodash';

import { TradeRpcService } from '../../rpc/trade-rpc.service';

import {
  documentPolicyEnum,
  documentPolicyStatus,
} from '../../policy/enum/document-policy.enum';

import { UserRpcService } from '../../rpc/user-rpc.service';
import { TrailBalanceDto } from '../reports/dto/trail-balance.dto';
import { PartnerGeneralLedgerService } from '../partner-general-ledger/partner-general-ledger.service';
import { conditionTypeEnum } from './enum/condition-type.enum';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { JournalRecalculateProxyPatternService } from '../proxy-pattern/journal-recalculate-pattern.service';
import { results } from './interface/result.interface';
import { SoftDeleteModel } from '../../../utils/schemas';
import { Calculator } from '../../../utils/calculator';
import { nameOf } from '../../../utils/object-key-name';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { CustomHttpException } from '../../../utils/custom-http.exception';
import { paginate } from '../../../utils/dto';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class GeneralLedgerService {
  constructor(
    @InjectModel(GeneralLedger.name)
    private generalLedgerModel: SoftDeleteModel<GeneralLedgerDocument>,
    private accountingNodeProxyPatternService: AccountingNodeProxyPatternService,
    private readonly journalRecalculateProxyPatternService: JournalRecalculateProxyPatternService,
    private readonly tradeRpcService: TradeRpcService,
    private readonly partnerLedgerService: PartnerGeneralLedgerService,
    private readonly userRpcService: UserRpcService,
  ) {}

  async create(
    createJournalDto: CreateGeneralLedgerDto,
    prepare: boolean = true,
    code: number,
    branch: Types.ObjectId,
  ): Promise<GeneralLedger> {
    // Store original data for partner ledger (before any processing)
    const originalData = _.cloneDeep(createJournalDto);

    let data;
    if (prepare == true) {
      data = await this.prepareJournal(createJournalDto, branch);
    }
    data = data || createJournalDto;

    // Check if all transactions belong to one accounting node and all have partner info
    const uniqueAccountingNodes = new Set(
      data.transactions.map((t) => String(t.accountingNode)),
    );
    const allTransactionsHavePartnerInfo = data.transactions.every(
      (t) => t.partner_id !== undefined && t.partner_type !== undefined,
    );

    // If all transactions belong to one accounting node and all have partner id and partner type
    // create general ledger with credit and debit set to 0, and create partner ledger with actual amounts
    if (uniqueAccountingNodes.size === 1 && allTransactionsHavePartnerInfo) {
      console.log(
        'Creating general ledger with zero amounts and partner ledger with actual amounts - all transactions belong to one accounting node with partner info',
      );

      // Create general ledger with merged transactions having credit and debit set to 0
      const generalLedgerData = _.cloneDeep(data);
      generalLedgerData.transactions = this.mergeTransactionsByAccountingNode(
        generalLedgerData.transactions,
      ).map((transaction) => ({
        ...transaction,
        credit: 0,
        debit: 0,
      }));

      const generalLedger =
        await this.generalLedgerModel.create(generalLedgerData);

      // Create partner ledger with original unprocessed amounts and same _id as general ledger
      const partnerLedgerData = _.cloneDeep(originalData);
      partnerLedgerData._id = generalLedger._id; // Use the same _id as general ledger to be easy for update and delete
      const cleanedPartnerData = _.omit(partnerLedgerData, [
        '__v',
        'createdAt',
        'updatedAt',
      ]);

      await this.partnerLedgerService.create(cleanedPartnerData, code);

      return generalLedger;
    }

    // Original logic for mixed transactions
    // Merge transactions by accounting node for general ledger with net calculation
    const mergedData = {
      ...data,
      transactions: this.mergeTransactionsByAccountingNode(data.transactions),
    };
    const generalLedger = await this.generalLedgerModel.create(mergedData);
    const accountingNodes = [];

    // Check for partner transactions in original data (before merging removed partner fields)
    const hasPartnerTransactions = originalData.transactions.some(
      (transaction) => transaction.partner_id !== undefined,
    );

    for (const transaction of generalLedger.transactions) {
      const hasJournal =
        await this.journalRecalculateProxyPatternService.hasJournals(
          new Types.ObjectId(transaction.accountingNode),
          generalLedger.transaction_date.toISOString(),
        );
      if (hasJournal) {
        accountingNodes.push(transaction.accountingNode);
      }
    }

    //find partner general ledger transactions
    if (hasPartnerTransactions) {
      // Use original unprocessed data for partner ledger
      const originalPartnerTransactions = originalData.transactions.filter(
        (transaction) => transaction.partner_id !== undefined,
      );

      const partnerGeneralLedgerData = _.cloneDeep(generalLedger);
      partnerGeneralLedgerData.transactions = originalPartnerTransactions;

      const cleanedData = _.omit(partnerGeneralLedgerData, [
        '__v',
        'createdAt',
        'updatedAt',
      ]);

      await this.partnerLedgerService.create(cleanedData, code);
    }

    if (accountingNodes.length > 0) {
      await this.journalRecalculateProxyPatternService.dispatchRecalculateJob(
        code,
        accountingNodes,
        branch,
      );
    }

    return generalLedger;
  }

  async prepareJournal(
    createJournalDto: CreateGeneralLedgerDto | UpdateGeneralLedgerDto,
    branch: Types.ObjectId,
  ) {
    const { ...rest } = createJournalDto;
    rest.transactions = this.parseTransactions(rest.transactions);
    if (branch) {
      rest.branch = branch;
    }
    let journalSum = 0;

    const accountingNodeIds = [];
    for (let i = 0; i < rest.transactions.length; i++) {
      rest.transactions[i].credit = +Number(
        rest.transactions[i].credit,
      ).toFixed(6);
      rest.transactions[i].debit = +Number(rest.transactions[i].debit).toFixed(
        6,
      );
      journalSum = Calculator.sum(journalSum, rest.transactions[i].credit)
        .round()
        .number();
      journalSum = Calculator.subtract(journalSum, rest.transactions[i].debit)
        .round()
        .number();

      rest.transactions[i].by_user = new Types.ObjectId(rest.by_user);
      rest.transactions[i].accountingNode = new Types.ObjectId(
        rest.transactions[i].accountingNode,
      );
      accountingNodeIds.push(rest.transactions[i].accountingNode);
      if (rest.transactions[i].debit > 0 && rest.transactions[i].credit > 0) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.onlyCreditOrDebit),
          erpExceptionCode.onlyCreditOrDebit,
        );
      }
      if (rest.transactions[i].partner_id) {
        rest.transactions[i].partner_id = new Types.ObjectId(
          rest.transactions[i].partner_id,
        );
      }
    }

    if (+Number(journalSum).toFixed(2) !== parseFloat('0')) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotBalanced),
        erpExceptionCode.journalNotBalanced,
      );
    }

    if (!rest.note && !rest.transactions[0].description) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.noteAndDescriptionNotfound),
        erpExceptionCode.noteAndDescriptionNotfound,
      );
    }

    const uniqueAccountingNodeIds = [
      ...new Set(accountingNodeIds.map((id) => String(id))),
    ];

    const acNodeResult =
      await this.accountingNodeProxyPatternService.findAllRawId(
        uniqueAccountingNodeIds.map((id) => new Types.ObjectId(id)),
        rest.branch,
      );

    if (acNodeResult.length !== uniqueAccountingNodeIds.length) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.notValidAccount),
        erpExceptionCode.notValidAccount,
      );
    }

    if (!rest.note) {
      rest.note = rest.transactions[0].description;
    }
    for (let index = 0; index < rest.transactions.length; index++) {
      const element = rest.transactions[index] as ITransaction;
      const acNode = acNodeResult.find((acnode) => {
        return (
          String(acnode._id) == String(rest.transactions[index].accountingNode)
        );
      });
      rest.transactions[index].description =
        rest.transactions[index].description || rest.note;
      const acNodeData = await this.accountingNodeProxyPatternService.findOne(
        {
          _id: element.accountingNode,
        },
        branch,
      );

      //to check if the account used in different way thant it is
      if (
        (element.partner_id == undefined || element.partner_id == null) &&
        (element.partner_type == undefined || element.partner_id == null)
      ) {
        const accountUsedAsGeneral = await this.checkAccountingNodeTransactions(
          element.accountingNode,
          conditionTypeEnum.general,
        );
        if (accountUsedAsGeneral) {
          throw new CustomHttpException(
            nameOf(
              erpExceptionCode,
              (exceptions) => exceptions.accountAlreadyUsedAsPartner,
            ),
            erpExceptionCode.accountAlreadyUsedAsPartner,
            {
              _id: acNodeData._id,
              name: acNodeData.name,
            },
          );
        }
      }

      acNodeData.balance = Calculator.subtract(
        acNodeData.balance,
        element.credit,
      )
        .round()
        .number();

      acNodeData.balance = Calculator.sum(acNodeData.balance, element.debit)
        .round()
        .number();

      rest.transactions[index].current_balance = Calculator.sum(
        acNode.balance,
        rest.transactions[index].debit,
      )
        .round()
        .number();
      rest.transactions[index].current_balance = Calculator.subtract(
        rest.transactions[index].current_balance,
        rest.transactions[index].credit,
      )
        .round()
        .number();

      const { currentBalance, transactionSeq } =
        await this.findJournalAccountNodeLastBalance(
          acNodeData._id,
          createJournalDto.transaction_date.toString(),
        );
      rest.transactions[index].before_balance =
        +Number(currentBalance).toFixed(6);
      if (transactionSeq > 0) {
        rest.transactions[index].transaction_sequence = +transactionSeq + 1;
      }
      rest.transactions[index].code = acNode.code;
      await acNodeData.save();
    }
    return rest;
  }

  parseTransactions(data: ITransaction[]) {
    return data.reduce(
      (
        r: ITransaction[],
        { accountingNode, credit, debit, description },
        index: number,
      ) => {
        let temp;
        if (credit > 0) {
          temp = r.find(
            (o) =>
              String(o.accountingNode) === String(accountingNode) &&
              o.credit > 0,
          );
        }
        if (debit > 0) {
          temp = r.find(
            (o) =>
              String(o.accountingNode) === String(accountingNode) &&
              o.debit > 0,
          );
        }
        if (!temp) {
          r.push(data[index]);
        } else {
          temp.credit += credit;
          temp.debit += debit;
          temp.description = `${temp.description} - ${description} `;
        }
        return r;
      },
      [],
    );
  }

  @ApiOkResponse({
    type: results,
  })
  async findAll(
    querys: GetGeneralLedgerDto,
    sortBy: string = '',
    ids: Types.ObjectId[],
  ): Promise<results> {
    const temp: GetJournal = querys;
    const {
      limit,
      page,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_transaction_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_equal_transaction_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_equal_transaction_date,
      queries,
      code,
      codes,
      nodeIds,
      partners,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_number,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_number,
      //used for destructure
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      manually,
      ...rest
    } = temp;

    interface GetJournal extends GetGeneralLedgerDto {
      createdAt?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      transaction_date?: any;
      transactions?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.code'?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.accountingNode'?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'transactions.partner_id'?: any;
      $sort?: any;
      sortBy?: string;
      _id?;
      number?;
      code?;
    }

    let query = rest as GetJournal;
    if (sortBy) {
      query = { ...query, $sort: sortBy };
    }
    if (Array.isArray(ids)) {
      query._id = { $in: ids };
    }
    if (code) {
      query = { ...query, 'transactions.code': code };
    }
    if (codes) {
      query = { ...query, 'transactions.code': { $in: codes } };
    }
    if (nodeIds) {
      query = { ...query, 'transactions.accountingNode': { $in: nodeIds } };
    }
    if (partners) {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      query = { ...query, 'transactions.partner_id': { $in: partners } };
    }
    if (from_number) {
      query.number = { $gte: from_number };
    }
    if (to_number) {
      query.number = { $lte: to_number };
    }
    if (from_date || to_date) {
      query.createdAt = {};
      if (from_date) {
        query.createdAt.$gte = new Date(from_date);
      }
      if (to_date) {
        query.createdAt.$lte = new Date(to_date);
      }
    }
    if (from_transaction_date) {
      query.transaction_date = {};
      query.transaction_date.$gt = new Date(from_transaction_date);
    }

    if (from_equal_transaction_date) {
      query.transaction_date = {};
      query.transaction_date.$gte = new Date(from_equal_transaction_date);

      if (to_equal_transaction_date) {
        query.transaction_date.$lte = new Date(to_equal_transaction_date);
      }
    }

    if (queries) {
      const searchText = { $regex: new RegExp(queries), $options: 'i' };
      query = {
        ...query,
        $or: [
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$number' },
                regex: new RegExp(queries),
                options: 'i',
              },
            },
          },
          {
            note: searchText,
          },
          {
            type: searchText,
          },
        ],
      } as any;
    }

    const aggrigateQuery = [
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNodePUP',
        },
      },
    ] as any;
    if (page && limit) {
      aggrigateQuery.push({ $skip: (page - 1) * limit }, { $limit: limit });
    }
    const data = await this.generalLedgerModel.aggregate(aggrigateQuery);
    const meta = await paginate(limit, page, this.generalLedgerModel, query);

    data.forEach((object) => {
      object.transactions.forEach((transaction) => {
        const matchingNode = object.accountingNodePUP.find(
          (accountingNode) =>
            accountingNode._id.toString() ===
            transaction.accountingNode.toString(),
        );

        if (matchingNode) {
          // Clone the matching node to avoid mutating the original object
          // which would affect subsequent iterations with the same accounting node
          transaction.accountingNode = { ...matchingNode };
        }
      });
      object.accountingNodePUP = object.accountingNodePUP.map(() => null);
    });

    return { result: data, meta };
  }

  async counter(): Promise<number> {
    const data = await this.generalLedgerModel.find({}).countDocuments();

    return data;
  }

  async findOne(query): Promise<GeneralLedgerDocument> {
    return await this.generalLedgerModel
      .findOne(query)
      .populate('transactions.accountingNode');
  }

  async findOneCustom(query: {
    _id: Types.ObjectId;
    code: number;
  }): Promise<any> {
    const data = await this.generalLedgerModel.aggregate([
      {
        $match: { _id: query._id },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNodePUP',
        },
      },
    ]);

    if (data.length === 0) {
      return null;
    }

    const generalLedger = data[0];

    // Get partner ledger with the same _id to retrieve original partner transactions
    const partnerLedger = await this.partnerLedgerService.findOne({
      _id: query._id,
    });

    // If partner ledger exists, replace only the transactions that had partner information
    // with the original unmerged partner transactions
    if (partnerLedger && partnerLedger.transactions) {
      // Find transactions in general ledger that were merged from partner transactions
      // These are transactions that belong to accounting nodes that have partner transactions
      const partnerAccountingNodes = new Set(
        partnerLedger.transactions.map((t) => String(t.accountingNode)),
      );

      // Replace general ledger transactions with partner transactions only for nodes that have partners
      const updatedTransactions = [];

      for (const glTransaction of generalLedger.transactions) {
        const nodeId = String(glTransaction.accountingNode);

        if (partnerAccountingNodes.has(nodeId)) {
          // This accounting node has partner transactions, use partner ledger transactions
          const partnerTransactionsForNode = partnerLedger.transactions.filter(
            (pt) => String(pt.accountingNode) === nodeId,
          );
          updatedTransactions.push(...partnerTransactionsForNode);
        } else {
          // This accounting node has no partner transactions, keep general ledger transaction
          updatedTransactions.push(glTransaction);
        }
      }

      generalLedger.transactions = updatedTransactions;
    }

    // Now process transactions with accounting node and partner information
    for (const transaction of generalLedger.transactions) {
      const matchingNode = generalLedger.accountingNodePUP.find(
        (accountingNode) =>
          accountingNode._id.toString() ===
          transaction.accountingNode.toString(),
      );

      if (matchingNode) {
        // Clone the matching node to avoid mutating the original object
        transaction.accountingNode = { ...matchingNode };
      }
    }

    generalLedger.accountingNodePUP = generalLedger.accountingNodePUP.map(
      () => null,
    );

    return generalLedger;
  }

  async updateDoc(
    code: number,
    document: Types.ObjectId | string,
    updateJournalDto: UpdateGeneralLedgerDto,
    branch: Types.ObjectId,
  ) {
    const internalJournal = await this.generalLedgerModel.findOne({
      _id: document,
    });
    if (!internalJournal) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotFound),
        erpExceptionCode.journalNotFound,
      );
    }

    updateJournalDto.transaction_date = internalJournal.transaction_date;
    const preparedJournal = await this.prepareJournal(updateJournalDto, branch);

    const partnerTransactions = [];
    for (const transaction of updateJournalDto.transactions) {
      //find partner general ledger transactions
      if (transaction.partner_type !== undefined) {
        partnerTransactions.push(transaction);
      }
    }

    const preparedNodes = preparedJournal.transactions.map((transaction) =>
      String(transaction.accountingNode),
    );
    const internalNodes = internalJournal.transactions.map((transaction) =>
      String(transaction.accountingNode),
    );

    const allUniqueNodes = Array.from(
      new Set([...preparedNodes, ...internalNodes]),
    );

    // Merge transactions by accounting node for general ledger update
    const mergedTransactions = this.mergeTransactionsByAccountingNode(
      updateJournalDto.transactions,
    );
    internalJournal.transactions = mergedTransactions;

    if (partnerTransactions.length > 0) {
      // Use original unmerged partner transactions for partner ledger
      const partnerGeneralLedgerData = _.cloneDeep(internalJournal);
      partnerGeneralLedgerData.transactions = partnerTransactions;

      const cleanedData = _.omit(partnerGeneralLedgerData, [
        '__v',
        'createdAt',
        'updatedAt',
      ]);
      await this.partnerLedgerService.update(cleanedData, code);
    }

    await internalJournal.save();

    await this.journalRecalculateProxyPatternService.dispatchRecalculateJob(
      code,
      allUniqueNodes,
      branch,
    );

    return internalJournal;
  }

  async remove(
    code: number,
    _id: Types.ObjectId,
    removeJournal: boolean = true,
    branch: Types.ObjectId,
  ): Promise<GeneralLedger> {
    const journalDoc = await this.generalLedgerModel
      .findOneAndDelete({ _id })
      .lean();

    if (!journalDoc) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotFound),
        erpExceptionCode.journalNotFound,
      );
    }

    //delete partner general ledger
    await this.partnerLedgerService.delete(new Types.ObjectId(_id));

    if (removeJournal) {
      const accountingNodes = journalDoc.transactions.map((transaction) =>
        String(transaction.accountingNode),
      );
      //TODO add branch
      await this.journalRecalculateProxyPatternService.dispatchRecalculateJob(
        code,
        accountingNodes,
        branch,
      );
    }
    return journalDoc;
  }

  async updateBulkJournals(
    code: number,
    journalsArray: any,
    branch: Types.ObjectId,
  ) {
    if (journalsArray.length > 0) {
      let journalSum = 0;

      for (const journal of journalsArray) {
        journal.updateOne.filter._id = new Types.ObjectId(
          journal.updateOne.filter._id,
        );
        journal.updateOne.filter['transactions.accountingNode'] =
          new Types.ObjectId(
            journal.updateOne.filter['transactions.accountingNode'],
          );
        journal.updateOne.arrayFilters.forEach((filter) => {
          filter['elem.accountingNode'] = new Types.ObjectId(
            filter['elem.accountingNode'],
          );
        });

        journalSum = Calculator.sum(
          journalSum,
          journal.updateOne.update.$set['transactions.$[elem].credit'],
        )
          .round()
          .number();

        journalSum = Calculator.subtract(
          journalSum,
          journal.updateOne.update.$set['transactions.$[elem].debit'],
        )
          .round()
          .number();
      }

      if (+Number(journalSum).toFixed(2) !== parseFloat('0')) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.journalNotBalanced),
          erpExceptionCode.journalNotBalanced,
        );
      }

      await this.generalLedgerModel.bulkWrite(journalsArray);

      const ids = journalsArray.map((journal) => journal.updateOne.filter._id);

      await this.journalRecalculateProxyPatternService.dispatchRecalculateJob(
        code,
        ids,
        branch,
      );
    }
  }

  async journalAccountingDetails(_id: string | Types.ObjectId): Promise<any> {
    const result = await this.generalLedgerModel.aggregate([
      {
        $match: {
          'transactions.accountingNode': new Types.ObjectId(String(_id)),
        },
      },
      { $unwind: '$transactions' },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNodeDetails',
        },
      },
      { $unwind: '$accountingNodeDetails' },
      {
        $project: {
          _id: { $month: '$transaction_date' },
          credit: {
            $cond: {
              if: {
                $eq: [
                  '$transactions.accountingNode',
                  new Types.ObjectId(String(_id)),
                ],
              },
              then: { $sum: '$transactions.credit' },
              else: '$$REMOVE',
            },
          },
          debit: {
            $cond: {
              if: {
                $eq: [
                  '$transactions.accountingNode',
                  new Types.ObjectId(String(_id)),
                ],
              },
              then: { $sum: '$transactions.debit' },
              else: '$$REMOVE',
            },
          },
          balance_type: '$accountingNodeDetails.balance_type',
          transaction_date: '$transaction_date',
        },
      },
      { $sort: { transaction_date: -1 } },
      {
        $group: {
          _id: '$_id',
          credit: { $sum: '$credit' },
          debit: { $sum: '$debit' },
          balance_type: { $first: '$balance_type' },
          last_transaction_date: { $max: '$transaction_date' },
        },
      },
      {
        $addFields: {
          credit: { $round: ['$credit', 6] },
          debit: { $round: ['$debit', 6] },
        },
      },
      { $sort: { last_transaction_date: 1 } },
    ]);

    let beforeRecordBalance = 0;
    let totalCredit = 0;
    let totalDebit = 0;
    result.forEach((record) => {
      record.balance = record.debit - record.credit + beforeRecordBalance;
      beforeRecordBalance = record.balance;
      totalCredit += record.credit;
      totalDebit += record.debit;

      if (record.balance_type === 'credit') {
        record.balance = +Number(record.balance * -1).toFixed(6);
      }
      if (record.balance_type === 'debit') {
        record.balance = +Number(record.balance * 1).toFixed(6);
      }
    });
    const outStandingBalance = totalDebit - totalCredit;

    return {
      result: result,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      outStandingBalance:
        result[0]?.balance_type === 'debit'
          ? +Number(outStandingBalance * 1).toFixed(6)
          : +Number(beforeRecordBalance * -1).toFixed(6),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      totalDebit: +Number(totalDebit).toFixed(6),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      totalCredit: +Number(totalCredit).toFixed(6),
    };
  }

  async journalAccountingDetailsByIds(
    ids: Types.ObjectId[],
    balanceType: string,
  ): Promise<any> {
    const objectIdArray = ids.map((id) => new Types.ObjectId(String(id)));
    const result = await this.generalLedgerModel.aggregate([
      {
        $match: {
          'transactions.accountingNode': { $in: objectIdArray },
        },
      },
      { $unwind: '$transactions' },
      {
        $project: {
          month: { $month: '$transaction_date' },
          year: { $year: '$transaction_date' },
          credit: {
            $cond: {
              if: { $in: ['$transactions.accountingNode', objectIdArray] },
              then: '$transactions.credit',
              else: 0,
            },
          },
          debit: {
            $cond: {
              if: { $in: ['$transactions.accountingNode', objectIdArray] },
              then: '$transactions.debit',
              else: 0,
            },
          },
          transaction_date: '$transaction_date',
        },
      },
      {
        $group: {
          _id: { month: '$month', year: '$year' },
          credit: { $sum: '$credit' },
          debit: { $sum: '$debit' },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          lastTransactionDate: { $max: '$transaction_date' },
        },
      },
      {
        $addFields: {
          credit: { $round: ['$credit', 6] },
          debit: { $round: ['$debit', 6] },
        },
      },
      { $sort: { 'date.year': 1, 'date.month': 1 } },
    ]);

    let beforeRecordBalance = 0;
    let totalCredit = 0;
    let totalDebit = 0;
    result.forEach((record) => {
      record.balance = record.debit - record.credit + beforeRecordBalance;
      beforeRecordBalance = record.balance;
      totalCredit += record.credit;
      totalDebit += record.debit;

      if (balanceType === 'credit') {
        record.balance = +Number(record.balance * -1).toFixed(6);
      }
      if (balanceType === 'debit') {
        record.balance = +Number(record.balance * 1).toFixed(6);
      }
    });
    const outStandingBalance = totalDebit - totalCredit;

    return {
      result: result,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      outStandingBalance:
        balanceType === 'debit'
          ? +Number(outStandingBalance * 1).toFixed(6)
          : +Number(beforeRecordBalance * -1).toFixed(6),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      totalDebit: +Number(totalDebit).toFixed(6),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      totalCredit: +Number(totalCredit).toFixed(6),
    };
  }

  async findJournalAccountNodeLastBalance(
    nodeId: Types.ObjectId,
    from_date: string,
  ) {
    const dateOnly = new Date(from_date);

    let transactionSeq = 0;
    let currentBalance = 0;

    const journals = await this.generalLedgerModel
      .find({
        'transactions.accountingNode': new Types.ObjectId(nodeId),
        transaction_date: {
          $gte: new Date(
            dateOnly.getFullYear(),
            dateOnly.getMonth(),
            dateOnly.getDate(),
          ),
          $lt: new Date(
            dateOnly.getFullYear(),
            dateOnly.getMonth(),
            dateOnly.getDate(),
            23,
            59,
            59,
            999,
          ),
        },
      })
      .sort({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'transactions.transaction_sequence': -1,
      })
      .limit(1);

    if (journals.length < 1) {
      const previousDate = new Date(from_date);

      const prevDateResult = await this.generalLedgerModel
        .find({
          'transactions.accountingNode': nodeId,
          transaction_date: {
            $lt: previousDate,
          },
        })
        .sort({
          transaction_date: -1,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'transactions.transaction_sequence': -1,
        })
        .limit(1);

      if (prevDateResult.length > 0) {
        const transaction = prevDateResult[0]?.transactions.find(
          (transaction) =>
            String(transaction.accountingNode) === String(nodeId),
        );
        currentBalance = transaction?.current_balance ?? 0;
      }
    } else {
      const transaction = journals[0]?.transactions.find(
        (transaction) => String(transaction.accountingNode) === String(nodeId),
      );

      currentBalance = transaction?.current_balance ?? 0;
      transactionSeq = transaction?.transaction_sequence;

      if (transactionSeq === undefined) {
        await this.generalLedgerModel.findOneAndUpdate(
          {
            _id: journals[0]?._id,
            'transactions.accountingNode': new Types.ObjectId(nodeId),
          },
          {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            $set: { 'transactions.$.transaction_sequence': 1 },
          },
          {
            new: true,
          },
        );
        transactionSeq = 1;
      }
    }

    return {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      currentBalance: currentBalance,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      transactionSeq: transactionSeq,
    };
  }

  private mergeTransactionsByAccountingNode(transactions: any[]): any[] {
    const mergedMap = new Map();

    for (const transaction of transactions) {
      const nodeId = String(transaction.accountingNode);

      if (mergedMap.has(nodeId)) {
        const existing = mergedMap.get(nodeId);
        // Calculate net amount for this accounting node: all debits - all credits
        const totalDebit = existing.debit + (transaction.debit || 0);
        const totalCredit = existing.credit + (transaction.credit || 0);
        const netAmount = totalDebit - totalCredit;

        // If net amount is positive, put it in debit and set credit to 0
        // If net amount is negative, put absolute value in credit and set debit to 0
        if (netAmount > 0) {
          existing.debit = netAmount;
          existing.credit = 0;
        } else if (netAmount < 0) {
          existing.credit = Math.abs(netAmount);
          existing.debit = 0;
        } else {
          // If net amount is 0, both debit and credit should be 0
          existing.debit = 0;
          existing.credit = 0;
        }

        // Concatenate descriptions
        if (
          transaction.description &&
          transaction.description !== existing.description
        ) {
          existing.description = `${existing.description} - ${transaction.description}`;
        }

        delete existing.partner_id;
      } else {
        // Create a new entry for this accounting node (no merging needed)
        // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars
        const { partner_id, ...transactionWithoutPartner } = transaction;
        mergedMap.set(nodeId, {
          ...transactionWithoutPartner,
          credit: transaction.credit || 0,
          debit: transaction.debit || 0,
        });
      }
    }

    return Array.from(mergedMap.values());
  }

  public async addBeginningBalance(
    code: number,
    data: CreateGeneralLedgerDto,
    branch: Types.ObjectId,
  ) {
    const beginningBalanceJournal = await this.generalLedgerModel.findOne({
      is_beginning_balance: true,
    });

    // Merge transactions by accounting node for general ledger
    const mergedData = {
      ...data,
      transactions: this.mergeTransactionsByAccountingNode(data.transactions),
    };

    let generalLedger;
    if (beginningBalanceJournal) {
      generalLedger = await this.generalLedgerModel
        .findOneAndUpdate(
          {
            is_beginning_balance: true,
          },
          mergedData,
          { new: true },
        )
        .exec();
    } else {
      generalLedger = await this.generalLedgerModel.create(mergedData);
    }

    // Pass original unmerged data to partner ledger
    await this.partnerLedgerService.addPartnerBeginningBalance(
      new Types.ObjectId(generalLedger._id),
      data,
    );

    const accountingNodes = generalLedger.transactions.map((transaction) =>
      String(transaction.accountingNode),
    );

    await this.journalRecalculateProxyPatternService.dispatchRecalculateJob(
      code,
      accountingNodes,
      branch,
    );
  }

  public async seeder(leafs: any[]) {
    await this.generalLedgerModel.deleteMany({});

    await this.generalLedgerModel.create({
      code: 1,
      transactions: [
        {
          code: leafs[0].code,
          credit: 0,
          debit: 100,
          accountingNode: new Types.ObjectId(leafs[0]._id),
          by_user: new Types.ObjectId('65128e387d6e377e37b40a6c'),
          description: 'beginning balance journal',
          current_balance: 100,
          partner_id: null,
          partner_type: null,
          before_balance: 0,
          is_beginning_balance: true,
        },
        {
          code: leafs[1].code,
          credit: 100,
          debit: 0,
          accountingNode: new Types.ObjectId(leafs[1]._id),
          by_user: new Types.ObjectId('65128e387d6e377e37b40a6c'),
          description: 'beginning balance journal',
          current_balance: -100,
          partner_id: null,
          partner_type: null,
          before_balance: 0,
          is_beginning_balance: true,
        },
        {
          code: leafs[2].code,
          credit: 0,
          debit: 1500,
          accountingNode: new Types.ObjectId(leafs[2]._id),
          by_user: new Types.ObjectId('65128e387d6e377e37b40a6c'),
          description: 'beginning balance journal',
          current_balance: 1500,
          partner_id: null,
          partner_type: null,
          before_balance: 0,
          is_beginning_balance: true,
        },
        {
          code: leafs[3].code,
          credit: 1500,
          debit: 0,
          accountingNode: new Types.ObjectId(leafs[3]._id),
          by_user: new Types.ObjectId('65128e387d6e377e37b40a6c'),
          description: 'beginning balance journal',
          current_balance: -1500,
          partner_id: null,
          partner_type: null,
          before_balance: 0,
          is_beginning_balance: true,
        },
      ],
      type: journalTypes.temp_beginning_balance,
      by_user: new Types.ObjectId('65128e387d6e377e37b40a6c'),
      note: 'beginning balance journal',
      reference: 'beginning balance journal',
      transaction_date: new Date(new Date().getFullYear(), 0, 1, 0, 0, 0, 0),
      is_beginning_balance: true,
    });

    leafs[0].balance = 100;
    leafs[0].beginning_balance = 100;
    leafs[1].balance = -100;
    leafs[1].beginning_balance = -100;
    leafs[2].balance = 1500;
    leafs[2].beginning_balance = 1500;
    leafs[3].balance = -1500;
    leafs[3].beginning_balance = -1500;
    return leafs;
  }

  async getTrailBalance(
    accountingNodeLeafs: any[],
    dto: TrailBalanceDto,
    branch?: Types.ObjectId,
  ) {
    const date = new Date(dto.date);
    const startOfDay = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      23,
      59,
      59,
      999,
    );

    return await Promise.all(
      accountingNodeLeafs.map(async (leaf) => {
        const query: any = {
          'transactions.accountingNode': new Types.ObjectId(String(leaf._id)),
          transaction_date: {
            $lte: startOfDay,
          },
        };

        if (branch) {
          query.branch = new Types.ObjectId(branch);
        }

        const result = await this.generalLedgerModel.aggregate([
          {
            $match: query,
          },
          { $unwind: '$transactions' },
          {
            $lookup: {
              from: 'accountingnodes',
              localField: 'transactions.accountingNode',
              foreignField: '_id',
              as: 'accountingNodeDetails',
            },
          },
          { $unwind: '$accountingNodeDetails' },
          {
            $project: {
              _id: 0,
              accountingNode: '$transactions.accountingNode',
              credit: '$transactions.credit',
              debit: '$transactions.debit',
              balance_type: '$accountingNodeDetails.balance_type',
              balance: '$accountingNodeDetails.balance',
              code: '$accountingNodeDetails.code',
              name: '$accountingNodeDetails.name',
              group: '$accountingNodeDetails.group',
              beginning_balance: '$accountingNodeDetails.beginning_balance',
              is_beginning_balance: '$transactions.is_beginning_balance',
              current_balance: '$transactions.current_balance',
            },
          },
          {
            $match: {
              accountingNode: new Types.ObjectId(String(leaf._id)),
            },
          },
          {
            $group: {
              _id: '$accountingNode',
              code: { $first: '$code' },
              name: { $first: '$name' },
              credit: { $sum: '$credit' },
              debit: { $sum: '$debit' },
              balance_type: { $first: '$balance_type' },
              beginning_balance: { $first: '$beginning_balance' },
              ending_balance_raw: { $last: '$current_balance' },
              ending_balance: {
                $last: {
                  $cond: {
                    if: { $eq: ['$balance_type', 'credit'] },
                    then: { $multiply: [-1, '$current_balance'] },
                    else: { $multiply: [1, '$current_balance'] },
                  },
                },
              },
              group: { $last: '$group' },
            },
          },
          {
            $addFields: {
              credit: { $round: ['$credit', 6] },
              debit: { $round: ['$debit', 6] },
              beginning_balance: { $round: ['$beginning_balance', 6] },
              ending_balance: { $round: ['$ending_balance', 6] },
            },
          },
          // ...(dto.showTransactions ? [] : [{ $unset: ['credit', 'debit'] }]),
          // ...(dto.showBeginningBalance
          //   ? []
          //   : [{ $unset: 'beginning_balance' }]),
          // ...(dto.showEndingBalance ? [] : [{ $unset: 'ending_balance' }]),
        ]);

        if (result.length < 1) {
          const object = {
            _id: leaf._id,
            code: leaf.code,
            name: leaf.name,
            credit: dto.showTransactions ? 0 : undefined,
            debit: dto.showTransactions ? 0 : undefined,
            balance_type: leaf.balance_type,
            beginning_balance: dto.showBeginningBalance
              ? (leaf.beginning_balance ?? 0)
              : undefined,
            ending_balance: dto.showEndingBalance
              ? (leaf.ending_balance ?? 0)
              : undefined,
            group: leaf.group,
          };
          Object.keys(object).forEach((key) => {
            if (object[key] === undefined) {
              delete object[key];
            }
          });
          return object;
        }
        return result;
      }),
    );
  }

  async findCustoms(getJournalDto) {
    const res = await this.findOneCustom({
      code: getJournalDto.code,
      _id: getJournalDto.ids[0],
    });
    const transactions = res.transactions;
    for (let index = 1; index < getJournalDto.ids.length; index++) {
      const customJournal = await this.findOneCustom({
        code: getJournalDto.code,
        _id: getJournalDto.ids[index],
      });
      transactions.push(...customJournal.transactions);
    }
    return res;
  }

  public async checkAccountingNodeTransactions(
    accountingNode: Types.ObjectId,
    conditionType: conditionTypeEnum,
  ) {
    const conditionalMatch =
      conditionType === conditionTypeEnum.general
        ? {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'transactions.partner_type': { $exists: true, $ne: null },
          }
        : {
            $or: [
              // eslint-disable-next-line @typescript-eslint/naming-convention
              { 'transactions.partner_type': { $exists: false } },
              // eslint-disable-next-line @typescript-eslint/naming-convention
              { 'transactions.partner_type': null },
            ],
          };

    const result = await this.generalLedgerModel.aggregate([
      {
        $match: {
          'transactions.accountingNode': accountingNode,
        },
      },
      { $unwind: '$transactions' },
      {
        $match: {
          'transactions.accountingNode': accountingNode,
          ...conditionalMatch,
        },
      },
      {
        $project: {
          _id: 1,
          transactions: 1,
        },
      },
      { $limit: 1 },
    ]);

    return result.length > 0;
  }

  async applyAdjustmentOrOverride(
    query,
    updatedDocumentTr: ITransaction[],
    policyData,
    user_id,
    code,
    branch,
  ) {
    let result;
    const nodes = [];
    const ledger = await this.generalLedgerModel.findOne({
      document: new Types.ObjectId(query._id),
    });
    if (!ledger) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.ledgerNotfound),
        erpExceptionCode.ledgerNotfound,
      );
    }

    switch (policyData.document_policy) {
      case documentPolicyEnum.adjustment:
        if (updatedDocumentTr && updatedDocumentTr?.length !== 0) {
          const docBalanceMap = await this.calculateCurrentState(query._id);

          const accountingTransactions: ITransaction[] = [];

          for (const updatedTransaction of updatedDocumentTr) {
            //find ac node
            if (
              docBalanceMap.has(String(updatedTransaction.accountingNode)) &&
              docBalanceMap.get(String(updatedTransaction.accountingNode))
                .net_balance !== 0
            ) {
              const acCalMap = docBalanceMap.get(
                String(updatedTransaction.accountingNode),
              );
              //credit
              if (acCalMap.net_balance >= 0) {
                const diff = Calculator.subtract(
                  updatedTransaction.credit,
                  Math.abs(acCalMap.net_balance),
                ).valueOf();

                accountingTransactions.push({
                  debit:
                    diff < 0
                      ? Calculator.sum(
                          Math.abs(diff),
                          updatedTransaction.debit,
                        ).valueOf()
                      : 0,
                  credit: diff > 0 ? diff : 0,
                  description: 'adjustment for amount change',
                  accountingNode: new Types.ObjectId(
                    updatedTransaction.accountingNode,
                  ),
                  partner_id: updatedTransaction?.partner_id || undefined,
                  partner_type: updatedTransaction.partner_type || undefined,
                  by_user: new Types.ObjectId(user_id),
                });
                acCalMap.done = true;
              }
              //debit
              if (acCalMap.net_balance < 0) {
                const diff = Calculator.subtract(
                  updatedTransaction.debit,
                  Math.abs(acCalMap.net_balance),
                ).valueOf();
                accountingTransactions.push({
                  debit: diff > 0 ? diff : 0,
                  credit:
                    diff < 0
                      ? Calculator.sum(
                          Math.abs(diff),
                          updatedTransaction.credit,
                        ).valueOf()
                      : 0,
                  description: 'adjustment for amount change',
                  accountingNode: new Types.ObjectId(
                    updatedTransaction.accountingNode,
                  ),
                  partner_id: updatedTransaction?.partner_id || undefined,
                  partner_type: updatedTransaction.partner_type || undefined,
                  by_user: new Types.ObjectId(user_id),
                });
              }
              acCalMap.done = true;
            } else {
              accountingTransactions.push({
                debit: updatedTransaction.debit,
                credit: updatedTransaction.credit,
                description: 'adjustment for amount change',
                accountingNode: new Types.ObjectId(
                  updatedTransaction.accountingNode,
                ),
                partner_id: updatedTransaction?.partner_id || undefined,
                partner_type: updatedTransaction?.partner_type || undefined,
                by_user: new Types.ObjectId(user_id),
              });
            }
            // clean unused accounting node
          }
          for (const [nodeId, nodeData] of docBalanceMap.entries()) {
            if (!nodeData.done && nodeData.net_balance !== 0) {
              const diff = nodeData.net_balance; // Positive means net credit, negative means net debit

              accountingTransactions.push({
                debit: diff > 0 ? diff : 0, // If net credit, need a debit to balance
                credit: diff < 0 ? Math.abs(diff) : 0, // If net debit, need a credit to balance
                description: 'adjustment for amount change',
                accountingNode: new Types.ObjectId(nodeId),
                partner_id: nodeData?.partner_id || undefined,
                partner_type: nodeData?.partner_type || undefined,
                by_user: new Types.ObjectId(user_id),
              });

              // Mark as processed
              nodeData.done = true;
            }
          }

          // Create the adjustment journal entry if there are transactions
          if (accountingTransactions.length > 0) {
            result = this.create(
              {
                transactions: accountingTransactions,
                type: ledger.type,
                by_user: user_id,
                branch: new Types.ObjectId(branch),
                note: 'adjustment for amount change',
                reference: 'adjustment',
                transaction_date: new Date(),
                related_journal_id: query._id,
                document: query._id,
              },
              true,
              code,
              new Types.ObjectId(branch),
            );
          }
          //delete logic
        } else {
          const accountingTransactions = [];
          const docBalanceMap = await this.calculateCurrentState(query._id);

          for (const [nodeId, nodeData] of docBalanceMap.entries()) {
            if (!nodeData.done && nodeData.net_balance !== 0) {
              const diff = nodeData.net_balance; // Positive means net credit, negative means net debit

              accountingTransactions.push({
                debit: diff > 0 ? diff : 0, // If net credit, need a debit to balance
                credit: diff < 0 ? Math.abs(diff) : 0, // If net debit, need a credit to balance
                description: 'adjustment for amount change',
                accountingNode: new Types.ObjectId(nodeId),
                by_user: new Types.ObjectId(user_id),
                partner_id: nodeData?.partner_id || undefined,
                partner_type: nodeData?.partner_type || undefined,
              });
              // Mark as processed
              nodeData.done = true;
            }
          }
          result = this.create(
            {
              transactions: accountingTransactions,
              type: ledger.type,
              by_user: user_id,
              branch: new Types.ObjectId(branch),
              note: 'adjustment for delete of general journal',
              reference: 'adjustment',
              transaction_date: new Date(),
              related_journal_id: query._id,
              document: query._id,
            },
            true,
            code,
            new Types.ObjectId(branch),
          );
        }

        break;
      case documentPolicyEnum.override:
        const documentData = await this.generalLedgerModel.findOne({
          document: query._id,
        });

        await this.partnerLedgerService.deleteByDocument(query._id);
        await documentData.softDelete();
        result = documentData;
        if (updatedDocumentTr) {
          result = this.create(
            {
              transactions: updatedDocumentTr,
              type: ledger.type,
              by_user: user_id,
              branch: new Types.ObjectId(branch),
              note: 'override journal',
              reference: query._id,
              transaction_date: new Date(),
              related_journal_id: query._id,
              document: query._id,
            },
            true,
            code,
            new Types.ObjectId(branch),
          );
        }
        await this.journalRecalculateProxyPatternService.dispatchRecalculateJob(
          code,
          nodes,
          branch,
        );
        break;
      default:
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.editDeletePolicyNotFound),
          erpExceptionCode.editDeletePolicyNotFound,
        );
    }

    await this.disableDocumentPolicyChange(
      code,
      policyData.document_policy_status,
    );

    // eslint-disable-next-line @typescript-eslint/naming-convention
    return result;
  }

  private async calculateCurrentState(document_id) {
    const documentData = await this.generalLedgerModel.find({
      document: new Types.ObjectId(document_id),
    });

    const acNodeMap = new Map();
    for (const doc of documentData) {
      for (let index = 0; index < doc.transactions.length; index++) {
        const element = doc.transactions[index];

        if (!acNodeMap.has(String(element.accountingNode))) {
          //init Ac node
          acNodeMap.set(String(element.accountingNode), {
            accountingNode: String(element.accountingNode),
            total_credit: 0, //element.credit,
            total_debit: 0, //element.debit,
            net_balance: 0, //Calculator.subtract(element.credit, element.debit), // Positive means net credit, negative means net debit
            partner_type: undefined,
            partner_id: undefined,
          });
        }
        const nodeData = acNodeMap.get(String(element.accountingNode));
        if (element.credit !== 0) {
          nodeData.total_credit = Calculator.sum(
            nodeData.total_credit,
            element.credit,
          ).valueOf();
        } else if (element.debit !== 0) {
          nodeData.total_debit = Calculator.sum(
            nodeData.total_debit,
            element.debit,
          ).valueOf();
        }

        nodeData.net_balance = Calculator.subtract(
          nodeData.total_credit,
          nodeData.total_debit,
        ).valueOf();
        nodeData.partner_type = element.partner_type;
        nodeData.partner_id = element.partner_id;
      }
    }
    return acNodeMap;
  }
  private async disableDocumentPolicyChange(
    code: number,
    document_policy_status: documentPolicyStatus,
  ) {
    if (document_policy_status === documentPolicyStatus.enable) {
      // send RPC to disable it
      await this.userRpcService.disableDocumentPolicy(code);
    } else {
      return;
    }
  }

  async deleteByIds(code: number, ids: string[], policyData: any, branch) {
    const journals = [];

    switch (policyData.document_policy) {
      case documentPolicyEnum.adjustment:
        const result = await this.generalLedgerModel
          .find({ _id: { $in: ids } })
          .exec();
        journals.push(...result);

        await this.generalLedgerModel.deleteMany({ _id: { $in: ids } });
        break;

      case documentPolicyEnum.override:
        for (const id of ids) {
          const journal = await this.generalLedgerModel
            .findOne({ _id: new Types.ObjectId(id) })
            .exec();

          //delete newly created journal
          if (journal) {
            journals.push(journal);
            journal.deleteOne();
          } else {
            //restore old journal was deleted
            const deletedJournal =
              await this.generalLedgerModel.findByIdWithDeleted(id);

            if (deletedJournal) {
              journals.push(deletedJournal);
              deletedJournal.restore();
            }
          }
        }

        break;
    }

    for (const journal of journals) {
      const nodeId = journal.transactions.map((tr) => {
        return tr.accountingNode;
      });
      await this.journalRecalculateProxyPatternService.dispatchRecalculateJob(
        code,
        nodeId,
        branch,
      );
    }
    await this.disableDocumentPolicyChange(
      code,
      policyData.document_policy_status,
    );
    return true;
  }
}
