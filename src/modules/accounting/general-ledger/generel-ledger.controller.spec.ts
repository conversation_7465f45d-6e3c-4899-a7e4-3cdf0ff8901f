import { Test, TestingModule } from '@nestjs/testing';
import { GeneralLedgerController } from './general-ledger.controller';
import { GeneralLedgerService } from './general-ledger.service';
import { getModelToken } from '@nestjs/mongoose';
import { AccountingNodeService } from '../accounting-node/accounting-node.service';
import { GeneralLedgerRecalculateService } from './recalculate.service';
import { GeneralLedger } from './schema/general-ledger.schema';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { PartnerGeneralLedger } from '../partner-general-ledger/schema/partner-general-ledger.schema';
import { PartnerGeneralLedgerService } from '../partner-general-ledger/partner-general-ledger.service';
import { TransactionsService } from '../transactions/transactions.service';
import { PolicyService } from '../../policy/policy.service';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { JournalRecalculateProxyPatternService } from '../proxy-pattern/journal-recalculate-pattern.service';
import { CaslAbilityFactory } from '../../casl/casl-ability.factory';

describe('JournalController', () => {
  let controller: GeneralLedgerController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GeneralLedgerController],
      providers: [
        GeneralLedgerService,
        { provide: getModelToken(GeneralLedger.name), useValue: {} },
        { provide: getModelToken(PartnerGeneralLedger.name), useValue: {} },
        { provide: AccountingNodeService, useValue: {} },
        { provide: GeneralLedgerRecalculateService, useValue: {} },
        { provide: UserRpcService, useValue: {} },
        { provide: TradeRpcService, useValue: {} },
        { provide: PartnerGeneralLedgerService, useValue: {} },
        { provide: TransactionsService, useValue: {} },
        { provide: PolicyService, useValue: {} },
        { provide: AccountingNodeProxyPatternService, useValue: {} },
        { provide: JournalRecalculateProxyPatternService, useValue: {} },
        { provide: CaslAbilityFactory, useValue: {} },
      ],
    }).compile();

    controller = module.get<GeneralLedgerController>(GeneralLedgerController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
