import { Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Model, Types } from 'mongoose';
import { GeneralLedger } from './schema/general-ledger.schema';
import { RecalculateGeneralLedgerDto } from './dto/recalculate-general-ledger.dto';
import { GeneralLedgerService } from './general-ledger.service';
import { WorkerService } from '../worker/worker.service';
import recalculateGeneralLedger from './worker/config';
import { SchedulerRpcService } from '../../rpc/scheduler-rpc.service';
import { PartnerGeneralLedgerService } from '../partner-general-ledger/partner-general-ledger.service';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';

export class results {
  @ApiProperty({
    type: GeneralLedger,
    isArray: true,
  })
  result: GeneralLedger[];
  @ApiProperty({
    type: 'string',
  })
  meta: any;
}

@Injectable({ scope: Scope.REQUEST, durable: true })
export class GeneralLedgerRecalculateService {
  constructor(
    @InjectModel(GeneralLedger.name) private journalModel: Model<GeneralLedger>,
    private readonly partnerLedgerService: PartnerGeneralLedgerService,
    private journalService: GeneralLedgerService,
    private accountingNodeProxyPatternService: AccountingNodeProxyPatternService,
    private workerService: WorkerService,
    private readonly schedulerRpcService: SchedulerRpcService,
  ) {}

  async calculate(dto: RecalculateGeneralLedgerDto) {
    const accountingNodes = dto.accounting_nodes.map((accountNode) => {
      return new Types.ObjectId(accountNode);
    });

    // eslint-disable-next-line @typescript-eslint/naming-convention
    const query: any = { nodeIds: accountingNodes, branch: dto.branch };

    const journals = await this.journalService.findAll(query, '', undefined);

    const paresJournalIdsToString = journals.result.map((journal) => ({
      ...journal,
      _id: journal._id.toString(),
      transactions: journal.transactions.map((transaction) => ({
        ...transaction,
        accountingNode: {
          ...(transaction.accountingNode as any),
          _id: String((transaction.accountingNode as any)._id),
        },
        by_user: String(transaction.by_user),
        partner_id: String(transaction.partner_id),
      })),
    }));

    const accountingNodesString = dto.accounting_nodes.map((accountNode) => {
      return String(accountNode);
    });

    const result = (await this.workerService.runWorker(
      recalculateGeneralLedger,
      {
        journals: paresJournalIdsToString,
        accounting_nodes: accountingNodesString,
        branch: dto.branch,
      },
    )) as any;

    console.log(JSON.stringify(result, null, 2), 'worker result');

    const isValidObjectId = (id) => {
      return (
        Types.ObjectId.isValid(id) && String(new Types.ObjectId(id)) === id
      );
    };

    const convertedUpdateOperations = result.updateGeneraLedgersData.map(
      (operation) => {
        const { filter, update } = operation.updateOne;

        const convertedFilter = {
          _id: filter._id,
          branch: dto.branch,
        };

        const convertedUpdate = {
          $set: {},
        };

        for (const key in update.$set) {
          const value = update.$set[key];
          const updatedValue = { ...value };

          if (isValidObjectId(value.accountingNode)) {
            updatedValue.accountingNode = new Types.ObjectId(
              value.accountingNode,
            );
          }
          if (isValidObjectId(value.by_user)) {
            updatedValue.by_user = new Types.ObjectId(value.by_user);
          }
          if (value.partner_id && isValidObjectId(value.partner_id)) {
            updatedValue.partner_id = new Types.ObjectId(value.partner_id);
          }

          convertedUpdate.$set[key] = updatedValue;
        }

        return {
          updateOne: {
            filter: convertedFilter,
            update: convertedUpdate,
          },
        };
      },
    );

    await this.accountingNodeProxyPatternService.bulkUpdate(
      result.updateAccountingNodeData,
    );

    if (convertedUpdateOperations.length > 0) {
      await this.journalModel.bulkWrite(convertedUpdateOperations);
    }

    await this.partnerLedgerService.recalculate(
      result.updatePartnerGeneraLedgersData,
      dto.branch,
      dto.code,
    );

    return true;
  }

  public async updateJob(jobId: string, status: string, code: number) {
    this.schedulerRpcService.updateJob(status, jobId, code);
    return true;
  }
}
