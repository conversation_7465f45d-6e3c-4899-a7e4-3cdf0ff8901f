import { Module } from '@nestjs/common';
import { GeneralLedgerController } from './general-ledger.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { AutoNumberingModule } from '../auto-numbering/auto-numbering.module';
import { GeneralLedgerService } from './general-ledger.service';
import { GeneralLedgerRecalculateService } from './recalculate.service';
import {
  GeneralLedger,
  generalLedgerSchema,
} from './schema/general-ledger.schema';
import { WorkerModule } from '../worker/worker.module';
import { RpcModule } from '../../rpc/rpc.module';
import { PartnerGeneralLedgerModule } from '../partner-general-ledger/partner-general-ledger.module';
import { ProxyPatternModule } from '../proxy-pattern/proxy-pattern.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { GeneralLedgerModel } from './model/general-ledger.model';
import { GeneralLedgerTransactionModel } from './model/general-ledger-transaction.model';
import { PartnerLedgerTransactionModel } from './model/partner-ledger-transaction.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      GeneralLedgerModel,
      GeneralLedgerTransactionModel,
      PartnerLedgerTransactionModel,
    ]),
    MongooseModule.forFeature([
      { name: GeneralLedger.name, schema: generalLedgerSchema },
    ]),
    AutoNumberingModule,
    ProxyPatternModule,
    WorkerModule,
    RpcModule,
    PartnerGeneralLedgerModule,
  ],
  controllers: [GeneralLedgerController],
  providers: [GeneralLedgerService, GeneralLedgerRecalculateService],
  exports: [GeneralLedgerService, GeneralLedgerRecalculateService],
})
export class GeneralLedgerModule {}
