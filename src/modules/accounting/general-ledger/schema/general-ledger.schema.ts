import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

import { ITransaction } from '../dto/transaction.dto';
import {
  SoftDeleteDocument,
  softDeletePlugin,
} from '../../../../utils/schemas';
export enum journalTypes {
  payment_voucher = 'payment_voucher',
  receipt_voucher = 'receipt_voucher',
  general = 'general',
  purchase_invoice = 'purchase_invoice',
  purchase_return_invoice = 'purchase_return_invoice',
  sales_invoice = 'sales_invoice',
  sales_order = 'sales_order',
  sales_return_invoice = 'sales_return_invoice',
  memo = 'memo',
  store_adjustment = 'store_adjustment',
  physical_inventory_posting = 'physical_inventory_posting',
  temp_beginning_balance = 'temp_beginning_balance',
}
export type GeneralLedgerDocument = HydratedDocument<GeneralLedger> &
  SoftDeleteDocument;
@Schema({ timestamps: true })
export class GeneralLedger {
  @Prop()
  transactions: ITransaction[];

  @Prop({ type: String, enum: journalTypes })
  type: journalTypes;

  @Prop()
  note: string;

  @Prop({ type: Types.ObjectId })
  by_user: Types.ObjectId;

  @Prop()
  reference: string;

  @Prop({ type: Date, default: Date.now() })
  transaction_date: Date;

  @Prop({ type: Types.ObjectId })
  Inventory_journal: Types.ObjectId;

  @Prop({ type: Types.ObjectId })
  document: Types.ObjectId;

  @Prop({ type: Number })
  document_code: number;

  @Prop({ type: Types.ObjectId })
  branch: Types.ObjectId;

  createdAt?: Date;

  @Prop({ type: Boolean, default: false })
  is_beginning_balance: boolean;

  @Prop({ required: false, type: Types.ObjectId, ref: GeneralLedger.name })
  related_journal_id: Types.ObjectId;
}

export const generalLedgerSchema = SchemaFactory.createForClass(GeneralLedger);
generalLedgerSchema.plugin(softDeletePlugin);
