import { IsDate, ValidateNested } from '@nestjs/class-validator';
import { Transform, Type } from 'class-transformer';
import { Types } from 'mongoose';
import { ITransaction } from './transaction.dto';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { journalTypes } from '../schema/general-ledger.schema';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class CreateGeneralLedgerDto {
  @IsOptional()
  _id?: Types.ObjectId;
  @ApiProperty({
    description: 'transactions of journal',
    example: [
      {
        credit: 100,
        debit: 0,
        description: 'this is test comment',
        accountingNode: '63400372c582a633284bf24c',
        by_user: '63400372c582a633284bf24c',
      },
      {
        credit: 0,
        debit: 100,
        description: 'this is test comment',
        accountingNode: '63400372c582a633284bf24c',
        by_user: '63400372c582a633284bf24c',
      },
    ],
  })
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(2)
  @Type(() => ITransaction)
  transactions?: ITransaction[];

  @ApiProperty({
    description: 'type of journal',
    enum: journalTypes,
    isArray: false,
    example: journalTypes.sales_invoice,
    required: true,
  })
  @IsEnum(journalTypes)
  type: journalTypes;

  @ApiProperty({
    description: 'userid who made of journal',
    example: '63400372c582a633284bf24c',
    required: true,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  by_user?: Types.ObjectId;

  @ApiProperty({
    description: 'brnach of accouting',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;

  @ApiProperty({
    description: 'note journal',
    example: 'test note',
  })
  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsOptional()
  reference?: string;

  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  @ApiProperty({
    description: 'date journal',
    example: new Date(),
    required: false,
  })
  transaction_date?: Date;

  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  Inventory_journal?: Types.ObjectId;

  @IsNotEmpty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  document?: Types.ObjectId;

  @IsNumber()
  @IsOptional()
  @IsNotEmpty()
  document_code?: number;

  @ApiHideProperty()
  @IsOptional()
  headers?: any;

  @ApiHideProperty()
  @IsOptional()
  code?: number;

  @IsOptional()
  related_journal_id?: Types.ObjectId;
}
