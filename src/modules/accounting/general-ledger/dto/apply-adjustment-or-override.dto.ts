import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

import { Types } from 'mongoose';
import { journalTypes } from '../schema/general-ledger.schema';

import { ITransaction } from './transaction.dto';
import { IsOptional } from '@nestjs/class-validator';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

import { RpcDto } from '../../../../utils/dto/rpc.dto';

class ApplyAdjustmentOrOverride {
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id: Types.ObjectId;

  @IsNotEmpty()
  @IsEnum(journalTypes)
  type: journalTypes;

  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  user_id: Types.ObjectId;

  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(2)
  @Type(() => ITransaction)
  @IsOptional()
  updated_transactions: ITransaction[];
}
export class ApplyAdjustmentOrOverrideDto extends RpcDto {
  @ValidateNested()
  @Type(() => ApplyAdjustmentOrOverride)
  data: ApplyAdjustmentOrOverride;
}
