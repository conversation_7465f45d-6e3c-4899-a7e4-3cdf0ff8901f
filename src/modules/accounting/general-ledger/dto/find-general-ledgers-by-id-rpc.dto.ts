import { Types } from 'mongoose';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { RpcDto } from '../../../../utils/dto/rpc.dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class FindGeneralLedgersByIdRpcDto extends RpcDto {
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsNotEmpty()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map((id) =>
        safeMongoIdTransformer({ value: id, property: 'id' }),
      );
    }
    return value;
  })
  document_id: Types.ObjectId;
}
