import { Types } from 'mongoose';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

import { RpcDto } from '../../../../utils/dto/rpc.dto';

export class FindOneGeneralLedgerRpcDto extends RpcDto {
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id?: Types.ObjectId;
}
