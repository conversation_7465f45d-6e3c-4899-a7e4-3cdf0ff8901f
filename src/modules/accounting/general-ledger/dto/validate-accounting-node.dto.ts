import { IsEnum, IsNotEmpty } from 'class-validator';
import { Types } from 'mongoose';
import { Transform } from 'class-transformer';

import { conditionTypeEnum } from '../enum/condition-type.enum';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

import { RpcDto } from '../../../../utils/dto/rpc.dto';

export class ValidateAccountingNodeDto extends RpcDto {
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  accounting_node: Types.ObjectId;

  @IsNotEmpty()
  @IsEnum(conditionTypeEnum)
  condition_type: conditionTypeEnum;
}
