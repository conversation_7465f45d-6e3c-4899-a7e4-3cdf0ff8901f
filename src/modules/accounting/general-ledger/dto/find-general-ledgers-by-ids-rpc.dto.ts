import { Types } from 'mongoose';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

import { RpcDto } from '../../../../utils/dto/rpc.dto';

export class FindGeneralLedgersByIdsRpcDto extends RpcDto {
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsNotEmpty()
  @IsArray()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map((id) =>
        safeMongoIdTransformer({ value: id, property: 'id' }),
      );
    }
    return value;
  })
  ids?: Types.ObjectId[];
}
