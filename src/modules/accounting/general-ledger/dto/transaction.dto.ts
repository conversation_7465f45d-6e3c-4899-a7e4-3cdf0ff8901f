import { <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/class-validator';
import { Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Prop } from '@nestjs/mongoose';
import { AccountingNode } from '../../accounting-node/schema/accounting-node.schema';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class ITransaction {
  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'code of transaction',
    example: '0',
    required: true,
  })
  code?: string;

  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  @ApiProperty({
    description: 'credit of transaction',
    example: 1223,
    required: false,
  })
  credit: number;

  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  @ApiProperty({
    description: 'debit of transaction',
    example: 1223,
    required: false,
  })
  debit: number;

  @ApiProperty({
    description: 'Description of transaction',
    example: 'commands',
    required: false,
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    description: 'accountingNode of transaction',
    example: '63400372c582a633284bf24c',
    required: true,
    type: 'string',
  })
  @Prop({ type: Types.ObjectId, ref: AccountingNode.name })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  accountingNode: Types.ObjectId;

  @IsNumber()
  @IsOptional()
  @Prop({ type: Number, default: 0 })
  current_balance?: number;

  @IsNumber()
  @IsOptional()
  @Prop({ type: Number, default: 0 })
  before_balance?: number;

  by_user?: Types.ObjectId;

  type?: any;

  journal?: any;

  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  document?: Types.ObjectId;

  @IsOptional()
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  partner_id?: Types.ObjectId;

  @IsOptional()
  @IsNotEmpty()
  @IsString()
  partner_type?: string;

  @IsOptional()
  @IsBoolean()
  @Prop({ type: Boolean, default: false, required: false })
  is_beginning_balance?: boolean;

  document_code?: any;

  created_at?: any;

  balance?: any;

  @IsOptional()
  @IsNumber()
  @Prop({ type: Number, required: false })
  transaction_sequence?: number;
}
