import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';
import { Types } from 'mongoose';
import { Transform } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class RecalculateGeneralLedgerDto {
  @ApiProperty({
    description: 'userid who made of journal',
    example: '6501b9f946ad8225ab76cb7f',
    required: true,
  })
  @IsArray()
  accounting_nodes: Types.ObjectId[];

  @ApiProperty({
    description: 'branch',
    example: '6501b9f946ad8225ab76cb7f',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;

  @IsNumber()
  code: number;
}
