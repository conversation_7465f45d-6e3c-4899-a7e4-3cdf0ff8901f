import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { Types } from 'mongoose';
import { journalTypes } from '../schema/general-ledger.schema';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class GetGeneralLedgerDto /* extends PartialType(CreateJournalDto) */ {
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public from_date?: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public from_transaction_date?: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public from_equal_transaction_date?: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public to_equal_transaction_date?: string;

  @IsBoolean()
  @IsOptional()
  public manually?: boolean;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public to_date?: string;

  @ApiProperty({
    required: false,
  })
  @IsString()
  @IsOptional()
  public code?: string;

  @ApiProperty({
    required: false,
  })
  @IsArray()
  @IsOptional()
  public codes?: string[];

  @ApiProperty({
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  public is_beginning_balance?: boolean;

  @ApiProperty({
    required: false,
  })
  @IsArray()
  @IsOptional()
  public partners?: Types.ObjectId[];

  @ApiProperty({
    required: false,
  })
  @IsArray()
  @IsOptional()
  public nodeIds?: Types.ObjectId[];

  @IsString()
  @IsOptional()
  public queries?: string;

  @ApiProperty({
    description: 'branch id journal',
    example: '63400372c582a633284bf24c',
    type: 'string',
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public limit?: number;

  @ApiProperty({
    description: 'type of journal',
    enum: journalTypes,
    isArray: false,
    example: journalTypes.sales_invoice,
    required: false,
  })
  @IsOptional()
  @IsEnum(journalTypes)
  type?: journalTypes;

  @ApiProperty({
    description: 'from number',
    example: 0,
  })
  @Type(() => Number)
  @IsString()
  @IsOptional()
  from_number?: number;

  @ApiProperty({
    description: 'to number',
    example: 0,
  })
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  to_number?: number;

  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  number?: number;
}
