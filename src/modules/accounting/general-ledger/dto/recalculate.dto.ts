import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

import { Types } from 'mongoose';
import { IsNotEmpty } from 'class-validator';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class RecalculateDto {
  @ApiProperty({
    description: 'userid who made of journal',
    example: '6501b9f946ad8225ab76cb7f',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  accounting_node: Types.ObjectId;
}
