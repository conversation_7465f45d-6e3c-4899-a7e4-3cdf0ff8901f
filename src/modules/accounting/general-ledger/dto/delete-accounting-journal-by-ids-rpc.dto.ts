import {
  ArrayMinSize,
  IsArray,
  IsMongoId,
  ValidateNested,
} from 'class-validator';

import { Type } from 'class-transformer';
import { RpcDto } from '../../../../utils/dto/rpc.dto';

class DeleteAccountingJournalByIdsRpc {
  @IsArray()
  @ArrayMinSize(1)
  @IsMongoId({ each: true })
  public ids: string[];
}

export class DeleteAccountingJournalByIdsRpcDto extends RpcDto {
  @ValidateNested()
  @Type(() => DeleteAccountingJournalByIdsRpc)
  data: DeleteAccountingJournalByIdsRpc;
}
