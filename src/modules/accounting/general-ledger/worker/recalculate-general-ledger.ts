import { workerData, parentPort } from 'worker_threads';
import { GeneralLedger } from '../schema/general-ledger.schema';

import { Types } from 'mongoose';
import { Calculator } from 'src/utils/calculator';
import { sortByBeginningBalanceAndDate } from '../../../../utils/ledger-sort';

async function run() {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { journals, accounting_nodes, branch } = workerData as {
    journals: any;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    accounting_nodes: any;
    branch: Types.ObjectId;
  };

  const updateGeneraLedgersData = [];
  const updatePartnerGeneraLedgersData = [];
  const updateAccountingNodeData = [];

  for (const accountingNode of accounting_nodes) {
    let beforeBalanceCalculate = 0;
    let accountNodeBalanceCalculate = 0;
    let currentBalanceCalculate = 0;

    let filteredJournals = journals.filter((journal) => {
      return journal.transactions.some(
        (transaction) =>
          String((transaction.accountingNode as any)?._id) ===
          String(accountingNode),
      );
    });

    if (filteredJournals.length < 1) {
      updateAccountingNodeData.push({
        updateOne: {
          filter: {
            _id: accountingNode,
            $or: [{ branch: { $exists: false } }, { branch }],
          },
          update: {
            $set: {
              balance: 0,
            },
          },
        },
      });
    }

    if (journals.length > 0) {
      filteredJournals = filteredJournals.map((journal) => ({
        ...journal,
        transaction_date: new Date(
          journal.transaction_date.toISOString().split('T')[0],
        ),
      })) as GeneralLedger[];

      filteredJournals.sort(sortByBeginningBalanceAndDate);

      // filteredJournals.sort((a, b) => {
      //   if (a.is_beginning_balance === true && !b.is_beginning_balance) {
      //     return -1;
      //   }
      //   if (!a.is_beginning_balance && b.is_beginning_balance === true) {
      //     return 1;
      //   }
      //
      //   if (a.is_beginning_balance && b.is_beginning_balance) {
      //     if (a.transaction_date === b.transaction_date) {
      //       return getTransactionSequence(a) - getTransactionSequence(b);
      //     }
      //     return (
      //       new Date(a.transaction_date).getTime() -
      //       new Date(b.transaction_date).getTime()
      //     );
      //   }
      //
      //   function getTransactionSequence(journal) {
      //     return journal.transactions.reduce((minSequence, transaction) => {
      //       return Math.min(minSequence, transaction.transaction_sequence);
      //     }, Infinity);
      //   }
      //
      //   return (
      //     new Date(a.transaction_date).getTime() -
      //     new Date(b.transaction_date).getTime()
      //   );
      // });

      for (let index = 0; index < filteredJournals.length; index++) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/naming-convention
        const { createdAt, updatedAt, __v, accountingNodePUP, ...journal } =
          filteredJournals[index] as any;

        journal.transactions.map((transaction: any, index) => {
          if (
            String((transaction.accountingNode as any)._id) ===
            String(accountingNode)
          ) {
            accountNodeBalanceCalculate = Calculator.subtract(
              accountNodeBalanceCalculate,
              transaction.credit,
            )
              .round()
              .number();

            accountNodeBalanceCalculate = Calculator.sum(
              accountNodeBalanceCalculate,
              transaction.debit,
            )
              .round()
              .number();

            transaction.current_balance = Calculator.sum(
              currentBalanceCalculate,
              transaction.debit,
            )
              .round()
              .number();

            transaction.current_balance = Calculator.subtract(
              transaction.current_balance,
              transaction.credit,
            )
              .round()
              .number();

            (currentBalanceCalculate = transaction.current_balance),
              (transaction.before_balance = beforeBalanceCalculate);

            transaction.accountingNode = (
              transaction.accountingNode as any
            )._id;
            (beforeBalanceCalculate = transaction.current_balance),
              updateGeneraLedgersData.push({
                updateOne: {
                  filter: { _id: journal._id },
                  update: { $set: { [`transactions.${index}`]: transaction } },
                },
              });

            if (transaction.partner_id !== undefined) {
              updatePartnerGeneraLedgersData.push({
                updateOne: {
                  filter: { _id: journal._id },
                  update: {
                    $set: {
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      'transactions.$[elem]': transaction,
                    },
                  },
                  arrayFilters: [
                    { 'elem.accountingNode': transaction.accountingNode },
                  ],
                },
              });
            }
          }
        });
        updateAccountingNodeData.push({
          updateOne: {
            filter: { _id: accountingNode },
            update: {
              $set: {
                balance: accountNodeBalanceCalculate,
              },
            },
          },
        });
      }
    }
  }

  parentPort.postMessage({
    // eslint-disable-next-line @typescript-eslint/naming-convention
    updateGeneraLedgersData,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    updateAccountingNodeData,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    updatePartnerGeneraLedgersData,
  });
}

run();
