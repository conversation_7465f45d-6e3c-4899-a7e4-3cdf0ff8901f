import { Test, TestingModule } from '@nestjs/testing';
import { GeneralLedgerService } from './general-ledger.service';
import { getModelToken } from '@nestjs/mongoose';
import { GeneralLedger } from './schema/general-ledger.schema';
import { AccountingNodeService } from '../accounting-node/accounting-node.service';
import { GeneralLedgerRecalculateService } from './recalculate.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { PartnerGeneralLedger } from '../partner-general-ledger/schema/partner-general-ledger.schema';
import { PartnerGeneralLedgerService } from '../partner-general-ledger/partner-general-ledger.service';
import { PolicyService } from '../../policy/policy.service';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { TransactionsService } from '../transactions/transactions.service';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { JournalRecalculateProxyPatternService } from '../proxy-pattern/journal-recalculate-pattern.service';

describe('JournalService', () => {
  let service: GeneralLedgerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GeneralLedgerService,
        { provide: getModelToken(GeneralLedger.name), useValue: {} },
        { provide: getModelToken(PartnerGeneralLedger.name), useValue: {} },
        { provide: AccountingNodeService, useValue: {} },
        { provide: GeneralLedgerRecalculateService, useValue: {} },
        { provide: TradeRpcService, useValue: {} },
        { provide: PartnerGeneralLedgerService, useValue: {} },
        { provide: TransactionsService, useValue: {} },
        { provide: UserRpcService, useValue: {} },
        { provide: PolicyService, useValue: {} },
        { provide: AccountingNodeProxyPatternService, useValue: {} },
        { provide: JournalRecalculateProxyPatternService, useValue: {} },
      ],
    }).compile();

    service = await module.resolve<GeneralLedgerService>(GeneralLedgerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
