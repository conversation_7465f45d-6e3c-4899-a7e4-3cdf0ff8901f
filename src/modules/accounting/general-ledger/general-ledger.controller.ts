import {
  Controller,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  Post,
  Req,
  UseInterceptors,
  Logger,
} from '@nestjs/common';
import { GeneralLedgerService } from './general-ledger.service';
import { CreateGeneralLedgerDto } from './dto/create-general-ledger.dto';
import { Payload, MessagePattern, EventPattern } from '@nestjs/microservices';
import { ApiHeader, ApiOkResponse, ApiTags } from '@nestjs/swagger';

import { publicRpc } from '../../auth/guards/public-event.decorator';
import { Types } from 'mongoose';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';
import {
  GeneralLedgerRecalculateService,
  results,
} from './recalculate.service';

import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { GetGeneralLedgerDto } from './dto/get-general-ledger.dto';
import { GeneralLedger, journalTypes } from './schema/general-ledger.schema';
import { RecalculateDto } from './dto/recalculate.dto';
import { FindOneGeneralLedgerRpcDto } from './dto/find-one-rpc.dto';
import { FindGeneralLedgersByIdsRpcDto } from './dto/find-general-ledgers-by-ids-rpc.dto';
import { UpdateBulkJournalsRpcDto } from './dto/update-builk-journals-rpc.dto';
import { ValidateAccountingNodeDto } from './dto/validate-accounting-node.dto';
import { serviceName } from '../../policy/service-name.decorator';
import { PolicyInterceptor } from '../../policy/policy.interceptor';
import { DeleteAccountingJournalByIdsRpcDto } from './dto/delete-accounting-journal-by-ids-rpc.dto';
import { ApplyAdjustmentOrOverrideDto } from './dto/apply-adjustment-or-override.dto';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';
import { accountingMessagePattern } from '../../../utils/queues.enum';
import { RpcDto } from '../../../utils/dto/rpc.dto';
import { FindGeneralLedgersByIdRpcDto } from './dto/find-general-ledgers-by-id-rpc.dto';
import { JournalRecalculateProxyPatternService } from '../proxy-pattern/journal-recalculate-pattern.service';

@ApiTags('journal')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('journal')
export class GeneralLedgerController {
  constructor(
    private readonly journalService: GeneralLedgerService,
    private readonly journalRecalculateService: GeneralLedgerRecalculateService,
    private readonly journalRecalculateProxyPatternService: JournalRecalculateProxyPatternService,
  ) {}

  @Get('/calculate')
  get(@Query() query: RecalculateDto, @Req() request: RequestWithUser) {
    return this.journalRecalculateService.calculate({
      accounting_nodes: [query.accounting_node],
      branch: request?.headers['branch'],
      code: request?.user?.code,
    });
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'create', subject: 'journal' })
  @Post()
  async create(
    @Req() request: RequestWithUser,
    @Body() createJournalDto: CreateGeneralLedgerDto,
  ): Promise<GeneralLedger> {
    createJournalDto.by_user = request.user.user_id;
    return this.journalService.create(
      createJournalDto,
      true,
      request?.user?.code,
      new Types.ObjectId(request?.headers['branch']),
    );
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'journal' })
  @Get()
  findAll(@Query() query: GetGeneralLedgerDto): Promise<results> {
    const data = query;
    data.type = journalTypes.general;
    return this.journalService.findAll(query, undefined, undefined);
  }

  @ApiOkResponse({
    type: GeneralLedger,
    isArray: false,
  })
  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'journal' })
  @Get(':_id')
  async findOne(@Param('_id') _id: string): Promise<GeneralLedger> {
    const rs = await this.journalService.findOne({ _id });
    if (!rs)
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.journalNotFound),
        erpExceptionCode.journalNotFound,
      );
    return rs;
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.create_journal })
  async createRpc(@Payload() createJournalDto: CreateGeneralLedgerDto) {
    const { code, branch, ...rest } = createJournalDto;

    return await this.journalService.create(
      rest as any,
      true,
      code,
      new Types.ObjectId(branch),
    );
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.get_journal })
  async getJournal(@Payload() dto: RpcDto) {
    return await this.journalService.findAll(
      { branch: dto.branch },
      undefined,
      undefined,
    );
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.get_single_journal })
  async getOneJournal(@Payload() getJournalDto: FindOneGeneralLedgerRpcDto) {
    return await this.journalService.findOneCustom(getJournalDto as any);
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.get_journals })
  async getJournals(@Payload() getJournalDto: FindGeneralLedgersByIdsRpcDto) {
    return await this.journalService.findCustoms(getJournalDto);
  }
  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.get_aio_journal })
  async getAioJournals(@Payload() getJournalDto: FindGeneralLedgersByIdRpcDto) {
    const rs =
      await this.journalRecalculateProxyPatternService.calculateCurrentState(
        getJournalDto.document_id,
      );

    return rs;
  }

  @publicRpc()
  @EventPattern({ cmd: accountingMessagePattern.rollback_journal })
  async rollbackJournal(@Payload() payload: FindOneGeneralLedgerRpcDto) {
    await this.journalService.remove(
      payload.code,
      payload._id,
      true,
      payload.branch,
    );
    return payload;
  }

  @publicRpc()
  @EventPattern({ cmd: accountingMessagePattern.update_journal })
  async updateJournal(@Payload() payload) {
    await this.journalService.updateDoc(
      payload.code,
      payload._id,
      payload.documnet,
      payload.branch,
    );
    return payload;
  }

  @publicRpc()
  @EventPattern({ cmd: accountingMessagePattern.bulk_update_journals })
  async updateBulkJournals(@Payload() payload: UpdateBulkJournalsRpcDto) {
    await this.journalService.updateBulkJournals(
      payload.code,
      payload.data,
      payload.branch,
    );
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.recalculate_general_ledger })
  async recalculate(@Payload() payload: any) {
    const { items, jobId, code, request } = payload;
    let status;
    try {
      await this.journalRecalculateService.calculate({
        accounting_nodes: items,
        branch: new Types.ObjectId(request.headers.branch),
        code,
      });
      status = 'done';
    } catch (e) {
      Logger.log(e);
      status = 'error';
    } finally {
      await this.journalRecalculateService.updateJob(jobId, status, code);
    }
  }

  @publicRpc()
  @MessagePattern({
    cmd: accountingMessagePattern.check_accounting_node_transactions,
  })
  async checkAccountingNodeTransaction(
    @Payload() payload: ValidateAccountingNodeDto,
  ) {
    return await this.journalService.checkAccountingNodeTransactions(
      payload.accounting_node,
      payload.condition_type,
    );
  }

  @UseInterceptors(PolicyInterceptor)
  @serviceName(['document_policy', 'document_policy_status'])
  @publicRpc()
  @MessagePattern({
    cmd: accountingMessagePattern.accounting_journal_delete_by_ids,
  })
  async deleteByIds(
    @Payload()
    payload: DeleteAccountingJournalByIdsRpcDto,
  ) {
    const { code, policyData, branch, ...result } = payload;

    return await this.journalService.deleteByIds(
      code,
      result.data.ids,
      policyData,
      branch,
    );
  }

  @UseInterceptors(PolicyInterceptor)
  @serviceName(['document_policy', 'document_policy_status'])
  @publicRpc()
  @MessagePattern({
    cmd: accountingMessagePattern.apply_adjustment_or_override,
  })
  async updateReferenceJournals(
    @Payload() payload: ApplyAdjustmentOrOverrideDto,
  ) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { code, policyData, ...result } = payload as any;
    return await this.journalService.applyAdjustmentOrOverride(
      { _id: payload.data._id, type: payload.data.type },
      payload.data.updated_transactions,
      policyData,
      payload.data.user_id,
      code,
      payload.data.branch,
    );
  }
}
