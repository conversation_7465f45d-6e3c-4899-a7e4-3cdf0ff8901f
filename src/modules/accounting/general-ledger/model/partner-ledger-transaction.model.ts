import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { AccountingNodeModel } from '../../accounting-node/model/accounting-node.model';
import { GeneralLedgerModel } from './general-ledger.model';
import { UserModel } from '../../../users/users/model/users.model';
//import { PartnerModel } from '../../../trade/partners/model/partner.model';

@Table({
  tableName: 'partner_ledger_transactions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class PartnerLedgerTransactionModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => GeneralLedgerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  general_ledger_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  transaction_sequence: number;

  @ForeignKey(() => AccountingNodeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  accounting_node_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  code: string;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
  })
  credit: number;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
  })
  debit: number;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
  })
  current_balance: number;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
  })
  balance: number;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
  })
  before_balance: number;

  @ForeignKey(() => UserModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  by_user_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description: string;

  /*   @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  partner_id: number;
 */
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  is_beginning_balance: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // Relations
  @BelongsTo(() => AccountingNodeModel, 'accounting_node_id')
  accountingNode: AccountingNodeModel;

  /*   @BelongsTo(() => PartnerModel, 'partner_id')
  partner: PartnerModel; */

  @BelongsTo(() => GeneralLedgerModel, 'general_ledger_id')
  generalLedger: GeneralLedgerModel;

  @BelongsTo(() => UserModel, 'by_user_id')
  user: UserModel;
}
