import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { UserModel } from '../../../users/users/model/users.model';

@Table({
  tableName: 'general_ledgers',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class GeneralLedgerModel extends Model {
  @Column({
    type: DataType.BIGINT,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  // Polymorphic fields
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  referenceable_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  referenceable_type: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  note: string;

  @ForeignKey(() => UserModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  by_user_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  reference: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
  })
  transaction_date: Date;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  branch_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  is_beginning_balance: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  general_ledger_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // Relations
  @BelongsTo(() => UserModel, 'by_user_id')
  user: UserModel;

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;

  @BelongsTo(() => GeneralLedgerModel, 'branch_id')
  relatedLedger: GeneralLedgerModel;

  @HasMany(() => GeneralLedgerModel, 'general_ledger_id')
  generalLedgerTransactions: GeneralLedgerModel;

  @HasMany(() => GeneralLedgerModel, 'general_ledger_id')
  partnerLedgerTransactions: GeneralLedgerModel;
}
