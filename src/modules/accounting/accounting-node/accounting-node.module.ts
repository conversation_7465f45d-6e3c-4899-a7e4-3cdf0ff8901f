import { Module } from '@nestjs/common';
import { AccountingNodeService } from './accounting-node.service';
import { AccountingNodeController } from './accounting-node.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AccountingNode,
  accountingNodeSchema,
} from './schema/accounting-node.schema';
import { AccountingNodeTemplateModule } from '../accounting-node-template/accounting-node-template.module';
import { GeneralLedgerModule } from '../general-ledger/general-ledger.module';
import { ProxyPatternModule } from '../proxy-pattern/proxy-pattern.module';
import { RpcModule } from '../../rpc/rpc.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { AccountingNodeModel } from './model/accounting-node.model';
import { AccountingNodeRepository } from './repository/accounting-node.repository';

@Module({
  imports: [
    SequelizeModule.forFeature([AccountingNodeModel]),
    MongooseModule.forFeature([
      { name: AccountingNode.name, schema: accountingNodeSchema },
    ]),
    AccountingNodeTemplateModule,
    ProxyPatternModule,
    GeneralLedgerModule,
    RpcModule,
  ],
  controllers: [AccountingNodeController],
  providers: [AccountingNodeService, AccountingNodeRepository],
  exports: [AccountingNodeService, AccountingNodeRepository],
})
export class AccountingNodeModule {}
