import { Types } from 'mongoose';

export interface IAccountingNode {
  _id?: string | Types.ObjectId;
  id?;
  code: string;
  title?: string;
  group?: any;
  type?: string;
  branch?: Types.ObjectId | string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  invoice_party_type?: string;
  nodes?: any;
  createdAt?: string;
  updatedAt?: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  __v?: string;
  active?: boolean;
  hidden?: boolean;
  balance?: number;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  balance_type?: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  beginning_balance?: number;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  node_type?: string;
  static?: boolean;
  currency?: string;
}
