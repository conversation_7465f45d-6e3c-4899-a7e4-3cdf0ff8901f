import { Test, TestingModule } from '@nestjs/testing';
import { AccountingNodeController } from './accounting-node.controller';
import { AccountingNodeService } from './accounting-node.service';
import { AccountingNodeTemplateService } from '../accounting-node-template/accounting-node-template.service';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { getModelToken } from '@nestjs/mongoose';
import { AccountingNode } from './schema/accounting-node.schema';
import { PolicyService } from '../../policy/policy.service';
import { Policy } from '../../policy/schema/policy.schema';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { TenantRpcService } from '../../rpc/tenant-rpc.service';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { CaslAbilityFactory } from '../../casl/casl-ability.factory';

describe('AccountingNodeController', () => {
  let controller: AccountingNodeController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AccountingNodeController],
      providers: [
        AccountingNodeService,
        PolicyService,
        { provide: getModelToken(Policy.name), useValue: {} },
        {
          provide: AccountingNodeTemplateService,
          useValue: {},
        },
        {
          provide: getModelToken(AccountingNode.name),
          useValue: {},
        },
        {
          provide: GeneralLedgerService,
          useValue: {},
        },
        {
          provide: 'company',
          useValue: {},
        },
        {
          provide: 'Setting',
          useValue: {},
        },
        {
          provide: UserRpcService,
          useValue: {},
        },
        {
          provide: TradeRpcService,
          useValue: {},
        },
        {
          provide: TenantRpcService,
          useValue: {},
        },
        {
          provide: AccountingNodeProxyPatternService,
          useValue: {},
        },
        {
          provide: CaslAbilityFactory,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<AccountingNodeController>(AccountingNodeController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
