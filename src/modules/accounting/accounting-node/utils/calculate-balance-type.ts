import { nodeTypeEnum } from '../enums/node-type.enum';
import { accountingNodeType } from '../interfaces/accounting-node-type.enum';

export function getCreditOrDebit(nodeType: nodeTypeEnum) {
  const debitTypes = [
    // Current Assets
    'cash_on_hand',
    'cash_on_banks',
    'accounts_receivable',
    'inventories',
    'other_financial_assets_current',

    // Non-Current Assets
    'investments_in_associates',
    'investments_in_jointly_controlled_entities',
    'other_financial_assets_non_current',
    'property_plant_and_equipment',
    'investment_property',
    'intangible_assets',
    'biological_assets',

    // Direct Costs
    'cost_of_goods_sold',

    // Operating Expenses
    'salaries_and_wages',
    'rent_and_utilities',
    'marketing_and_advertising_expenses',
    'office_supplies_and_other_general_expenses',
    'depreciation',

    // Other Income and Expenses
    'interest_expense',

    // Income Tax Expenses
    'income_tax_expense',
    'other_income',
  ];

  const creditTypes = [
    // Current Liabilities
    'accounts_payable',
    'provisions_current',
    'other_financial_liabilities_current',

    // Non-Current Liabilities
    'provisions_non_current',
    'other_financial_liabilities_non_current',
    'deferred_tax_liabilities',

    // Equity
    'share_capital',
    'unallocated_earnings',
    'retained_earnings',
    'non_controlling_interest',
    'other_reserves',

    // Revenue
    'sales_revenue',
    'other_operating_revenue',

    // Other Income and Expenses
    'interest_income',
    'other_losses',
  ];

  if (debitTypes.includes(nodeType)) {
    return accountingNodeType.debit;
  } else if (creditTypes.includes(nodeType)) {
    return accountingNodeType.credit;
  } else {
    return undefined;
  }
}
