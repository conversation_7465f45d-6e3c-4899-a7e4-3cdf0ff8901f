import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { HttpException } from '@nestjs/common';
import { ImportAccountingNodeDto } from '../dto/import-accounting-node.dto';
import { generalExceptionCode } from '../../../../exceptions/exception-code.general';
import { nameOf } from '../../../../utils/object-key-name';

export async function validateBatch(items: any[], batchSize: number = 500) {
  let isValid = true;
  const errors = [];
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const transformedBatch = batch.map((item) =>
      plainToInstance(ImportAccountingNodeDto, item),
    );

    const batchErrors = await Promise.all(
      transformedBatch.map(async (item, index) => {
        const error = await validate(item, {
          validationError: { target: false },
        });

        if (error.length > 0) {
          // Capture the index within the overall `items` array
          return { index: i + index, errors: error };
        }
        return null;
      }),
    );

    batchErrors.forEach((error) => {
      if (error) {
        isValid = false;
        errors.push(error);
      }
    });
  }

  // eslint-disable-next-line @typescript-eslint/naming-convention
  if (!isValid) {
    throw new HttpException(
      {
        exceptionCode: nameOf(
          generalExceptionCode,
          (exception) => exception.badRequest,
        ),
        // eslint-disable-next-line @typescript-eslint/naming-convention
        response: { isValid, errors },
      },
      generalExceptionCode.badRequest,
    );
  }
  // eslint-disable-next-line @typescript-eslint/naming-convention
  return { isValid, errors };
}
