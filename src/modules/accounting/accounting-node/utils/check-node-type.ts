import { nodeTypeEnum } from '../enums/node-type.enum';
import { accountingNodeType } from '../interfaces/accounting-node-type.enum';

import { reportingTypeEnum } from '../interfaces/reporting-type.enum';
import { getCreditOrDebit } from './calculate-balance-type';
import { getAccountCategory } from './calculate-report-type';

export function checkAndFillNodeType(nodeType: nodeTypeEnum) {
  let balanceType: accountingNodeType;
  let reportingType: reportingTypeEnum;
  if (nodeType == nodeTypeEnum.cash_on_banks) {
  } else if (nodeType == nodeTypeEnum.cash_on_hand) {
  } else {
  }
  if (nodeType) {
    balanceType = getCreditOrDebit(nodeType);
    reportingType = getAccountCategory(nodeType);
  }
  return {
    balance_type: balanceType,
    reporting_type: reportingType,
  };
}
