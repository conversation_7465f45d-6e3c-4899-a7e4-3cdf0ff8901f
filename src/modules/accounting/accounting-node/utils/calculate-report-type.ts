import { nodeTypeEnum } from '../enums/node-type.enum';
import { reportingTypeEnum } from '../interfaces/reporting-type.enum';

export function getAccountCategory(
  accountType: nodeTypeEnum,
): reportingTypeEnum | null {
  const balanceSheetCategories = [
    // Balance Sheet Categories
    'cash_on_hand',
    'cash_on_banks',
    'accounts_receivable',
    'inventories',
    'other_financial_assets_current',
    'investments_in_associates',
    'investments_in_jointly_controlled_entities',
    'other_financial_assets_non_current',
    'property_plant_and_equipment',
    'investment_property',
    'intangible_assets',
    'biological_assets',
    'accounts_payable',
    'provisions_current',
    'other_financial_liabilities_current',
    'provisions_non_current',
    'other_financial_liabilities_non_current',
    'deferred_tax_liabilities',
    'share_capital',
    'unallocated_earnings',
    'retained_earnings',
    'non_controlling_interest',
    'other_reserves',
  ];

  const incomeStatementCategories = [
    // Invoice Statement Categories
    'cost_of_goods_sold',
    'salaries_and_wages',
    'rent_and_utilities',
    'marketing_and_advertising_expenses',
    'office_supplies_and_other_general_expenses',
    'depreciation',
    'interest_expense',
    'income_tax_expense',
    'other_income',
    'sales_revenue',
    'other_operating_revenue',
    'interest_income',
    'other_losses',
  ];

  if (balanceSheetCategories.includes(accountType)) {
    return reportingTypeEnum.balance_sheet;
  } else if (incomeStatementCategories.includes(accountType)) {
    return reportingTypeEnum.income_statement;
  } else {
    // Account type not found in any category
    return null;
  }
}
