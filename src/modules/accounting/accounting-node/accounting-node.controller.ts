import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
  Request,
  UploadedFile,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
} from '@nestjs/common';
import { MessagePattern, Payload, EventPattern } from '@nestjs/microservices';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiHeader,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Types } from 'mongoose';

import { AccountingNodeTemplateService } from '../accounting-node-template/accounting-node-template.service';
import { AccountingNodeService } from './accounting-node.service';
import { AccountingTreeDto } from './dto/accounting-tree.dto';
import {
  CreateAccountingNodeDto,
  CreateAccountingNodeRpcDto,
} from './dto/create-accounting-node.dto';
import { FindAllAccountingNodeDto } from './dto/find-all-accounting-node.dto';
import { UpdateAccountingNodeDto } from './dto/update-accounting-node.dto';
import { AccountingNode } from './schema/accounting-node.schema';
import { AccountingNodeWithMeta } from './interfaces/response-with-meta.interface';
import { publicRpc } from '../../auth/guards/public-event.decorator';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';
import { PolicyInterceptor } from '../../policy/policy.interceptor';
import { serviceName } from '../../policy/service-name.decorator';
import { FindAbstractCombined } from './dto/find-abstract.dto';
import { FindOneAccountingNodeRpcDto } from './dto/find-one-rpc.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { ImportAccountsDto } from './dto/import-accounts.dto';
import { PartnerToAccountDto } from './dto/partner-to-account.dto';
import { getOneAccountingNodeRpcDto } from './dto/get-accounting-node-code-rpc.dto';
import { FeatureAccessibilityGuard } from '../../casl/guards/feature-accessibility.guard';
import { feature } from '../../casl/guards/feature.decorator';
import { authType } from '../../casl/guards/auth-type.decorator';
import { ValidateAcnodeRpcDto } from './dto/validate-nodes.dto';
import { BranchHeaderDto } from '../../../utils/dto/request-headers.dto';
import { RequestHeaders } from '../../../utils/decorators/request-header.validator';
import {
  accountingMessagePattern,
  generalQueueMessagePattern,
} from '../../../utils/queues.enum';
import {
  acnodeType,
  parseNode,
} from '../../../utils/seeders/accounting-node-seeder';

@ApiTags('accounting-node')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  example: '64956ef5f506e9edf21df4e8',
  required: true,
})
@Controller('accounting-node')
export class AccountingNodeController {
  constructor(
    private readonly accountingNodeService: AccountingNodeService,
    private readonly accountingNodeTemplateService: AccountingNodeTemplateService,
  ) {}

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'accounting_node' })
  @abilities({ action: 'create', subject: 'accounting_node' })
  @UseInterceptors(PolicyInterceptor)
  @serviceName(['depth'])
  @Post()
  create(
    @Req() req: RequestWithUser,
    @Body() createAccountingNodeDto: CreateAccountingNodeDto,
  ): Promise<AccountingNode> {
    return this.accountingNodeService.create(req, createAccountingNodeDto);
  }

  @Post('/init')
  @ApiBody({
    type: CreateAccountingNodeDto,
    isArray: true,
  })
  async accountingnodeInit(
    @Body() createAccountingNodeDto: acnodeType[],
  ): Promise<boolean> {
    const result = parseNode(createAccountingNodeDto);
    return this.accountingNodeService.insertMany(
      result as CreateAccountingNodeDto[],
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'accounting_node' })
  @abilities({ action: 'create', subject: 'accounting_node' })
  @Post('import-file')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async importXlsx(
    @Body() data: ImportAccountsDto,
    @Req() req: RequestWithUser,
    @UploadedFile(
      'file',
      new ParseFilePipe({
        validators: [
          // eslint-disable-next-line @typescript-eslint/naming-convention
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 30 }),
          // eslint-disable-next-line @typescript-eslint/naming-convention
          new FileTypeValidator({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            fileType:
              /^(text\/csv|application\/vnd\.ms-excel|application\/vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet)$/i,
          }),
        ],
      }),
    )
    file: Express.Multer.File,
  ) {
    await this.accountingNodeService.importExcel(file, data, req.user.code);
    return { status: 'done' };
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'accounting_tree' })
  @abilities({ action: 'read', subject: 'accounting_node_tree' })
  @authType('both')
  @ApiBearerAuth('TOKEN')
  @Get('/tree')
  accountingTree(
    @Query() query: AccountingTreeDto,
    @Request() req: RequestWithUser,
  ) {
    return this.accountingNodeService.accountingTree(query, undefined, req);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'accounting_node' })
  @abilities({ action: 'list', subject: 'accounting_node' })
  @ApiOkResponse({
    description: 'The accountingnode records',
    type: AccountingNodeWithMeta,
    isArray: false,
  })
  @Get('/')
  findAll(
    @RequestHeaders() header: BranchHeaderDto,
    @Query() query: FindAllAccountingNodeDto,
  ): Promise<AccountingNodeWithMeta> {
    query.branch = header.branch;
    return this.accountingNodeService.findAll(query);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'abstract_accounting_node' })
  @Get('abstract')
  abstract(@RequestHeaders() header: BranchHeaderDto) {
    return this.accountingNodeService.abstract(header.branch);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'abstract_accounting_node' })
  @Get('abstract-combined')
  abstractCombined(
    @Req() req: RequestWithUser,
    @RequestHeaders() header: BranchHeaderDto,
    @Query() query: FindAbstractCombined,
  ) {
    return this.accountingNodeService.abstractCombined(
      req,
      query,
      header.branch,
    );
  }

  @Get('/seed')
  seed() {
    return this.accountingNodeService.seeder();
  }

  @Get('/onboarding-nodes')
  async onBoardingNodes() {
    return this.accountingNodeService.getParent();
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'accounting_node' })
  @abilities({ action: 'read', subject: 'accounting_node' })
  @Get('/details/:id')
  accountDetails(
    @Param('id') _id: string,
    @RequestHeaders() header: BranchHeaderDto,
    @Request() req: RequestWithUser,
  ) {
    return this.accountingNodeService.calculateAccountingJournalingForAccountingNode(
      _id,
      header.branch,
      req,
    );
  }

  @ApiOkResponse({
    description: 'The one accountingNode records',
    type: AccountingNode,
    isArray: false,
  })
  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'accounting_node' })
  @abilities({ action: 'read', subject: 'accounting_node' })
  @Get(':id')
  findOne(
    @Param('id') _id: string,
    @Req() req: RequestWithUser,
  ): Promise<AccountingNode> {
    return this.accountingNodeService.findOneGet(
      {
        _id: new Types.ObjectId(_id),
      },
      req,
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'accounting_node' })
  @abilities({ action: 'edit', subject: 'accounting_node' })
  @ApiOkResponse({
    description: 'The one accountingnode records',
    type: AccountingNode,
    isArray: false,
  })
  @Patch(':id')
  @UseInterceptors(PolicyInterceptor)
  @serviceName(['depth'])
  async update(
    @Param('id') _id: string,
    @Req() req: RequestWithUser,
    @RequestHeaders() headers: BranchHeaderDto,
    @Body() updateAccountingNodeDto: UpdateAccountingNodeDto,
  ): Promise<any> {
    return await this.accountingNodeService.update(
      new Types.ObjectId(_id),
      headers.branch,
      updateAccountingNodeDto,
      req,
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'accounting_node' })
  @abilities({ action: 'delete', subject: 'accounting_node' })
  @ApiOkResponse({
    description: 'The one accountingnode records',
    type: AccountingNode,
    isArray: false,
  })
  @Delete(':id')
  remove(@Param('id') _id: string) {
    return this.accountingNodeService.remove(new Types.ObjectId(_id));
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.populate_account })
  async populateAccount(@Payload() data: object) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { code, headers, branch, ...result } = data as any;

    for (let index = 0; index < Object.keys(result).length; index++) {
      const element = Object.keys(result)[index];
      if (!Types.ObjectId.isValid(result[element])) {
        result[element] = null;
      } else {
        result[element] = await this.accountingNodeService.findOneBranch(
          {
            _id: result[element],
          },
          //  new Types.ObjectId(branch),
        );
      }
    }
    return result;
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.accounting_node_create })
  @UseInterceptors(PolicyInterceptor)
  @serviceName(['depth'])
  async createRpc(@Payload() data: CreateAccountingNodeRpcDto) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { code, policyData, ...result } = data;
    return await this.accountingNodeService.createRpc(result, policyData);
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.get_single_accounting_node })
  findOneRpc(
    @Payload()
    query: FindOneAccountingNodeRpcDto,
  ): Promise<AccountingNode> {
    return this.accountingNodeService.findOneBranch(
      { _id: query._id },
      //   query.branch,
    );
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.has_any_accounting_node })
  hasAny(): Promise<boolean> {
    return this.accountingNodeService.hasAny();
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.abstract_accounting_node })
  abstractRpc(@Payload() data: BranchHeaderDto) {
    return this.accountingNodeService.abstract(data.branch);
  }

  @publicRpc()
  @EventPattern({ cmd: accountingMessagePattern.add_partner_to_account })
  addPartnerFromAccount(@Payload() dto: PartnerToAccountDto) {
    return this.accountingNodeService.addPartnerToAccount(dto);
  }

  @publicRpc()
  @EventPattern({ cmd: accountingMessagePattern.remove_partner_from_account })
  removePartnerFromAccount(@Payload() dto: PartnerToAccountDto) {
    return this.accountingNodeService.removePartnerToAccount(dto);
  }

  @EventPattern(generalQueueMessagePattern.tenent_created_event)
  async handleMessagePrinted(@Payload() data) {
    const rs: CreateAccountingNodeDto[] =
      (await this.accountingNodeTemplateService.findAllRaw({
        template: data.accountingTemplate,
      })) as CreateAccountingNodeDto[];
    for (let index = 0; index < rs.length; index++) {
      const newId = new Types.ObjectId();
      const oldId = rs[index]._id;
      rs[index]._id = newId;
      if (rs[index].code.length === 3 && (index + 1) % 2 != 0) {
        data.is_parent = false;
      }
      rs[index].static = true;
      rs[index].hidden = false;
      rs[index].active = true;
      for (let index2 = 1; index2 < rs.length; index2++) {
        if (String(oldId) == String(rs[index2].group)) {
          rs[index2].group = newId;
        }
      }
    }

    await this.accountingNodeService.insertMany(
      rs as CreateAccountingNodeDto[],
    );
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.fill_ac_node })
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async fillAccountingNodes(@Payload() data) {
    return await this.accountingNodeService.seeder(true, false);
  }

  @publicRpc()
  @MessagePattern({
    cmd: accountingMessagePattern.abstract_accounting_node_code,
  })
  async getAccountingNodeByCode(@Payload() data: getOneAccountingNodeRpcDto) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/naming-convention
    const { code, branch, ac_code } = data;
    const rs = await this.accountingNodeService.getOneAcNodeWithCode(ac_code);
    return rs;
  }

  @publicRpc()
  @MessagePattern({
    cmd: accountingMessagePattern.validate_nodes,
  })
  async validateAcNodes(@Payload() data: ValidateAcnodeRpcDto) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/naming-convention
    const { code, branch, ids } = data;

    const rs = await this.accountingNodeService.validateAcNodes(ids);
    return rs;
  }
}
