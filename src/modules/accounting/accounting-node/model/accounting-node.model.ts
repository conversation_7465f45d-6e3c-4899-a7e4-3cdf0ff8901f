import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasMany,
  HasOne,
  BelongsTo,
} from 'sequelize-typescript';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { GeneralLedgerTransactionModel } from '../../general-ledger/model/general-ledger-transaction.model';
import { BeginningBalanceModel } from '../../temp-beginning-balance/model/beginning-balance.model';
import { PartnerModel } from '../../../trade/partners/model/partner.model';
import { PaymentTypeModel } from '../../../../modules/users/payment-types/model/payment-type.model';
import { nodeTypeEnum } from '../enums/node-type.enum';

@Table({
  tableName: 'accounting_nodes',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class AccountingNodeModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  code: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name_ar: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  is_parent: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  active: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  hidden: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  balance_type: string;

  @ForeignKey(() => AccountingNodeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  parent_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  type: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  reporting_type: string;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  branch_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  is_static: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  invoice_party_type: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  invoice_party_id: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: true,
    defaultValue: 0,
  })
  balance: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: true,
    defaultValue: 0,
  })
  beginning_balance: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: 'SAR',
  })
  currency: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
    defaultValue: 0,
  })
  node_qty: number;

  @Column({
    type: DataType.ENUM(...Object.values(nodeTypeEnum)),
    allowNull: true, // or true, depending on your logic
  })
  node_type: nodeTypeEnum;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @BelongsTo(() => AccountingNodeModel, 'partner_id')
  parent: AccountingNodeModel;

  @HasMany(() => AccountingNodeModel, 'partner_id')
  children: AccountingNodeModel[];

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;

  @HasMany(() => PartnerModel, 'accounting_node_id')
  partners: PartnerModel[];

  @HasOne(() => BeginningBalanceModel, 'accounting_node_id')
  beginningBalance: BeginningBalanceModel;

  @HasMany(() => GeneralLedgerTransactionModel, 'accounting_node_id')
  generalLedgerTransactions: GeneralLedgerTransactionModel[];

  @HasMany(() => PaymentTypeModel)
  paymentTypes: PaymentTypeModel[];
}
