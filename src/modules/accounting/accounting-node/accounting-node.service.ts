import { HttpException, HttpStatus, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { AccountingTreeDto } from './dto/accounting-tree.dto';
import {
  CreateAccountingNodeDto,
  CreateAccountingNodeRpcDto,
} from './dto/create-accounting-node.dto';
import { FindAllAccountingNodeDto } from './dto/find-all-accounting-node.dto';
import { FindOneAccountingNodeDto } from './dto/find-one-accounting-node.dto';
import { UpdateAccountingNodeDto } from './dto/update-accounting-node.dto';
import {
  AccountingNode,
  AccountingNodeDocument,
} from './schema/accounting-node.schema';
import { IAccountingNode } from './interfaces/accounting.interface';
import { AccountingNodeWithMeta } from './interfaces/response-with-meta.interface';

import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { FindAccountingNodeDto } from './dto/find-accounting-node.dto';
import { extend } from 'lodash';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { invoicePartyType } from './interfaces/invoice-party-type.enum';
import { accountingTypeEnum } from './interfaces/accounting-type.enum';
import { accountingNodeType } from './interfaces/accounting-node-type.enum';
import {
  abstractTypeEnum,
  FindAbstractCombined,
} from './dto/find-abstract.dto';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { checkAndFillNodeType } from './utils/check-node-type';
import * as XLSX from 'xlsx';
import { ImportAccountsDto } from './dto/import-accounts.dto';
import { PartnerToAccountDto } from './dto/partner-to-account.dto';
import { validateBatch } from './utils/validate-batch';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { paginate } from '../../../utils/dto/pagination.dto';
import { arrayToTree } from '../../../utils/array-to-tree';
import {
  nodeSeeder,
  acnodeType,
} from '../../../utils/seeders/accounting-node-seeder';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';
import { generalExceptionCode } from '../../../exceptions/exception-code.general';
import { digitFill } from '../../../utils/digit';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class AccountingNodeService {
  constructor(
    @InjectModel(AccountingNode.name)
    private accountingNodeModel: Model<AccountingNode>,
    private generalLedgerService: GeneralLedgerService,
    private readonly tradeRpcService: TradeRpcService,
    private readonly userRpcService: UserRpcService,
    private readonly accountingNodeProxyPatternService: AccountingNodeProxyPatternService,
  ) {}

  async create(
    req: RequestWithUser,
    createAccountingNodeDto: CreateAccountingNodeDto,
  ): Promise<AccountingNode> {
    if (!createAccountingNodeDto.type) {
      createAccountingNodeDto.type = accountingTypeEnum.general;
    }
    const nodeDepth = req.policyData.depth;
    const parentData = await this.accountingNodeProxyPatternService.findOne(
      {
        _id: createAccountingNodeDto.group,
      },
      new Types.ObjectId(req?.headers['branch']),
    );
    if (createAccountingNodeDto.group) {
      if (!parentData?.is_parent) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.addNodeToLeaf),
          erpExceptionCode.addNodeToLeaf,
        );
      }
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { balance_type, reporting_type } = checkAndFillNodeType(
        createAccountingNodeDto.node_type,
      );

      createAccountingNodeDto.balance_type = balance_type;
      createAccountingNodeDto.reporting_type = reporting_type;

      parentData.nodeQty += 1;
      createAccountingNodeDto.code = `${parentData.code}${digitFill(
        nodeDepth,
        Number(createAccountingNodeDto.code) || parentData.nodeQty,
      )}`;
    } else {
      if (!(Number(createAccountingNodeDto.code) || parentData?.nodeQty)) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.accountingCodeMissing),
          erpExceptionCode.accountingCodeMissing,
        );
      }
      createAccountingNodeDto.is_parent = true;
      createAccountingNodeDto.code = `${parentData?.code || ''}${digitFill(
        nodeDepth,
        Number(createAccountingNodeDto.code) || parentData.nodeQty,
      )}`;
    }
    const codeExist = await this.accountingNodeProxyPatternService.findOne(
      {
        code: createAccountingNodeDto.code,
      },
      req?.headers['branch'],
    );
    if (codeExist) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.accountingCodeExist),
        erpExceptionCode.accountingCodeExist,
      );
    }
    if (parentData) {
      await parentData.save();
    }

    return (
      await new this.accountingNodeModel(createAccountingNodeDto).save()
    ).populate(['group']);
  }

  async createRpc(
    createAccountingNodeDto: CreateAccountingNodeRpcDto,
    policyData: any,
  ): Promise<AccountingNode> {
    const nodeDepth = policyData.depth;
    const parentData = await this.accountingNodeProxyPatternService.findOne(
      {
        _id: createAccountingNodeDto.group,
      },
      createAccountingNodeDto.branch,
    );
    if (createAccountingNodeDto.group) {
      if (!parentData?.is_parent) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.addNodeToLeaf),
          erpExceptionCode.addNodeToLeaf,
        );
      }
      createAccountingNodeDto.balance_type = parentData.balance_type;
      parentData.nodeQty += 1;
      createAccountingNodeDto.code = `${parentData.code}${digitFill(
        nodeDepth,
        Number(createAccountingNodeDto.code) || parentData.nodeQty,
      )}`;
    } else {
      if (!(Number(createAccountingNodeDto.code) || parentData?.nodeQty)) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.accountingCodeMissing),
          erpExceptionCode.accountingCodeMissing,
        );
      }
      createAccountingNodeDto.is_parent = true;
      createAccountingNodeDto.code = `${parentData?.code || ''}${digitFill(
        nodeDepth,
        Number(createAccountingNodeDto.code) || parentData.nodeQty,
      )}`;
    }
    const codeExist = await this.accountingNodeProxyPatternService.findOne(
      {
        code: createAccountingNodeDto.code,
      },
      createAccountingNodeDto.branch,
    );
    if (codeExist) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.accountingCodeExist),
        erpExceptionCode.accountingCodeExist,
      );
    }
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { balance_type, reporting_type } = checkAndFillNodeType(
      createAccountingNodeDto.node_type,
    );

    createAccountingNodeDto.balance_type = balance_type;
    createAccountingNodeDto.reporting_type = reporting_type;

    if (parentData) {
      await parentData.save();
    }
    return (
      await new this.accountingNodeModel(createAccountingNodeDto).save()
    ).populate(['group']);
  }

  async findAll(
    pagination: FindAllAccountingNodeDto,
  ): Promise<AccountingNodeWithMeta> {
    const { limit, page, queries, branch, ...rest } = pagination;
    const orActions = [];
    orActions.push({
      $or: [{ branch: { $exists: false } }, { branch }],
    });
    if (rest.node_type?.length > 0) {
      orActions.push({
        $or: rest.node_type?.map((data) => {
          return { node_type: data };
        }),
      });
    }

    const databaseQuerys = rest || ({} as any);
    databaseQuerys.hidden = false;

    if (queries) {
      const searchText = { $regex: new RegExp(queries), $options: 'i' };
      orActions.push({
        $or: [
          {
            'name.ar': searchText,
          },
          {
            'name.en': searchText,
          },
          {
            code: searchText,
          },
        ],
      });
    }
    if (orActions.length > 0) {
      databaseQuerys.$and = [...orActions];
    }
    const result = (await this.accountingNodeModel
      .find(databaseQuerys)
      .skip((page - 1) * limit)
      .populate('group')
      .limit(limit)
      .select('name balance code')
      .lean()) as AccountingNode[];

    const meta = await paginate(
      limit,
      page,
      this.accountingNodeModel,
      databaseQuerys,
    );

    const finalResult = result.map((accountingNode) => {
      if (accountingNode.balance_type === accountingNodeType.credit) {
        accountingNode.balance = -1 * accountingNode.balance;
      }
      return accountingNode;
    });

    return { result: finalResult, meta };
  }

  async abstract(branch: Types.ObjectId) {
    const orActions = [];
    orActions.push({
      $or: [{ branch: { $exists: false } }, { branch }],
    });
    const result = await this.accountingNodeModel
      .find({ active: true, hidden: false, ...orActions[0] })
      .lean();

    const finalResult = result.map((accountingNode) => {
      if (accountingNode.balance_type === accountingNodeType.credit) {
        accountingNode.balance = -1 * accountingNode.balance;
      }
      return accountingNode;
    });

    return { result: finalResult };
  }

  async abstractCombined(
    req: RequestWithUser,
    query: FindAbstractCombined,
    branch: Types.ObjectId,
  ) {
    const orActions = [];
    orActions.push({
      $or: [{ branch: { $exists: false } }, { branch }],
    });
    const response = [];
    if (
      query.type == abstractTypeEnum.account ||
      query.type == abstractTypeEnum.all
    ) {
      const accountingNodeList = await this.accountingNodeModel
        .find({
          active: true,
          is_parent: false,
          hidden: false,
          ...orActions[0],
        })
        .select('code _id name balance balance_type');
      response.push(...accountingNodeList);
    }
    if (
      query.type == abstractTypeEnum.customer ||
      query.type == abstractTypeEnum.vendor ||
      query.type == abstractTypeEnum.all
    ) {
      let partnerList = await this.tradeRpcService.getPartnersAbstract(
        req?.user?.code,
        [abstractTypeEnum.customer, abstractTypeEnum.vendor],
      );
      partnerList = partnerList.map(async (partner) => {
        // const onePartner = await this.accountingNodeModel
        //   .findOne({
        //     _id: partner.accounting_info.general_account,
        //   })
        //   .select('balance balance_type');
        return {
          code: String(partner.number),
          _id: partner._id, // need for frontend
          name: partner.name,
          email: partner.email,
          mobile: partner.mobile,
          balance: partner.accounting_info.balance,
          balance_type: partner.type === 'customer' ? 'debit' : 'credit',
          partner_type: partner.type,
          is_partner: true,
          partner_id: partner._id,
        };
      });
      partnerList = await Promise.all(partnerList);
      response.push(...partnerList);
    }
    return response;
  }

  async hasAny(): Promise<boolean> {
    const nodeCount = await this.accountingNodeModel.countDocuments();
    const hasAny = nodeCount > 0 ? true : false;
    return hasAny;
  }

  async accountingTree(
    pagination: AccountingTreeDto,
    allAcNodes: boolean = true,
    req: RequestWithUser,
  ) {
    const showDisabledAccount = pagination.show_disabled_account;
    const showHiddenAccount = pagination.show_hidden_account;
    let query = {
      active: true,
      hidden: false,
    };
    if (showDisabledAccount) {
      delete query.active;
    }
    if (showHiddenAccount) {
      delete query.hidden;
    }
    const orActions = { $or: [] };
    if (pagination.branch && allAcNodes == false) {
      orActions.$or.push({ branch: { $exists: false } });
      orActions.$or.push({ branch: pagination.branch });
    }
    if (orActions.$or.length > 0) {
      query = { ...query, ...orActions };
    }
    const nodes = await this.accountingNodeModel
      .find(query)
      .populate(
        'group',
        'name balance_type branch code nodes nodesQty group type active is_parent',
      )
      .select(
        'name balance_type branch code nodes nodesQty group type active is_parent',
      )
      .lean();
    for (let index = 0; index < nodes.length; index++) {
      const element = nodes[index];
      if (Types.ObjectId.isValid(element.branch)) {
        const branchInfo = await this.userRpcService.getOneBranch(
          req.user.code,
          element.branch as Types.ObjectId,
        );
        element.branch = {
          ...branchInfo.general_information.name,
          _id: element.branch,
        };
      }
    }
    return this.createNodeRo(nodes);
    // return nodes;
  }

  //parent id should be passed as objectid

  async createNodeRo(nodes) {
    //1. no parents
    //2.find childs of this parent
    //3 if dont has parent its leaves

    const resp = [];
    for (let index = 0; index < nodes.length; index++) {
      nodes[index].id = nodes[index]._id;
      const element = nodes[index];
      //element.createdAt
      delete element.updatedAt;
      delete element.__v;
      if (!element.group) {
        resp.push(element);
      }
    }

    for (let index = 0; index < nodes.length; index++) {
      const element = nodes[index];
      if (element.group?._id) {
        const tree = arrayToTree(nodes, element.group._id);

        const indexParent = resp.findIndex((data) => {
          return String(data._id) == String(element.group._id);
        });
        if (indexParent > -1) {
          resp[indexParent].nodes = tree.length > 0 ? tree : null;
        }
      }
    }
    // const rs = arrayToTree(nodes, 0);
    // accountingNodes .push(rs)

    // eslint-disable-next-line @typescript-eslint/naming-convention
    return { docs: resp, nodeCount: nodes.length };
  }

  async findOneBranch(
    query: FindOneAccountingNodeDto,
  ): Promise<AccountingNodeDocument> {
    return await this.accountingNodeModel
      .findOne({ ...query })
      .populate(['group']);
  }

  async findOneGet(
    query: FindOneAccountingNodeDto,
    req: RequestWithUser,
  ): Promise<AccountingNode> {
    const rs = await this.accountingNodeModel
      .findOne({ ...query })
      .populate(
        'group',
        'name balance_type branch code nodes nodesQty group type active is_parent',
      )
      .select(
        'name balance_type branch code nodes nodesQty group type active is_parent',
      );
    if (Types.ObjectId.isValid(rs.branch)) {
      const branchInfo = await this.userRpcService.getOneBranch(
        req.user.code,
        rs.branch as Types.ObjectId,
      );
      rs.branch = {
        ...branchInfo.general_information.name,
        _id: rs.branch,
      };
    }
    return rs;
  }

  async findByIds(query: FindAccountingNodeDto) {
    return await this.accountingNodeModel
      .find({
        _id: { $in: query.ids },
        invoice_party_type: query.invoice_party_type,
        type: query.type,
      })
      .populate(['group']);
  }

  async seeder(freshSeed: boolean = false, seedjournal: boolean = true) {
    await this.accountingNodeModel.deleteMany({});
    const data = nodeSeeder(undefined, freshSeed);
    const accountingNodes = await this.accountingNodeModel.insertMany(data);

    const leafs = data.filter(
      (accountingNodeLeaf) => accountingNodeLeaf.is_parent === false,
    );
    if (seedjournal) {
      const updatedLeafs = await this.generalLedgerService.seeder(leafs);
      const bulkOperations = [];
      for (const updatedLeaf of updatedLeafs) {
        bulkOperations.push({
          updateOne: {
            filter: { _id: updatedLeaf._id },
            update: {
              $set: {
                beginning_balance: updatedLeaf.beginning_balance,
                balance: updatedLeaf.balance,
              },
            },
          },
        });
      }
      await this.accountingNodeModel.bulkWrite(bulkOperations);
    }
    return accountingNodes;
  }

  async update(
    _id: Types.ObjectId,
    branch: Types.ObjectId,
    updateAccountingNodeDto: UpdateAccountingNodeDto,
    req: RequestWithUser,
  ): Promise<any> {
    const orActions = [];
    orActions.push({
      $or: [{ branch: { $exists: false } }, { branch }],
    });
    const rs = await this.accountingNodeModel
      .findOne({ _id /* ...orActions[0]  */ })
      .populate(['group']);
    if (!rs) {
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.notFound),
        generalExceptionCode.notFound,
      );
    }
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { balance_type, reporting_type } = checkAndFillNodeType(
      updateAccountingNodeDto.node_type,
    );

    updateAccountingNodeDto.balance_type = balance_type;
    updateAccountingNodeDto.reporting_type = reporting_type;
    const generalLedgerData = await this.generalLedgerService.findOne({
      'transactions.accountingNode': _id,
    });
    if (String(updateAccountingNodeDto.branch) !== String(rs.branch)) {
      if (generalLedgerData) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.leafHasTransactions),
          erpExceptionCode.leafHasTransactions,
        );
      }
    }
    if (updateAccountingNodeDto.is_parent !== rs.is_parent) {
      if (updateAccountingNodeDto.is_parent == true && generalLedgerData) {
        // exception
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.leafHasTransactions),
          erpExceptionCode.leafHasTransactions,
        );
      }
      if (updateAccountingNodeDto.is_parent == false) {
        const child = await this.accountingNodeModel.findOne({ group: rs._id });

        if (child) {
          throw new HttpException(
            nameOf(erpExceptionCode, (x) => x.nodeHasChild),
            erpExceptionCode.nodeHasChild,
          );
        }
      }
    }
    if (rs.static == true) {
      throw new HttpException('static account ', HttpStatus.FORBIDDEN);
    }
    let groupAccount: any = rs.group;
    // new group
    if (
      updateAccountingNodeDto.group &&
      String(updateAccountingNodeDto.group) !== String(rs.group)
    ) {
      const child = await this.accountingNodeModel.findOne({ group: rs._id });

      if (child) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.nodeHasChild),
          erpExceptionCode.nodeHasChild,
        );
      }

      groupAccount = await this.accountingNodeModel.findOne({
        _id: updateAccountingNodeDto.group,
        is_parent: true,
      });
      groupAccount.nodeQty += 1;
      await groupAccount.save();
    }
    if (updateAccountingNodeDto.code || groupAccount?.nodeQty) {
      const nodeDepth = req.policyData.depth;
      updateAccountingNodeDto.code = `${groupAccount?.code || ''}${digitFill(
        nodeDepth,
        Number(updateAccountingNodeDto.code || groupAccount.nodeQty),
      )}`;
      const codeExist = await this.accountingNodeProxyPatternService.findOne(
        {
          code: updateAccountingNodeDto.code,
        },
        branch,
      );
      if (codeExist && String(codeExist._id) !== String(rs._id)) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.accountingCodeExist),
          erpExceptionCode.accountingCodeExist,
        );
      }
    }
    extend(rs, updateAccountingNodeDto);
    await rs.save();
    return rs;
  }

  async remove(_id: Types.ObjectId) {
    const parentData = await this.accountingNodeModel
      .find({ group: _id })
      .countDocuments();

    if (parentData > 0) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.nodeHasChild),
        erpExceptionCode.nodeHasChild,
      );
    }
    const nodeData = await this.findOneBranch({ _id });
    if (!nodeData) {
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.notFound),
        generalExceptionCode.notFound,
      );
    }
    if (nodeData.static == true) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.staticNodes),
        erpExceptionCode.staticNodes,
      );
    }
    const generalLedgerData = await this.generalLedgerService.findOne({
      'transactions.accountingNode': nodeData._id,
    });
    if (generalLedgerData) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.leafHasTransactions),
        erpExceptionCode.leafHasTransactions,
      );
    }
    return this.accountingNodeModel.deleteOne({ _id });
  }

  async findAllRaw(
    ids?: any[],
    type?: accountingTypeEnum[],
    invoice_party_type?: invoicePartyType,
    group?: string | Types.ObjectId,
    search?: string,
    branch?: Types.ObjectId,
    allAcNodes: boolean = false,
    nodeTypes?: string[],
    hasRelatedPartners: boolean = false,
  ): Promise<IAccountingNode[]> {
    let query: {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      is_parent: boolean;
      _id?: any;
      type?: accountingTypeEnum[];
      // eslint-disable-next-line @typescript-eslint/naming-convention
      invoice_party_type?: invoicePartyType;
      group?: string | Types.ObjectId;
      search?: string;
      branch?: Types.ObjectId;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      node_type?: any;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      related_partners?: any;
    } = {
      is_parent: false,
    };
    const orActions = { $or: [] };

    if (ids && ids.length > 0) {
      query._id = { $in: ids };
    }

    if (nodeTypes && nodeTypes.length > 0) {
      query.node_type = { $in: nodeTypes };
    }

    if (type && type.length > 0) {
      query.type = type;
    }

    if (invoice_party_type) {
      query.invoice_party_type = invoice_party_type;
    }

    if (hasRelatedPartners) {
      query.related_partners = { $size: 0 };
    }

    if (group) {
      query.group = group;
    }

    if (branch && allAcNodes === false) {
      orActions.$or.push({ branch: { $exists: false } });
      orActions.$or.push({ branch: branch });
    }

    if (search) {
      const searchText = { $regex: new RegExp(search), $options: 'i' };
      (query as any).$or = [
        { 'name.ar': searchText },
        { 'name.en': searchText },
        { code: searchText },
      ];
    }
    if (orActions.$or.length > 0) {
      query = { ...query, ...orActions };
    }
    return (await this.accountingNodeModel.find(query)) as IAccountingNode[];
  }

  async insertMany(data: CreateAccountingNodeDto[]): Promise<boolean> {
    // TODO: check is there ant transaction for existed accounts
    await this.accountingNodeModel.deleteMany({});
    await this.accountingNodeModel.insertMany(data);
    return true;
  }

  async calculateAccountingJournalingForAccountingNode(
    _id: string | Types.ObjectId,
    branch: Types.ObjectId,
    req: RequestWithUser,
  ) {
    const accountingNode = await this.accountingNodeModel.findOne({
      _id,
    });

    if (accountingNode.is_parent === false) {
      return await this.generalLedgerService.journalAccountingDetails(_id);
    }

    const accountTreeArray = await this.accountingTree({}, undefined, req);

    const foundAccountingNode = this.findAccountingNodeTreeById(
      accountTreeArray.docs,
      _id,
    );

    //get the accounting leafs with is_parent == false
    const accountingNodeLeafs = this.findAccountingNodeLeaf([
      foundAccountingNode,
    ]);

    const accountingNodeIds = accountingNodeLeafs
      .flat(Infinity)
      .map((accountingNode) => accountingNode._id);

    return await this.generalLedgerService.journalAccountingDetailsByIds(
      accountingNodeIds,
      accountingNode.balance_type,
    );
  }

  private findAccountingNodeTreeById(accountTreeArray, id) {
    if (!Array.isArray(accountTreeArray)) {
      return accountTreeArray;
    }

    for (const node of accountTreeArray) {
      if (String(node._id) === String(id)) {
        return node;
      }

      const foundInChildren = this.findAccountingNodeTreeById(node.nodes, id);
      if (foundInChildren) {
        return foundInChildren;
      }
    }
    return null;
  }

  private findAccountingNodeLeaf(accountTreeArray) {
    const leafNodes = [];

    if (!Array.isArray(accountTreeArray)) {
      leafNodes.push(accountTreeArray);
      return;
    }

    for (const node of accountTreeArray) {
      if (node.is_parent === false) {
        leafNodes.push(node);
      }

      const foundInChildren = this.findAccountingNodeLeaf(node.nodes);
      if (foundInChildren) {
        leafNodes.push(foundInChildren);
      }
    }
    return leafNodes;
  }

  public async addBeginningBalance(bulkOperations: any) {
    if (bulkOperations.length > 0) {
      return await this.accountingNodeModel.bulkWrite(bulkOperations);
    }
    return true;
  }

  private template() {
    const data = nodeSeeder(undefined);
    return data;
  }

  public getParent() {
    const acNodes = this.template();
    const rs = acNodes.filter((acNode) => {
      return acNode.is_parent === true && !acNode.group;
    });
    return [
      {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        templateName: 'Accounting Node',
        description: 'this is simple accounting node ',
        nodes: rs,
      },
    ];
  }

  async importExcel(
    fileObject: Express.Multer.File,
    body: ImportAccountsDto,
    code: number,
  ) {
    //if has ledger
    const generalLedgerData = await this.generalLedgerService.findOne({});
    if (generalLedgerData) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.leafHasTransactions),
        erpExceptionCode.leafHasTransactions,
      );
    }
    if (!fileObject) {
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.notFound),
        generalExceptionCode.notFound,
      );
    }

    const workbook = XLSX.read(fileObject.buffer);
    const sheetNameList = workbook.SheetNames;
    const sheetArray = XLSX.utils.sheet_to_json(
      workbook.Sheets[sheetNameList[0]],
    );

    const parsedData = this.parseNode(sheetArray as acnodeType[], body);

    await validateBatch(parsedData);

    const rs = await this.insertMany(parsedData);
    await this.userRpcService.cleanBranch(code);
    await this.userRpcService.dropAllPaymentTypes(code);
    await this.tradeRpcService.dropAllPartners(code);
    await this.tradeRpcService.dropAllExpenses(code);
    return rs;
  }

  parseNode(acnode: acnodeType[], body: ImportAccountsDto) {
    acnode = acnode.map((data: acnodeType) => {
      return {
        _id: new Types.ObjectId(),
        static: false,
        hidden: false,
        active: true,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        nodeQty: 0,
        code: data[body.code],
        name: { ar: data[body.name.ar], en: data[body.name.en] },
        node_type: data[body.node_type],
        is_parent: data[body.is_parent] === '0' ? true : false,
        group: data[body.group] === '-' ? undefined : data[body.group],
      };
    });
    for (let index = 0; index < acnode.length; index++) {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { balance_type, reporting_type } = checkAndFillNodeType(
        acnode[index].node_type,
      );

      acnode[index].balance_type = balance_type;
      acnode[index].reporting_type = reporting_type;
      if (acnode[index].group) {
        const index2 = acnode.findIndex(
          (data) => data.code == acnode[index].group,
        );
        if (index2 != -1) {
          acnode[index].group = acnode[index2]._id;
          acnode[index2].nodeQty += 1;
        }
      }
    }
    return acnode as CreateAccountingNodeDto[];
  }

  public async addPartnerToAccount(dto: PartnerToAccountDto) {
    return await this.accountingNodeModel.findOneAndUpdate(
      { _id: dto.account },
      { $addToSet: { related_partners: dto.partner } },
      { new: true },
    );
  }

  public async removePartnerToAccount(dto: PartnerToAccountDto) {
    return await this.accountingNodeModel.findOneAndUpdate(
      { _id: dto.account },
      { $pull: { related_partners: dto.partner } },
      { new: true },
    );
  }

  public async getOneAcNodeWithCode(acCode: string[]) {
    return await this.accountingNodeModel
      .find({ code: { $in: acCode } })
      .select('_id code');
  }

  public async validateAcNodes(ids: string[]) {
    const validObjectIds = ids.filter((id) => {
      // Check if the id is a valid MongoDB ObjectId
      return id && Types.ObjectId.isValid(id);
    });
    return await this.accountingNodeModel
      .find({ _id: { $in: validObjectIds } })
      .select('_id');
  }
}
