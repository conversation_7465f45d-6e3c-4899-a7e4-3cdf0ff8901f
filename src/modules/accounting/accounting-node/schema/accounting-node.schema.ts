import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { accountingNodeType } from '../interfaces/accounting-node-type.enum';
import { accountingTypeEnum } from '../interfaces/accounting-type.enum';
import { invoicePartyType } from '../interfaces/invoice-party-type.enum';
import { reportingTypeEnum } from '../interfaces/reporting-type.enum';
import { nodeTypeEnum } from '../enums/node-type.enum';
import { NameDto } from '../../../../utils/dto';

export type AccountingNodeDocument = HydratedDocument<AccountingNode>;

@Schema({ timestamps: true })
export class AccountingNode {
  @Prop({ type: String, required: true, unique: true })
  code: string;

  @Prop({ type: NameDto, required: true })
  name: NameDto;

  @Prop({ type: Boolean, required: true })
  is_parent: boolean;

  @Prop({ type: Boolean, default: true })
  active: boolean;

  @Prop({ type: Boolean, default: false })
  hidden: boolean;

  @Prop({ type: String, enum: accountingNodeType })
  balance_type: accountingNodeType;

  @Prop({ type: Types.ObjectId, ref: AccountingNode.name })
  group: AccountingNode;

  @Prop({ type: String, enum: accountingTypeEnum })
  type: accountingTypeEnum;

  @Prop({ type: String, enum: nodeTypeEnum })
  node_type: nodeTypeEnum;

  @Prop({ enum: reportingTypeEnum, required: false })
  reporting_type: reportingTypeEnum;

  @Prop({ type: Types.ObjectId })
  branch: Types.ObjectId;

  @Prop({ type: Boolean, default: false })
  static: boolean;

  @Prop({ enum: invoicePartyType })
  invoice_party_type: invoicePartyType;

  @Prop({ type: Types.ObjectId })
  invoice_party_id: Types.ObjectId | string;

  @Prop({ type: Number, default: 0 })
  balance: number;

  @Prop({ type: Number, default: 0 })
  beginning_balance: number;

  @Prop({ type: String, default: 'SAR' })
  currency: string;

  @Prop({ type: Number, default: 0 })
  nodeQty: number;

  @Prop({ type: [Types.ObjectId], default: [] })
  related_partners: Types.ObjectId[];
}

export const accountingNodeSchema =
  SchemaFactory.createForClass(AccountingNode);
