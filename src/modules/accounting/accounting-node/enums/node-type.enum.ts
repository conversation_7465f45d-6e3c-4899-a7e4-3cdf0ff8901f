// Balance Sheet

// currentAssetsEnum
export enum currentAssetsEnum {
  cash_on_hand = 'cash_on_hand',
  cash_on_banks = 'cash_on_banks',
  accounts_receivable = 'accounts_receivable',
  inventories = 'inventories',
  other_financial_assets = 'other_financial_assets',
}

// nonCurrentAssetsEnum
export enum nonCurrentAssetsEnum {
  investments_in_associates = 'investments_in_associates',
  investments_in_jointly_controlled_entities = 'investments_in_jointly_controlled_entities',
  other_financial_assets_non_current = 'other_financial_assets_non_current',
  property_plant_and_equipment = 'property_plant_and_equipment',
  investment_property = 'investment_property',
  intangible_assets = 'intangible_assets',
  biological_assets = 'biological_assets',
}

// currentLiabilitiesEnum
export enum currentLiabilitiesEnum {
  accounts_payable = 'accounts_payable',
  provisions_current = 'provisions_current',
  other_financial_liabilities_current = 'other_financial_liabilities_current',
}

// nonCurrentLiabilitiesEnum
export enum nonCurrentLiabilitiesEnum {
  provisions_non_current = 'provisions_non_current',
  other_financial_liabilities_non_current = 'other_financial_liabilities_non_current',
  deferred_tax_liabilities = 'deferred_tax_liabilities',
}

// equityEnum
export enum equityEnum {
  share_capital = 'share_capital',
  unallocated_earnings = 'unallocated_earnings',
  retained_earnings = 'retained_earnings',
  non_controlling_interest = 'non_controlling_interest',
  other_reserves = 'other_reserves',
}

// Income Statement

// revenueEnum
export enum revenueEnum {
  sales_revenue = 'sales_revenue',
  other_operating_revenue = 'other_operating_revenue',
}

// directCostsEnum
export enum directCostsEnum {
  cost_of_goods_sold = 'cost_of_goods_sold',
}

// operatingExpensesEnum
export enum operatingExpensesEnum {
  salaries_and_wages = 'salaries_and_wages',
  rent_and_utilities = 'rent_and_utilities',
  marketing_and_advertising_expenses = 'marketing_and_advertising_expenses',
  office_supplies_and_other_general_expenses = 'office_supplies_and_other_general_expenses',
  depreciation = 'depreciation',
}

// otherIncomeAndExpensesEnum
export enum otherIncomeAndExpensesEnum {
  interest_income = 'interest_income',
  interest_expense = 'interest_expense',
  other_losses = 'other_losses',
  other_income = 'other_income',
}

// incomeTaxExpensesEnum
export enum incomeTaxExpensesEnum {
  income_tax_expense = 'income_tax_expense',
}

export enum nodeTypeEnum {
  // Current Assets
  cash_on_hand = 'cash_on_hand',
  cash_on_banks = 'cash_on_banks',
  accounts_receivable = 'accounts_receivable',
  inventories = 'inventories',
  other_financial_assets_current = 'other_financial_assets_current',

  // Non-Current Assets
  investments_in_associates = 'investments_in_associates',
  investments_in_jointly_controlled_entities = 'investments_in_jointly_controlled_entities',
  other_financial_assets_non_current = 'other_financial_assets_non_current',
  property_plant_and_equipment = 'property_plant_and_equipment',
  investment_property = 'investment_property',
  intangible_assets = 'intangible_assets',
  biological_assets = 'biological_assets',

  // Current Liabilities
  accounts_payable = 'accounts_payable',
  provisions_current = 'provisions_current',
  other_financial_liabilities_current = 'other_financial_liabilities_current',

  // Non-Current Liabilities
  provisions_non_current = 'provisions_non_current',
  other_financial_liabilities_non_current = 'other_financial_liabilities_non_current',
  deferred_tax_liabilities = 'deferred_tax_liabilities',

  // Equity
  share_capital = 'share_capital',
  unallocated_earnings = 'unallocated_earnings',
  retained_earnings = 'retained_earnings',
  non_controlling_interest = 'non_controlling_interest',
  other_reserves = 'other_reserves',

  // Revenue
  sales_revenue = 'sales_revenue',
  other_operating_revenue = 'other_operating_revenue',

  // Direct Costs
  cost_of_goods_sold = 'cost_of_goods_sold',

  // Operating Expenses
  salaries_and_wages = 'salaries_and_wages',
  rent_and_utilities = 'rent_and_utilities',
  marketing_and_advertising_expenses = 'marketing_and_advertising_expenses',
  office_supplies_and_other_general_expenses = 'office_supplies_and_other_general_expenses',
  depreciation = 'depreciation',

  // Other Income and Expenses
  interest_income = 'interest_income',
  interest_expense = 'interest_expense',
  other_losses = 'other_losses',
  other_income = 'other_income',
  // Income Tax Expenses
  income_tax_expense = 'income_tax_expense',
}
