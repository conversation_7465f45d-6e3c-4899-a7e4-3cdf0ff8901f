import { IsEnum } from '@nestjs/class-validator';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, Validate } from 'class-validator';
import { reportingTypeEnum } from '../interfaces/reporting-type.enum';
import { Types } from 'mongoose';
import { Transform } from 'class-transformer';
import { nodeTypeEnum } from '../enums/node-type.enum';
import { accountingNodeType } from '../interfaces/accounting-node-type.enum';
import {
  validateLanguage,
  NameDto,
  validateEnLanguage,
} from '../../../../utils/dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class UpdateAccountingNodeDto {
  @ApiProperty({
    description: 'title of account node',
    example: 'test',
    required: true,
  })
  @ApiProperty({ type: NameDto })
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @ApiProperty({
    description: 'active status of account node',
    example: false,
    required: true,
  })
  @IsBoolean()
  @IsOptional()
  active: boolean;

  @ApiProperty({
    description: 'hidden status of account node',
    example: false,
    required: true,
  })
  @IsBoolean()
  @IsOptional()
  hidden: boolean;

  @IsString()
  @IsOptional()
  code: string;

  @IsString()
  @IsOptional()
  @IsEnum(reportingTypeEnum)
  reporting_type: reportingTypeEnum;

  @IsOptional()
  @IsString()
  @IsEnum(nodeTypeEnum)
  node_type?: nodeTypeEnum;

  @ApiProperty({
    description: 'id of account node parent',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  group?: Types.ObjectId;

  @ApiProperty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsBoolean()
  @IsOptional()
  is_parent?: boolean;
  @ApiHideProperty()
  balance_type: accountingNodeType;
}
