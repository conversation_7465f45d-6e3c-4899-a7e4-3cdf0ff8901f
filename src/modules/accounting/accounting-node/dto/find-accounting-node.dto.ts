import { Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsString } from 'class-validator';
import { IsObjectId } from 'class-validator-mongo-object-id';
import { accountingTypeEnum } from '../interfaces/accounting-type.enum';
import { invoicePartyType } from '../interfaces/invoice-party-type.enum';
import { reportingTypeEnum } from '../interfaces/reporting-type.enum';
import { nodeTypeEnum } from '../enums/node-type.enum';

export class FindAccountingNodeDto {
  @IsArray()
  @IsObjectId()
  @IsOptional()
  ids?: Types.ObjectId[];

  @ApiProperty({
    description: 'type of account node',
    example: 'party',
    required: false,
  })
  @IsOptional()
  @IsEnum(accountingTypeEnum)
  type?: accountingTypeEnum;

  @IsOptional()
  @IsString()
  @IsEnum(nodeTypeEnum)
  node_type?: nodeTypeEnum;

  @ApiProperty({
    description: 'invoice_party_type of account node',
    example: 'customer',
    required: false,
  })
  @IsOptional()
  @IsEnum(invoicePartyType)
  invoice_party_type?: invoicePartyType;

  @IsString()
  @IsOptional()
  @IsEnum(reportingTypeEnum)
  reporting_type: reportingTypeEnum;
}
