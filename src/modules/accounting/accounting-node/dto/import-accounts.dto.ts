import { IsString, Validate } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  NameDto,
  validateEnLanguage,
  validateLanguage,
} from '../../../../utils/dto';

export class ImportAccountsDto {
  @ApiProperty()
  @IsString()
  code: string;

  @ApiProperty({
    description: 'name of account node',
    example: `{"ar": "test",
      "en": "test",}`,
    required: true,
  })
  @ApiProperty({ type: NameDto })
  @Transform(({ value }) => {
    try {
      return JSON.parse(value);
    } catch (e) {
      return {};
    }
  })
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @ApiProperty()
  @IsString()
  node_type: string;

  @ApiProperty()
  @IsString()
  is_parent: string;

  @ApiProperty()
  @IsString()
  group: string;

  @ApiProperty({ type: 'string', format: 'binary', required: true })
  file: Express.Multer.File;
}
