import { Type, Transform } from 'class-transformer';
import { IsInt, Min, IsOptional, IsString, IsBoolean } from 'class-validator';

import { Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class AccountingTreeDto {
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public limit?: number;

  // TODO: what is this q???
  @IsOptional()
  @IsString()
  public q?: string;

  @ApiProperty({
    type: String,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  public branch?: Types.ObjectId;

  @Transform(({ value }) => value === true)
  @IsBoolean()
  @IsOptional()
  show_disabled_account?: boolean = true;

  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  show_hidden_account?: boolean;
}
