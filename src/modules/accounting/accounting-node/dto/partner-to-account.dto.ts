import { IsNotEmpty } from 'class-validator';
import { Types } from 'mongoose';
import { Transform } from 'class-transformer';
import { RpcDto } from '../../../../utils/dto/rpc.dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class PartnerToAccountDto extends RpcDto {
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  account: Types.ObjectId;

  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  partner: Types.ObjectId;
}
