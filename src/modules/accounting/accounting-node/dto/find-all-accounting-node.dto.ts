import { Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsIn,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { IsInt } from '@nestjs/class-validator';
import { Transform, Type } from 'class-transformer';
import { accountingNodeType } from '../interfaces/accounting-node-type.enum';
import { accountingTypeEnum } from '../interfaces/accounting-type.enum';
import { invoicePartyType } from '../interfaces/invoice-party-type.enum';
import { reportingTypeEnum } from '../interfaces/reporting-type.enum';
import { nodeTypeEnum } from '../enums/node-type.enum';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class FindAllAccountingNodeDto {
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public page?: number;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public limit?: number;

  @IsOptional()
  @IsString()
  public queries?: string;

  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  include_balance?: boolean;

  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id?: Types.ObjectId;

  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({
    description: 'is_parent of account node',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  is_parent?: boolean;

  @ApiProperty({
    description: 'balance_type of account node',
    example: 'debit',
    required: false,
  })
  @IsEnum(accountingNodeType)
  @IsOptional()
  balance_type?: accountingNodeType;

  /*   @IsOptional()
  @IsString()
  @IsEnum(nodeTypeEnum)
  node_type?: nodeTypeEnum;
 */
  @ApiProperty({
    description: 'active status of account node',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  active?: boolean;

  @ApiProperty({
    description: 'hidden status of account node',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  hidden?: boolean;

  @ApiProperty({
    description: 'id of account node parent',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  parent?: string;

  @ApiProperty({
    description: 'type of account node',
    example: 'party',
    required: false,
  })
  @IsOptional()
  @IsEnum(accountingTypeEnum)
  type?: accountingTypeEnum;

  @ApiProperty({
    description: 'brnach of accouting',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;

  @ApiProperty({
    description: 'invoice_party_type of account node',
    example: 'customer',
    required: false,
  })
  @IsOptional()
  @IsEnum(invoicePartyType)
  invoice_party_type?: invoicePartyType;

  @ApiProperty({
    description: 'currecy of account',
    example: 'usd',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @IsOptional()
  @IsEnum(reportingTypeEnum)
  reporting_type?: reportingTypeEnum;

  @ApiProperty({
    required: false,
    type: [String],
  })
  @IsOptional()
  @Transform(({ value }) => (Array.isArray(value) ? value : Array(value)))
  @IsIn(Object.values(nodeTypeEnum), { each: true })
  node_type?: [nodeTypeEnum];
}
