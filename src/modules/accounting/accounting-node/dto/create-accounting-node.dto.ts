import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsString,
  Validate,
} from 'class-validator';
import { Types } from 'mongoose';
import { IsOptional } from 'class-validator';
import { invoicePartyType } from '../interfaces/invoice-party-type.enum';
import { accountingNodeType } from '../interfaces/accounting-node-type.enum';
import { accountingTypeEnum } from '../interfaces/accounting-type.enum';
import { Transform } from 'class-transformer';
import { reportingTypeEnum } from '../interfaces/reporting-type.enum';

import { nodeTypeEnum } from '../enums/node-type.enum';
import {
  NameDto,
  validateEnLanguage,
  validateLanguage,
} from '../../../../utils/dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class CreateAccountingNodeDto {
  _id?: Types.ObjectId;

  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({
    description: 'name of account node',
    example: 'test',
    required: true,
  })
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @ApiProperty({
    description: 'is_parent of account node',
    example: false,
    required: true,
  })
  @IsBoolean()
  @Transform(({ value }) => value === true)
  is_parent: boolean;

  @ApiHideProperty()
  balance_type: accountingNodeType;

  @ApiProperty({
    description: 'active status of account node',
    example: true,
    required: true,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === true)
  active?: boolean = true;

  @ApiProperty({
    description: 'hidden status of account node',
    example: false,
    required: false,
  })
  @IsBoolean()
  @Transform(({ value }) => value === true)
  hidden?: boolean;

  @ApiProperty({
    description: 'id of account node parent',
    example: '6347e554fcf62efbbe757b66',
    required: true,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  group?: Types.ObjectId;

  @ApiProperty({
    description: 'type of account node',
    example: 'party',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsEnum(accountingTypeEnum)
  type: accountingTypeEnum;

  @IsOptional()
  @IsString()
  @IsEnum(nodeTypeEnum)
  node_type?: nodeTypeEnum;

  @ApiProperty({
    description: 'brnach of accouting',
    example: '6347e554fcf62efbbe757b66',
    required: true,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @ApiProperty({
    description: 'invoice_party_type of account node',
    example: 'customer',
    required: false,
  })
  @IsOptional()
  @IsEnum(invoicePartyType)
  invoice_party_type: invoicePartyType;

  @ApiProperty({
    description: 'currecy of account',
    example: 'usd',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency: string;

  static?: boolean;

  @ApiProperty({
    description: ' report type of account node',
    example: reportingTypeEnum.balance_sheet,
    required: true,
  })
  @IsString()
  @IsOptional()
  @IsEnum(reportingTypeEnum)
  reporting_type: reportingTypeEnum;
}
export class CreateAccountingNodeRpcDto extends CreateAccountingNodeDto {
  @IsOptional()
  policyData?: any;
}
export class initAccountingNodeDto {
  _id?: Types.ObjectId;

  code: string;

  @ApiProperty({ type: NameDto })
  @Validate(validateLanguage)
  @Validate(validateEnLanguage)
  name: NameDto;

  @ApiProperty({
    description: 'is_parent of account node',
    example: false,
    required: true,
  })
  @IsBoolean()
  @Transform(({ value }) => value === true)
  is_parent: boolean;

  @ApiHideProperty()
  balance_type: accountingNodeType;

  @IsBoolean()
  @Transform(({ value }) => value === true)
  active?: boolean;

  @IsBoolean()
  @Transform(({ value }) => value === true)
  hidden?: boolean;

  @ApiProperty({
    description: 'id of account node parent',
    example: '6347e554fcf62efbbe757b66',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  group?: Types.ObjectId;

  @ApiProperty({
    description: 'type of account node',
    example: 'party',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value || accountingTypeEnum.general)
  @IsEnum(accountingTypeEnum)
  type: accountingTypeEnum;

  @ApiProperty({
    description: 'invoice_party_type of account node',
    example: 'customer',
    required: false,
  })
  @IsOptional()
  @IsEnum(invoicePartyType)
  invoice_party_type: invoicePartyType;

  @ApiProperty({
    description: 'currecy of account',
    example: 'usd',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency: string;

  static?: boolean;

  report_type?: string;
}
