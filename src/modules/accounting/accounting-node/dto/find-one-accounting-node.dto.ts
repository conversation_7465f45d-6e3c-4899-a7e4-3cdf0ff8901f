import { Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { accountingNodeType } from '../interfaces/accounting-node-type.enum';
import { accountingTypeEnum } from '../interfaces/accounting-type.enum';
import { invoicePartyType } from '../interfaces/invoice-party-type.enum';
import { reportingTypeEnum } from '../interfaces/reporting-type.enum';
import { nodeTypeEnum } from '../enums/node-type.enum';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class FindOneAccountingNodeDto {
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id?: Types.ObjectId;

  code?: string;

  @ApiProperty({
    description: 'is_parent of account node',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === true)
  is_parent?: boolean;

  @ApiProperty({
    description: 'balance_type of account node',
    example: 'debit',
    required: false,
  })
  @IsEnum(accountingNodeType)
  @IsOptional()
  balance_type?: accountingNodeType;

  @IsOptional()
  @IsString()
  @IsEnum(nodeTypeEnum)
  node_type?: nodeTypeEnum;

  @ApiProperty({
    description: 'active status of account node',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @ApiProperty({
    description: 'hidden status of account node',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  hidden?: boolean;

  @ApiProperty({
    description: 'id of account node parent',
    example: '6347e554fcf62efbbe757b66',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  parent?: Types.ObjectId;

  @ApiProperty({
    description: 'type of account node',
    example: 'party',
    required: false,
  })
  @IsOptional()
  @IsEnum(accountingTypeEnum)
  type?: accountingTypeEnum;

  @ApiProperty({
    description: 'invoice_party_type of account node',
    example: 'customer',
    required: false,
  })
  @IsOptional()
  @IsEnum(invoicePartyType)
  invoice_party_type?: invoicePartyType;

  @ApiProperty({
    description: 'currecy of account',
    example: 'usd',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({
    required: false,
    type: () => reportingTypeEnum,
  })
  @IsOptional()
  @IsEnum(reportingTypeEnum)
  reporting_type?: reportingTypeEnum;

  @IsOptional()
  @IsString()
  public queries?: string;
}
