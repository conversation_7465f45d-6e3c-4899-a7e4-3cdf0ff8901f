import { IsBoolean, IsEnum, IsString, Validate } from 'class-validator';
import { Types } from 'mongoose';
import { IsOptional } from 'class-validator';

import { Transform } from 'class-transformer';

import { nodeTypeEnum } from '../enums/node-type.enum';

import { ApiProperty } from '@nestjs/swagger';
import { NameDto, validateLanguage } from '../../../../utils/dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class ImportAccountingNodeDto {
  @IsString()
  code?: string;

  @ApiProperty({ type: NameDto })
  @Validate(validateLanguage)
  name: NameDto;

  @IsBoolean()
  @Transform(({ value }) => value === true)
  is_parent: boolean;

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === true)
  active?: boolean = true;

  @IsBoolean()
  @Transform(({ value }) => value === true)
  hidden?: boolean = false;

  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  group?: Types.ObjectId;

  @IsString()
  @IsEnum(nodeTypeEnum)
  node_type: nodeTypeEnum;
}
