import { Test, TestingModule } from '@nestjs/testing';
import { AccountingNodeService } from './accounting-node.service';
import { getModelToken } from '@nestjs/mongoose';
import { AccountingNode } from './schema/accounting-node.schema';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';

describe('AccountingNodeService', () => {
  let service: AccountingNodeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AccountingNodeService,
        {
          provide: getModelToken(AccountingNode.name),
          useValue: {},
        },
        {
          provide: GeneralLedgerService,
          useValue: {},
        },
        {
          provide: 'company',
          useValue: {},
        },
        {
          provide: TradeRpcService,
          useValue: {},
        },
        {
          provide: UserRpcService,
          useValue: {},
        },
        {
          provide: AccountingNodeProxyPatternService,
          useValue: {},
        },
      ],
    }).compile();

    service = await module.resolve<AccountingNodeService>(
      AccountingNodeService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
