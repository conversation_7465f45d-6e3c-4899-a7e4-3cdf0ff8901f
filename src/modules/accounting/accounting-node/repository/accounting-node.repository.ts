import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { AccountingNodeModel } from '../model/accounting-node.model';

@Injectable()
export class AccountingNodeRepository extends BaseRepository<AccountingNodeModel> {
  constructor(
    @InjectModel(AccountingNodeModel)
    accountingNodeModel: typeof AccountingNodeModel,
  ) {
    super(accountingNodeModel);
  }

  public async getByMongoId(mongoId: string) {
    return (
      (await this.findOne(
        { mongo_id: mongoId },
        { attributes: ['id'], raw: true },
      )) || null
    );
  }
}
