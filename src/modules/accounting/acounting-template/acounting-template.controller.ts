import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  HttpException,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ObjectId } from 'mongoose';

import { AcountingTemplateService } from './acounting-template.service';
import { CreateAcountingTemplateDto } from './dto/create-acounting-template.dto';
import { UpdateAcountingTemplateDto } from './dto/update-acounting-template.dto';
import * as XLSX from 'xlsx';
import { ApiBody, ApiConsumes, ApiHeader, ApiTags } from '@nestjs/swagger';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';
import { PaginationDto } from '../../../utils/dto';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';
@ApiTags('acounting-template')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('acounting-template')
export class AcountingTemplateController {
  constructor(
    private readonly acountingTemplateService: AcountingTemplateService,
  ) {}

  @UseGuards(CaslGuard)
  @abilities({ action: 'create', subject: 'accounting_template' })
  @Post()
  create(@Body() createAcountingTemplateDto: CreateAcountingTemplateDto) {
    return this.acountingTemplateService.create(createAcountingTemplateDto);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'accounting_template' })
  @Get()
  findAll(@Query() pagination: PaginationDto) {
    return this.acountingTemplateService.findAll(pagination);
  }

  @Get('seeder')
  seeder() {
    return this.acountingTemplateService.seeder(undefined, 'test');
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'create', subject: 'accounting_template' })
  @Post('import')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          // 👈 this property
          type: 'string',
          format: 'binary',
          nullable: false,
        },
        name: {
          // 👈 this property
          type: 'string',
        },
      },
      required: ['file', 'name'],
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  importXlsx(@Body() data, @UploadedFile('file') file: Express.Multer.File) {
    if (!file) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.emptyFile),
        erpExceptionCode.emptyFile,
      );
    }
    const workbook = XLSX.read(file.buffer);
    const sheetNameList = workbook.SheetNames;
    return this.acountingTemplateService.seeder(
      XLSX.utils.sheet_to_json(workbook.Sheets[sheetNameList[0]]),
      data.name,
    );
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'accounting_template' })
  @Get(':id')
  findOne(@Param('id') _id: ObjectId) {
    return this.acountingTemplateService.findOne(_id);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'edit', subject: 'accounting_template' })
  @Patch(':id')
  update(
    @Param('id') _id: ObjectId,
    @Body() updateAcountingTemplateDto: UpdateAcountingTemplateDto,
  ) {
    return this.acountingTemplateService.update(
      _id,
      updateAcountingTemplateDto,
    );
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'delete', subject: 'accounting_template' })
  @Delete(':id')
  remove(@Param('id') id: ObjectId) {
    return this.acountingTemplateService.remove(id);
  }
}
