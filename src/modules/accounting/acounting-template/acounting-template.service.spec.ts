import { Test, TestingModule } from '@nestjs/testing';
import { AcountingTemplateService } from './acounting-template.service';
import { getModelToken } from '@nestjs/mongoose';
import { AcountingTemplate } from './schema/acounting-template.schema';
import { AccountingNodeTemplateService } from '../accounting-node-template/accounting-node-template.service';

describe('AcountingTemplateService', () => {
  let service: AcountingTemplateService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AcountingTemplateService,
        {
          provide: getModelToken(AcountingTemplate.name, 'commonDb'),
          useValue: {},
        },
        {
          provide: AccountingNodeTemplateService,
          useValue: {},
        },
      ],
    }).compile();

    service = await module.resolve<AcountingTemplateService>(
      AcountingTemplateService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
