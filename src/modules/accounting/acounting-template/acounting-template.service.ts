import { HttpException, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId } from 'mongoose';

import { AccountingNodeTemplateService } from '../accounting-node-template/accounting-node-template.service';
import { CreateAcountingTemplateDto } from './dto/create-acounting-template.dto';
import { UpdateAcountingTemplateDto } from './dto/update-acounting-template.dto';
import {
  AcountingTemplate,
  AcountingTemplateDocument,
} from './schema/acounting-template.schema';
import { paginate, PaginationDto } from '../../../utils/dto';
import { nodeSeeder } from '../../../utils/seeders/accounting-node-seeder';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';
@Injectable({ scope: Scope.REQUEST, durable: true })
export class AcountingTemplateService {
  constructor(
    @InjectModel(AcountingTemplate.name, 'commonDb')
    private acountingTemplateModel: Model<AcountingTemplate>,
    private acountingNodeTemplateService: AccountingNodeTemplateService,
  ) {}
  async create(
    createAcountingTemplateDto: CreateAcountingTemplateDto,
  ): Promise<AcountingTemplateDocument> {
    const result = await new this.acountingTemplateModel(
      createAcountingTemplateDto,
    ).save();

    return result;
  }

  async findAll(
    pagination: PaginationDto,
  ) /* : Promise<AcountingTemplate[]> */ {
    const { page, limit } = pagination;
    const result = await this.acountingTemplateModel
      .find({})
      .skip((pagination.page - 1) * pagination.limit)
      .limit(pagination.limit);
    const meta = await paginate(limit, page, this.acountingTemplateModel, {});
    return { result, meta };
  }
  async seeder(input: any, name: string): Promise<any> {
    await this.acountingTemplateModel.deleteMany({});
    const acTemplate = await this.create({ title: name });
    const data = input || nodeSeeder(acTemplate._id);
    await this.acountingNodeTemplateService.insertManyclean(data);
  }
  async findOne(_id: ObjectId) {
    return await this.acountingTemplateModel.findOne({ _id });
  }

  async update(
    _id: ObjectId,
    updateAcountingTemplateDto: UpdateAcountingTemplateDto,
  ) {
    const data = await this.acountingTemplateModel.findOneAndUpdate(
      { _id },
      updateAcountingTemplateDto,
    );
    if (!data)
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.accountingTemplateNotFound),
        erpExceptionCode.accountingTemplateNotFound,
      );
    return data;
  }

  async remove(_id: ObjectId) {
    const data = await this.acountingTemplateModel.findOneAndDelete({ _id });
    if (!data)
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.accountingTemplateNotFound),
        erpExceptionCode.accountingTemplateNotFound,
      );
    return data;
  }
}
