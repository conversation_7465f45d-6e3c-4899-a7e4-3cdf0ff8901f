import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type AcountingTemplateDocument = HydratedDocument<AcountingTemplate>;
@Schema({ timestamps: true })
export class AcountingTemplate {
  @Prop({ type: String, required: true })
  title: string;
}
export const acountingTemplateSchema =
  SchemaFactory.createForClass(AcountingTemplate);
