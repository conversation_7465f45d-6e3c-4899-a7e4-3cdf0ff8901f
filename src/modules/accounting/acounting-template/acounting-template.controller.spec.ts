import { Test, TestingModule } from '@nestjs/testing';
import { AcountingTemplateController } from './acounting-template.controller';
import { AcountingTemplateService } from './acounting-template.service';
import { AccountingNodeTemplateService } from '../accounting-node-template/accounting-node-template.service';
import { getModelToken } from '@nestjs/mongoose';
import { AcountingTemplate } from './schema/acounting-template.schema';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { CaslAbilityFactory } from '../../casl/casl-ability.factory';

describe('AcountingTemplateController', () => {
  let controller: AcountingTemplateController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AcountingTemplateController],
      providers: [
        AcountingTemplateService,
        {
          provide: getModelToken(AcountingTemplate.name, 'commonDb'),
          useValue: {},
        },
        { provide: AccountingNodeTemplateService, useValue: {} },
        { provide: CaslAbilityFactory, useValue: {} },
        { provide: UserRpcService, useValue: {} },
      ],
    }).compile();

    controller = await module.resolve<AcountingTemplateController>(
      AcountingTemplateController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
