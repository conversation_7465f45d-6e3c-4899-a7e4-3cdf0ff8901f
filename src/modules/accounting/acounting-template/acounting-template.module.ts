import { Module } from '@nestjs/common';
import { AcountingTemplateService } from './acounting-template.service';
import { AcountingTemplateController } from './acounting-template.controller';
import { MongooseModule } from '@nestjs/mongoose';

import {
  AcountingTemplate,
  acountingTemplateSchema,
} from './schema/acounting-template.schema';
import { AccountingNodeTemplateModule } from '../accounting-node-template/accounting-node-template.module';

@Module({
  imports: [
    MongooseModule.forFeature(
      [{ name: AcountingTemplate.name, schema: acountingTemplateSchema }],
      'commonDb',
    ),
    AccountingNodeTemplateModule,
  ],
  controllers: [AcountingTemplateController],
  providers: [AcountingTemplateService],
})
export class AcountingTemplateModule {}
