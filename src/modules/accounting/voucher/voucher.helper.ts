import { Types } from 'mongoose';
import { voucherType } from '../transactions/dto';
import { Accounts, CreateVoucherDto } from './dto/create-voucher.dto';
import { HttpException } from '@nestjs/common';

import * as salesTax from 'sales-tax';
import { ITransaction } from '../general-ledger/dto/transaction.dto';
import { UpdateVoucherDto } from './dto/update-voucher.dto';
import { UpdateMomoDto } from './dto/update-memo.dto';
import { voucherPartyType } from './dto/voucher-party.enum';
import {
  AccountingNode,
  AccountingNodeDocument,
} from '../accounting-node/schema/accounting-node.schema';
import { journalTypes } from '../general-ledger/schema/general-ledger.schema';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';
import { Calculator } from '../../../utils/calculator';

export async function handleVoucherCreation(
  voucherDto: CreateVoucherDto | UpdateVoucherDto,
  code: number,
  branchId,
  policies,
  accountingNodeService: AccountingNodeProxyPatternService,
  tradeRpcService,
  userRpcService,
): Promise<any> {
  let paymentTypeData = undefined;
  let paymentCommission = 0;
  let debitPartnerId = undefined,
    debitPartnerType = null,
    creditPartnerId = undefined,
    creditPartnerType = null;
  const transactions = [];
  const taxRate = (await salesTax.getSalesTax('SA'))?.rate;
  voucherDto.amount = +Number(voucherDto.amount.toFixed(2));
  voucherDto.branch = new Types.ObjectId(voucherDto.branch);
  voucherDto.journal = new Types.ObjectId();
  if (voucherDto.payment_type) {
    //get payment type data
    paymentTypeData = await userRpcService.getpaymentType(code, {
      _id: voucherDto.payment_type,
    });

    if (!paymentTypeData) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.paymentTypeNotFound),
        erpExceptionCode.paymentTypeNotFound,
      );
    }
    assignVoucherAccounts(voucherDto, paymentTypeData);

    paymentCommission = Calculator.sum(
      Calculator.percentage(
        Number(voucherDto.amount),
        paymentTypeData.commission_percentage,
      ).number(),
      paymentTypeData.commission_value,
    )
      .round()
      .number();

    checkVoucherAmount(voucherDto);

    if (paymentCommission > 0 && paymentTypeData.commission_account?._id) {
      transactions.push({
        code: paymentTypeData.commission_account.code,
        credit: 0,
        debit: +Number(paymentCommission).toFixed(2),
        accountingNode: new Types.ObjectId(
          paymentTypeData.commission_account._id,
        ),
        by_user: new Types.ObjectId(voucherDto.by_user),
        description: voucherDto.note || 'voucher',
        current_balance: 0,
      } as ITransaction);
    }
  }

  const discountId = policies.sales_discount_account;
  if (!discountId && voucherDto.discount > 0) {
    throw new HttpException(
      nameOf(erpExceptionCode, (x) => x.salesDiscountAccountNotFound),
      erpExceptionCode.salesDiscountAccountNotFound,
    );
  }
  const discountAccount = await accountingNodeService
    .findOne({ _id: discountId }, branchId)
    .catch((er) => er);

  if (!discountAccount && voucherDto.discount > 0) {
    throw new HttpException(
      nameOf(erpExceptionCode, (x) => x.salesDiscountAccountNotFound),
      erpExceptionCode.salesDiscountAccountNotFound,
    );
  }

  const creditAccounts = voucherDto.credit_account.map((account) => {
    return {
      accounting_node: account.accounting_node,
      partner_id: account.partner_id,
      partner_type: account.partner_type,
    };
  });
  const debitAccounts = voucherDto.debit_account.map((account) => {
    return {
      accounting_node: account.accounting_node,
      partner_id: account.partner_id,
      partner_type: account.partner_type,
    };
  });

  if (
    voucherDto.invoice_party_type === voucherPartyType.customer ||
    voucherDto.invoice_party_type === voucherPartyType.vendor
  ) {
    if (voucherDto.type === voucherType.payment) {
      const partner = await tradeRpcService.getPartner(code, {
        _id: debitAccounts[0].accounting_node,
        type: voucherDto.invoice_party_type,
        branch: voucherDto.branch,
      });

      if (!partner) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.partnerNotFound),
          erpExceptionCode.partnerNotFound,
        );
      }
      voucherDto.debit_account[0].partner_id = new Types.ObjectId(partner._id);
      voucherDto.debit_account[0].partner_type = voucherDto.invoice_party_type;
      voucherDto.debit_account[0].accounting_node =
        partner.accounting_info.general_account._id;

      debitPartnerId = partner._id;
      debitPartnerType = partner.type;
      debitAccounts[0].accounting_node =
        partner.accounting_info.general_account._id;
    }

    if (voucherDto.type === voucherType.receipt) {
      const partner = await tradeRpcService.getPartner(code, {
        _id: creditAccounts[0].accounting_node,
        type: voucherDto.invoice_party_type,
        branch: voucherDto.branch,
      });

      if (!partner) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.partnerNotFound),
          erpExceptionCode.partnerNotFound,
        );
      }
      voucherDto.credit_account[0].partner_id = new Types.ObjectId(partner._id);
      voucherDto.credit_account[0].partner_type = voucherDto.invoice_party_type;
      voucherDto.credit_account[0].accounting_node =
        partner.accounting_info.general_account._id;
      creditPartnerId = partner._id;
      creditPartnerType = partner.type;
      creditAccounts[0].accounting_node =
        partner.accounting_info.general_account._id;
    }
  }

  if (
    voucherDto.type === voucherType.credit_memo ||
    voucherDto.type === voucherType.debit_memo
  ) {
    if (voucherDto.type === voucherType.credit_memo) {
      for (const account of voucherDto.credit_account) {
        if (account.partner_id) {
          const partner = await tradeRpcService.getPartner(code, {
            _id: account.partner_id,
            type: account.partner_type,
            branch: voucherDto.branch,
          });

          if (!partner) {
            throw new HttpException(
              nameOf(erpExceptionCode, (x) => x.partnerNotFound),
              erpExceptionCode.partnerNotFound,
            );
          }
          account.partner_id = new Types.ObjectId(partner._id);
          account.accounting_node = partner.accounting_info.general_account._id;
          creditPartnerId = partner._id;
          creditPartnerType = partner.type;

          const targetAccount = creditAccounts.find(
            (account) => String(account.partner_id) === String(partner._id),
          );

          if (targetAccount) {
            targetAccount.accounting_node =
              partner.accounting_info.general_account._id;
          }
        }
      }
    }

    if (voucherDto.type === voucherType.debit_memo) {
      for (const account of voucherDto.debit_account) {
        if (account.partner_id) {
          const partner = await tradeRpcService.getPartner(code, {
            _id: account.partner_id,
            type: account.partner_type,
            branch: voucherDto.branch,
          });

          if (!partner) {
            throw new HttpException(
              nameOf(erpExceptionCode, (x) => x.partnerNotFound),
              erpExceptionCode.partnerNotFound,
            );
          }
          account.partner_id = new Types.ObjectId(partner._id);
          account.accounting_node = partner.accounting_info.general_account._id;
          creditPartnerId = partner._id;
          creditPartnerType = partner.type;

          const targetAccount = debitAccounts.find(
            (account) => String(account.partner_id) === String(partner._id),
          );

          if (targetAccount) {
            targetAccount.accounting_node =
              partner.accounting_info.general_account._id;
          }
        }
      }
    }
  }

  const acData = await findAccountingNodes(
    debitAccounts,
    creditAccounts,
    new Types.ObjectId(voucherDto.branch),
    accountingNodeService,
  );
  acData.forEach((element: AccountingNodeDocument) => {
    if (
      voucherDto.type === voucherType.credit_memo ||
      voucherDto.type === voucherType.debit_memo
    ) {
      if (voucherDto.type === voucherType.credit_memo) {
        const accounts = voucherDto.credit_account.filter((account) => {
          return String(account.accounting_node) === String(element._id);
        });

        if (accounts.length > 0) {
          for (const account of accounts) {
            transactions.push({
              code: element.code,
              credit: +Number(account.amount).toFixed(2),
              debit: 0,
              accountingNode: new Types.ObjectId(element._id),
              by_user: new Types.ObjectId(voucherDto.by_user),
              description: voucherDto.note || 'voucher',
              current_balance: 0,
              partner_id: account.partner_id,
              partner_type: account.partner_type,
            } as ITransaction);
          }
        }

        const account = voucherDto.debit_account.find((account) => {
          return String(account.accounting_node) === String(element._id);
        });
        if (account) {
          transactions.push({
            code: element.code,
            credit: 0,
            debit: +Number(account.amount).toFixed(2),
            accountingNode: new Types.ObjectId(element._id),
            by_user: new Types.ObjectId(voucherDto.by_user),
            description: voucherDto.note || 'voucher',
            current_balance: 0,
            partner_id: undefined,
            partner_type: undefined,
          } as ITransaction);
        }
      }

      if (voucherDto.type === voucherType.debit_memo) {
        const accounts = voucherDto.debit_account.filter((account) => {
          return String(account.accounting_node) === String(element._id);
        });

        if (accounts.length > 0) {
          for (const account of accounts) {
            transactions.push({
              code: element.code,
              credit: 0,
              debit: +Number(account.amount).toFixed(2),
              accountingNode: new Types.ObjectId(element._id),
              by_user: new Types.ObjectId(voucherDto.by_user),
              description: voucherDto.note || 'memo voucher',
              current_balance: 0,
              partner_id: account.partner_id,
              partner_type: account.partner_type,
            } as ITransaction);
          }
        }

        const account = voucherDto.credit_account.find((account) => {
          return String(account.accounting_node) === String(element._id);
        });
        if (account) {
          transactions.push({
            code: element.code,
            credit: +Number(account.amount).toFixed(2),
            debit: 0,
            accountingNode: new Types.ObjectId(element._id),
            by_user: new Types.ObjectId(voucherDto.by_user),
            description: voucherDto.note || 'voucher',
            current_balance: 0,
            partner_id: undefined,
            partner_type: undefined,
          } as ITransaction);
        }
      }
    } else {
      let account;
      account = voucherDto.credit_account.find((account) => {
        return String(account.accounting_node) === String(element._id);
      });

      if (account) {
        transactions.push({
          code: element.code,
          credit: +Number(account.amount).toFixed(2),
          debit: 0,
          accountingNode: new Types.ObjectId(element._id),
          by_user: new Types.ObjectId(voucherDto.by_user),
          description: voucherDto.note || 'voucher',
          current_balance: 0,
          partner_id: creditPartnerId,
          partner_type: creditPartnerType,
        } as ITransaction);
      } else {
        account = voucherDto.debit_account.find((account) => {
          return String(account.accounting_node) === String(element._id);
        });
        if (account) {
          if (
            voucherDto.type == voucherType.payment &&
            voucherDto.is_include_tax == true
          ) {
            const taxAccount = policies.purchase_tax_account;

            const baseAmount = Calculator.div(
              Calculator.subtract(account.amount, paymentCommission).number(),
              Calculator.sum(1, taxRate).number(),
            )
              .round()
              .number();
            const temp = Calculator.subtract(account.amount, paymentCommission)
              .round()
              .number();

            transactions.push({
              code: element.code,
              credit: 0,
              debit: +Number(baseAmount).toFixed(2),
              accountingNode: new Types.ObjectId(element._id),
              by_user: new Types.ObjectId(voucherDto.by_user),
              description: voucherDto.note || 'voucher base amount',
              current_balance: 0,
              partner_id: debitPartnerId,
              partner_type: debitPartnerType,
            } as ITransaction);
            // expenses  tax account
            if (
              +voucherDto.tax_amount.toFixed(2) !==
              Calculator.subtract(temp, baseAmount).round().number()
            ) {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.taxAmountNotValid),
                erpExceptionCode.taxAmountNotValid,
              );
            }
            transactions.push({
              code: element.code,
              credit: 0,
              debit: Calculator.subtract(temp, baseAmount).round().number(),
              accountingNode: new Types.ObjectId(taxAccount), // draft need to tacecare of it
              by_user: new Types.ObjectId(voucherDto.by_user),
              description: voucherDto.note || 'tax',
              current_balance: 0,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              isTax: true,
            } as ITransaction);
          } else {
            transactions.push({
              code: element.code,
              credit: 0,
              debit: +Number(account.amount - paymentCommission).toFixed(2),
              accountingNode: new Types.ObjectId(element._id),
              by_user: new Types.ObjectId(voucherDto.by_user),
              description: voucherDto.note || 'voucher',
              current_balance: 0,
              partner_id: debitPartnerId,
              partner_type: debitPartnerType,
            } as ITransaction);
          }
        }
      }
    }
  });

  const type: journalTypes = findVoucherType(voucherDto.type);

  const partnerAccountData = findPartnerAccountData(acData, voucherDto);

  return {
    type,
    transactions,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    debitPartnerId,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    creditPartnerId,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    discountAccount,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    partnerAccountData,
  };
}

export function assignVoucherAccounts(
  voucherDto: CreateVoucherDto | UpdateVoucherDto,
  paymentTypeData: any,
): void {
  if (voucherDto.type === voucherType.receipt) {
    voucherDto.debit_account = [
      {
        accounting_node: paymentTypeData.payment_account?._id,
        amount: voucherDto.amount,
      },
    ];
  } else if (voucherDto.type === voucherType.payment) {
    voucherDto.credit_account = [
      {
        accounting_node: paymentTypeData.payment_account?._id,
        amount: voucherDto.amount,
      },
    ];
  }
}

export function checkVoucherAmount(
  voucherDto: CreateVoucherDto | UpdateVoucherDto | UpdateMomoDto,
) {
  const creditAmount = voucherDto.credit_account.reduce((pr, cr: Accounts) => {
    cr.amount = +Number(cr.amount).toFixed(2);
    return pr + cr.amount;
  }, 0);
  const debitAmount = voucherDto.debit_account.reduce((pr, cr: Accounts) => {
    cr.amount = +Number(cr.amount).toFixed(2);
    return pr + cr.amount;
  }, 0);
  if (
    voucherDto.amount !== +Number(creditAmount).toFixed(2) ||
    voucherDto.amount !== +Number(debitAmount).toFixed(2)
  ) {
    throw new HttpException(
      nameOf(erpExceptionCode, (x) => x.voucherNotBalanced),
      erpExceptionCode.voucherNotBalanced,
    );
  }

  return debitAmount;
}

export async function findAccountingNodes(
  debitAccounts,
  creditAccounts,
  branch: Types.ObjectId,
  accountingNodeService: AccountingNodeProxyPatternService,
) {
  const accountingNodes = (await accountingNodeService.findAllRawId(
    [
      ...debitAccounts.map(
        (account) => new Types.ObjectId(account.accounting_node),
      ),
      ...creditAccounts.map(
        (account) => new Types.ObjectId(account.accounting_node),
      ),
    ],
    branch,
  )) as AccountingNode[];
  if (accountingNodes.length != debitAccounts.length + creditAccounts.length) {
    throw new HttpException(
      nameOf(erpExceptionCode, (x) => x.accountingNodeNotFound),
      erpExceptionCode.accountingNodeNotFound,
    );
  }
  return accountingNodes;
}

function findVoucherType(voucher) {
  let type: journalTypes;
  switch (voucher) {
    case voucherType.payment:
      type = journalTypes.payment_voucher;
      break;
    case voucherType.receipt:
      type = journalTypes.receipt_voucher;
      break;
    case voucherType.credit_memo:
      type = journalTypes.memo;
      break;
    case voucherType.debit_memo:
      type = journalTypes.memo;
      break;
    default:
      type = journalTypes.payment_voucher;
      break;
  }
  return type;
}

function findPartnerAccountData(acData, voucherDto) {
  let partnerAccountData;
  if (voucherDto.type === voucherType.receipt) {
    partnerAccountData = acData.find((account) => {
      return (
        String(account._id) ===
        String(voucherDto.credit_account[0].accounting_node)
      );
    });
  }
  if (voucherDto.type === voucherType.payment) {
    partnerAccountData = acData.find((account) => {
      return (
        String(account._id) ===
        String(voucherDto.debit_account[0].accounting_node)
      );
    });
  }
  return partnerAccountData;
}
