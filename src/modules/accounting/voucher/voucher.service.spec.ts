import { Test, TestingModule } from '@nestjs/testing';
import { VoucherService } from './voucher.service';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { AccountingNodeService } from '../accounting-node/accounting-node.service';
import { REQUEST } from '@nestjs/core';
import { AutoNumberingService } from '../auto-numbering/auto-numbering.service';
import { getModelToken } from '@nestjs/mongoose';
import { Voucher } from './schema/voucher.schema';
import { voucherModelMock } from '../../__mocks__/models/voucher.model';
import { journalServiceMock } from '../../__mocks__/services/general-ledger.service';
import { accountingNodeServiceMock } from '../../__mocks__/services/accounting-node.service';
import { userClientMock } from '../../__mocks__/services/user-rpc.service';
import { tradeClientMock } from '../../__mocks__/services/trade-rpc.service';
import { autoNumberingServiceMock } from '../../__mocks__/services/auto-numbering.service';
import {
  paymentVoucherStub,
  receiptVoucherStub,
} from '../../__mocks__/stubs/voucher.stub';
import { requestWithUserStub } from '../../__mocks__/stubs/request-with-user.stub';
import { HttpException } from '@nestjs/common';
import { Types } from 'mongoose';
import { generalAccountingNodeStub } from '../../__mocks__/stubs/accounting-node.stub';
import { findOneReceiptVoucherResponseStub } from '../../__mocks__/stubs/find-one-voucher.respose.stub';
import { settlementType } from './dto/settlment-type.enum';
import { TransactionsService } from '../transactions/transactions.service';
import { transactionServiceMock } from '../../__mocks__/services/document-coordinator.service';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { notificationClientMock } from '../../__mocks__/services/notification-rpc.service';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { nameOf } from '../../../utils/object-key-name';

describe('VoucherService', () => {
  let service: VoucherService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VoucherService,
        { provide: getModelToken(Voucher.name), useValue: voucherModelMock },
        { provide: GeneralLedgerService, useValue: journalServiceMock() },
        {
          provide: AccountingNodeService,
          useValue: accountingNodeServiceMock(),
        },
        {
          provide: AccountingNodeProxyPatternService,
          useValue: accountingNodeServiceMock(),
        },
        { provide: REQUEST, useValue: { user: { user_id: 'testUserId' } } },
        { provide: TradeRpcService, useValue: tradeClientMock() },
        { provide: NotificationRpcService, useValue: notificationClientMock() },
        { provide: UserRpcService, useValue: userClientMock() },
        { provide: TransactionsService, useValue: transactionServiceMock() },
        {
          provide: AutoNumberingService,
          useValue: autoNumberingServiceMock(),
        },
      ],
    }).compile();

    service = await module.resolve<VoucherService>(VoucherService);
    jest.clearAllMocks();
  });

  describe('create', () => {
    describe('when create is called', () => {
      let createRequestForUserStub;
      let createReceiptVoucherStubs;
      let createPaymentVoucherStubs;
      beforeEach(() => {
        createRequestForUserStub = requestWithUserStub();
        createReceiptVoucherStubs = receiptVoucherStub();
        createPaymentVoucherStubs = paymentVoucherStub();
      });

      describe('with receipt', () => {
        it('it should throw an HttpException if the voucher amount does not balanced', async () => {
          createReceiptVoucherStubs.amount = 10;

          await expect(
            service.create(
              createReceiptVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(erpExceptionCode, (x) => x.voucherNotBalanced),
              erpExceptionCode.voucherNotBalanced,
            ),
          );
        });

        it('it should throw an HttpException if discount > 0 and branch setting discount account is missing ', async () => {
          createRequestForUserStub.policyData.sales_discount_account =
            new Types.ObjectId();
          createRequestForUserStub.policyData.sales_discount_account =
            new Types.ObjectId();

          createReceiptVoucherStubs.payment_type = String(
            receiptVoucherStub().payment_type,
          );

          await expect(
            service.create(
              createReceiptVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(erpExceptionCode, (x) => x.salesDiscountAccountNotFound),
              erpExceptionCode.salesDiscountAccountNotFound,
            ),
          );
        });

        it('it should throw an HttpException if discount account does not exists ', async () => {
          createReceiptVoucherStubs.payment_type = String(
            receiptVoucherStub().payment_type,
          );
          createRequestForUserStub.policyData.sales_discount_account =
            new Types.ObjectId();
          await expect(
            service.create(
              createReceiptVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(erpExceptionCode, (x) => x.salesDiscountAccountNotFound),
              erpExceptionCode.salesDiscountAccountNotFound,
            ),
          );
        });

        // this test commended cuz requirement of voucher changed after making sure we will remove it
        /*      it('it should throw an HttpException if payment type id not equal debit account id', async () => {
          createReceiptVoucherStubs.debit_account[0].accounting_node =
            new Types.ObjectId();

          await expect(
            service.create(
              createReceiptVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(
                erpExceptionCode,
                (x) => x.wrongPaymentTypeNotEqualDebitAccount,
              ),
              erpExceptionCode.wrongPaymentTypeNotEqualDebitAccount,
            ),
          );
        }); */

        it('it should throw an HttpException if customer does not exists', async () => {
          createReceiptVoucherStubs.payment_type = String(
            receiptVoucherStub().payment_type,
          );
          createReceiptVoucherStubs.credit_account[0].accounting_node =
            new Types.ObjectId();

          createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
            generalAccountingNodeStub()._id;

          await expect(
            service.create(
              createReceiptVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(erpExceptionCode, (x) => x.partnerNotFound),
              erpExceptionCode.partnerNotFound,
            ),
          );
        });

        // TODO:fix this test
        // it('it should throw an HttpException if accounting node of debit or credit accounts does not exists', async () => {
        //   createReceiptVoucherStubs.payment_type = String(
        //     receiptVoucherStub().payment_type,
        //   );
        //   createReceiptVoucherStubs.debit_account[0].accounting_node =
        //     new Types.ObjectId();

        //   createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
        //     generalAccountingNodeStub()._id;

        //   await expect(
        //     service.create(
        //       createReceiptVoucherStubs,
        //       null,
        //       createRequestForUserStub,
        //     ),
        //   ).rejects.toThrow(
        //     new HttpException(
        //       nameOf(erpExceptionCode, (x) => x.accountingNodeNotFound),
        //       erpExceptionCode.accountingNodeNotFound,
        //     ),
        //   );
        // });

        // it('it should create receipt voucher without credit memo if discount < 1', async () => {
        //   jest
        //     .spyOn(service, 'findOne')
        //     .mockResolvedValue(findOneReceiptVoucherResponseStub());
        //
        //   createReceiptVoucherStubs.payment_type = String(
        //     receiptVoucherStub()._id,
        //   );
        //
        //   createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
        //     generalAccountingNodeStub()._id;
        //
        //   createReceiptVoucherStubs.discount = 0;
        //
        //   const create = await service.create(
        //     createReceiptVoucherStubs,
        //     null,
        //     createRequestForUserStub,
        //   );
        //
        //   expect(create['voucher'].type).toEqual(receiptVoucherStub().type);
        //   expect(create['voucher'].debit_account[0].accounting_node).toEqual(
        //     receiptVoucherStub().debit_account[0].accounting_node,
        //   );
        //   expect(create['voucher'].credit_account[0].accounting_node).toEqual(
        //     customerStub().accounting_info.general_account._id,
        //   );
        //   expect(create['voucher'].credit_account[0].partner_id).toEqual(
        //     customerStub()._id,
        //   );
        //   expect(create['voucher'].credit_account[0].partner_type).toEqual(
        //     'customer',
        //   );
        //   expect(create['voucher']).not.toHaveProperty('memo');
        // });

        // it('it should create receipt voucher with credit memo if discount > 0', async () => {
        //   jest
        //     .spyOn(service, 'findOne')
        //     .mockResolvedValue(findOneReceiptVoucherResponseStub());
        //
        //   createReceiptVoucherStubs.payment_type = String(
        //     receiptVoucherStub()._id,
        //   );
        //
        //   createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
        //     generalAccountingNodeStub()._id;
        //
        //   const create = await service.create(
        //     createReceiptVoucherStubs,
        //     null,
        //     createRequestForUserStub,
        //   );
        //
        //   expect(create['voucher'].type).toEqual(receiptVoucherStub().type);
        //   expect(create['voucher'].debit_account[0].accounting_node).toEqual(
        //     receiptVoucherStub().debit_account[0].accounting_node,
        //   );
        //   expect(create['voucher'].credit_account[0].accounting_node).toEqual(
        //     customerStub().accounting_info.general_account._id,
        //   );
        //   expect(create['voucher'].credit_account[0].partner_id).toEqual(
        //     customerStub()._id,
        //   );
        //   expect(create['voucher'].credit_account[0].partner_type).toEqual(
        //     'customer',
        //   );
        //   expect(create['voucher']).toHaveProperty('memo');
        // });

        // it('it should create accounting journal for receipt voucher', async () => {
        //   jest
        //     .spyOn(service, 'findOne')
        //     .mockResolvedValue(findOneReceiptVoucherResponseStub());
        //
        //   createReceiptVoucherStubs.payment_type = String(
        //     receiptVoucherStub()._id,
        //   );
        //
        //   createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
        //     generalAccountingNodeStub()._id;
        //
        //   const create = await service.create(
        //     createReceiptVoucherStubs,
        //     null,
        //     createRequestForUserStub,
        //   );
        //
        //   expect(create['voucher']).toHaveProperty('journal');
        //   expect(create['voucher'].journal.type).toEqual('receipt_voucher');
        //
        //   const hasGeneralAccountAccountingNode = create[
        //     'voucher'
        //   ].journal.transactions.some(
        //     (transaction) =>
        //       transaction.accountingNode ===
        //       customerStub().accounting_info.general_account._id,
        //   );
        //   const hasCustomerAccountAccountingNode = create[
        //     'voucher'
        //   ].journal.transactions.some(
        //     (transaction) =>
        //       transaction.accountingNode === generalAccountingNodeStub()._id,
        //   );
        //
        //   expect(hasCustomerAccountAccountingNode).toBe(true);
        //   expect(hasGeneralAccountAccountingNode).toBe(true);
        // });

        it('it should Http Exception if create receipt voucher amount not equal credit + debit + invoices ', async () => {
          jest
            .spyOn(service, 'findOne')
            .mockResolvedValue(findOneReceiptVoucherResponseStub());

          createReceiptVoucherStubs.payment_type = String(
            receiptVoucherStub().payment_type,
          );
          createReceiptVoucherStubs.settlement_type =
            settlementType.settlement_of_invoice;
          createReceiptVoucherStubs.invoice = [
            // eslint-disable-next-line @typescript-eslint/naming-convention
            { Invoice: '65fccae8aad505ddf1959321', amount: 14 },
          ];

          createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
            generalAccountingNodeStub()._id;

          await expect(
            service.create(
              createReceiptVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(erpExceptionCode, (x) => x.voucherNotBalanced),
              erpExceptionCode.voucherNotBalanced,
            ),
          );
        });

        // it('it should create receipt voucher with settlement ', async () => {
        //   jest
        //     .spyOn(service, 'findOne')
        //     .mockResolvedValue(findOneReceiptVoucherResponseStub());
        //
        //   createReceiptVoucherStubs.payment_type = String(
        //     receiptVoucherStub()._id,
        //   );
        //   createReceiptVoucherStubs.settlement_type =
        //     SettlementType.settlement_of_invoice;
        //   createReceiptVoucherStubs.invoice = [
        //     { Invoice: '65fccae8aad505ddf1959321', amount: 123 },
        //   ];
        //
        //   createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
        //     generalAccountingNodeStub()._id;
        //
        //   const create = await service.create(
        //     createReceiptVoucherStubs,
        //     null,
        //     createRequestForUserStub,
        //   );
        //
        //   expect(create['voucher'].journal.type).toEqual('receipt_voucher');
        // });
      });

      describe('with  payment', () => {
        it('it should throw an HttpException if vendor does not exists', async () => {
          createPaymentVoucherStubs.payment_type = String(
            paymentVoucherStub().payment_type,
          );
          createPaymentVoucherStubs.debit_account[0].accounting_node =
            new Types.ObjectId();

          createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
            generalAccountingNodeStub()._id;

          await expect(
            service.create(
              createPaymentVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(erpExceptionCode, (x) => x.partnerNotFound),
              erpExceptionCode.partnerNotFound,
            ),
          );
        });

        it('it should throw an HttpException if tax amount not valid', async () => {
          delete createPaymentVoucherStubs.payment_type;
          createPaymentVoucherStubs.is_include_tax = true;
          createPaymentVoucherStubs.tax_amount = 1;
          createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
            generalAccountingNodeStub()._id;

          await expect(
            service.create(
              createPaymentVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(erpExceptionCode, (x) => x.taxAmountNotValid),
              erpExceptionCode.taxAmountNotValid,
            ),
          );
        });

        it('it should Http Exception if create payment voucher amount not equal credit + debit + invoices ', async () => {
          jest
            .spyOn(service, 'findOne')
            .mockResolvedValue(findOneReceiptVoucherResponseStub());

          createPaymentVoucherStubs.payment_type = String(
            paymentVoucherStub().payment_type,
          );
          createPaymentVoucherStubs.settlement_type =
            settlementType.settlement_of_invoice;
          createPaymentVoucherStubs.invoice = [
            // eslint-disable-next-line @typescript-eslint/naming-convention
            { Invoice: '65fccae8aad505ddf1959321', amount: 14 },
          ];

          createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
            generalAccountingNodeStub()._id;

          await expect(
            service.create(
              createPaymentVoucherStubs,
              null,
              createRequestForUserStub,
            ),
          ).rejects.toThrow(
            new HttpException(
              nameOf(erpExceptionCode, (x) => x.voucherNotBalanced),
              erpExceptionCode.voucherNotBalanced,
            ),
          );
        });

        // it('it should create payment voucher with settlement ', async () => {
        //   jest
        //     .spyOn(service, 'findOne')
        //     .mockResolvedValue(findOneReceiptVoucherResponseStub());
        //
        //   createPaymentVoucherStubs.payment_type = String(
        //     paymentVoucherStub()._id,
        //   );
        //   createPaymentVoucherStubs.settlement_type =
        //     SettlementType.settlement_of_invoice;
        //   createPaymentVoucherStubs.invoice = [
        //     { Invoice: '65fccae8aad505ddf1959321', amount: 123 },
        //   ];
        //
        //   createRequestForUserStub.settings[0].settings.global_accounts.sales_accounts.sales_discount_account =
        //     generalAccountingNodeStub()._id;
        //
        //   const create = await service.create(
        //     createPaymentVoucherStubs,
        //     null,
        //     createRequestForUserStub,
        //   );
        //
        //   expect(create['voucher'].journal.type).toEqual('payment_voucher');
        // });
      });
    });
  });
});
