import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { PaymentTypeModel } from '../../../users/payment-types/model/payment-type.model';
import { SalesRepresentativeModel } from '../../../trade/sales-representative/model/sales-representative.model';
import { VoucherTransactionModel } from './voucher-transaction.model';

@Table({
  tableName: 'vouchers',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class VoucherModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  number: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  type: string;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: true,
  })
  amount: number;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: true,
  })
  tax_amount: number;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  branch_id: number;

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
  })
  discount: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  invoice_party_type: string;

  @ForeignKey(() => VoucherModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  memo_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  reference: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  note: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: true,
    defaultValue: DataType.NOW,
  })
  transaction_date: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  settlement_type: string;

  @ForeignKey(() => PaymentTypeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  payment_type_id: number;

  @ForeignKey(() => SalesRepresentativeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  sales_representative_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  is_include_tax: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  auto_generated: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // Relations
  @BelongsTo(() => VoucherTransactionModel, {
    foreignKey: 'id',
    targetKey: 'voucher_id',
    scope: {
      transactionType: 'debit',
    },
  })
  debitAccountTransaction: VoucherTransactionModel[];

  @BelongsTo(() => VoucherTransactionModel, {
    foreignKey: 'id',
    targetKey: 'voucher_id',
    scope: {
      transactionType: 'credit',
    },
  })
  creditAccountTransaction: VoucherTransactionModel[];

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;

  @BelongsTo(() => VoucherModel, 'memo_id')
  memo: VoucherModel;

  @BelongsTo(() => PaymentTypeModel, 'payment_type_id')
  paymentType: PaymentTypeModel;

  @BelongsTo(() => SalesRepresentativeModel, 'sales_representative_id')
  salesRepresentative: SalesRepresentativeModel;
}
