import {
  Table,
  Column,
  Model,
  DataType,
  CreatedAt,
  UpdatedAt,
  DeletedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { AccountingNodeModel } from '../../accounting-node/model/accounting-node.model';
//import { PartnerModel } from '../../../trade/partners/model/partner.model';
import { VoucherModel } from './voucher.model';

@Table({
  tableName: 'voucher_transactions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class VoucherTransactionModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => VoucherModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    field: 'voucher_id',
  })
  voucher_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: 'Type of transaction - debit or credit',
  })
  transaction_type: 'debit' | 'credit';

  @Column({
    type: DataType.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0,
    field: 'amount',
  })
  amount: number;

  @ForeignKey(() => AccountingNodeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  accounting_node_id: number;

  //  @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  partner_id: number;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @DeletedAt
  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    field: 'mongo_id',
  })
  mongoId: string;

  // Relations
  @BelongsTo(() => VoucherModel, 'voucher_id')
  voucher: VoucherModel;

  @BelongsTo(() => AccountingNodeModel, 'accounting_node_id')
  accountingNode: AccountingNodeModel;

  /*   @BelongsTo(() => PartnerModel, 'partner_id')
  partner: PartnerModel; */
}
