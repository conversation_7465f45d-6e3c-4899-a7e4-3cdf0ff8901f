import { VoucherService } from '../voucher.service';
import { Test, TestingModule } from '@nestjs/testing';
import { PolicyService } from '../../../policy/policy.service';
import { TenantRpcService } from '../../../rpc/tenant-rpc.service';
import { Voucher } from '../schema/voucher.schema';
import { receiptVoucherStub } from '../../../__mocks__/stubs/voucher.stub';
import { requestWithUserStub } from '../../../__mocks__/stubs/request-with-user.stub';
import { CreateVoucherDto } from '../dto/create-voucher.dto';
import { ReceiptVoucherController } from './receipt-voucher.controller';
import { CaslAbilityFactory } from '../../../casl/casl-ability.factory';

// No need for jest.mock since we're importing the mock directly

describe('ReceiptVoucherController', () => {
  let controller: ReceiptVoucherController;
  let service: VoucherService;

  beforeEach(async () => {
    // Import the mock directly
    const { VoucherService: mockVoucherService } = await import(
      '../../../__mocks__/services/voucher.service'
    );

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReceiptVoucherController],
      providers: [
        {
          provide: VoucherService,
          useFactory: () => mockVoucherService(),
        },
        {
          provide: CaslAbilityFactory,
          useValue: {},
        },
        {
          provide: PolicyService,
          useValue: {},
        },
        {
          provide: TenantRpcService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<ReceiptVoucherController>(ReceiptVoucherController);
    service = module.get<VoucherService>(VoucherService);
    jest.clearAllMocks();
  });

  describe('create', () => {
    describe('when create is called', () => {
      describe('when create is called with receipt', () => {
        let voucher: Voucher | any;

        beforeEach(async () => {
          voucher = await controller.create(
            receiptVoucherStub() as unknown as CreateVoucherDto,
            requestWithUserStub(),
          );
        });

        test('then it should call voucher service with receipt', async () => {
          expect(service.create).toHaveBeenCalledWith(
            expect.objectContaining(
              receiptVoucherStub() as unknown as CreateVoucherDto,
            ),
            null,
            expect.objectContaining(requestWithUserStub()),
          );
        });

        test('then it should return a payment voucher', async () => {
          expect(voucher).toEqual(receiptVoucherStub());
        });
      });
    });
  });
});
