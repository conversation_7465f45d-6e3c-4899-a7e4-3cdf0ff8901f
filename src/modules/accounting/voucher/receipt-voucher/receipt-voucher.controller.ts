import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { EventPattern, MessagePattern, Payload } from '@nestjs/microservices';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { VoucherService } from '../voucher.service';
import { FeatureAccessibilityGuard } from '../../../casl/guards/feature-accessibility.guard';
import { CaslGuard } from '../../../casl/guards/casl.guard';
import { feature } from '../../../casl/guards/feature.decorator';
import { abilities } from '../../../casl/guards/abilities.decorator';
import { PolicyInterceptor } from '../../../policy/policy.interceptor';
import { serviceName } from '../../../policy/service-name.decorator';
import { RequestWithUser } from '../../../auth/interfaces/authorize.interface';
import { VoucherWithMeta } from '../exportTypes/voucherResponse.types';
import { FindAllVoucherDto } from '../dto/get-voucher.dto';
import { CreateVoucherDto } from '../dto/create-voucher.dto';
import { GetTemplateDto } from '../dto/get-template.dto';
import { Voucher } from '../schema/voucher.schema';
import { UpdateVoucherDto } from '../dto/update-voucher.dto';
import { publicRpc } from '../../../auth/guards/public-event.decorator';
import { RollbackVoucherRpcDto } from '../dto/rollback-voucher-rpc.dto';
import { voucherType } from '../dto/voucher-type.enum';
import { RouteRules } from '../../../../interceptor/routes-rule.interceptor';
import { accountingMessagePattern } from '../../../../utils/queues.enum';

@ApiTags('receipt-voucher')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('vouchers/receipt')
export class ReceiptVoucherController {
  constructor(private readonly voucherService: VoucherService) {}

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'receipt_voucher' })
  @abilities({ action: 'create', subject: 'receipt_voucher' })
  @Post()
  @UseInterceptors(PolicyInterceptor)
  @serviceName([
    'receipt_voucher',
    'debit_memo',
    'credit_memo',
    'purchase_tax_account',
    'sales_discount_account',
  ])
  async create(
    @Body() createVoucherDto: CreateVoucherDto,
    @Req() request: RequestWithUser,
  ) {
    createVoucherDto.type = voucherType.receipt;
    return this.voucherService.create(createVoucherDto, null, request);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'receipt_voucher' })
  @abilities({ action: 'list', subject: 'receipt_voucher' })
  @Get()
  findAll(@Query() query: FindAllVoucherDto): Promise<VoucherWithMeta> {
    query.type = voucherType.receipt;
    return this.voucherService.findAll(query);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'receipt_voucher' })
  @abilities({ action: 'read', subject: 'receipt_voucher' })
  @Get(':id')
  findOne(@Param('id') _id: string): Promise<Voucher> {
    return this.voucherService.findOne({ _id });
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'receipt_voucher' })
  @abilities({ action: 'read', subject: 'receipt_voucher' })
  @Post('template')
  printOne(@Body() getTemplateDto: GetTemplateDto): Promise<any> {
    return this.voucherService.printOne(getTemplateDto);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'receipt_voucher' })
  @abilities({ action: 'edit', subject: 'receipt_voucher' })
  @Patch(':id')
  @UseInterceptors(PolicyInterceptor, RouteRules)
  @serviceName([
    'receipt_voucher',
    'debit_memo',
    'credit_memo',
    'purchase_tax_account',
    'sales_discount_account',
    'allow_documents_edition',
    'document_policy',
    'document_policy_status',
  ])
  update(
    @Param('id') id: string,
    @Body() updateVoucherDto: UpdateVoucherDto,
    @Req() req: RequestWithUser,
  ) {
    return this.voucherService.update(id, updateVoucherDto, req);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'receipt_voucher' })
  @abilities({ action: 'delete', subject: 'receipt_voucher' })
  @UseInterceptors(PolicyInterceptor, RouteRules)
  @serviceName([
    'allow_documents_deletion',
    'document_policy',
    'document_policy_status',
  ])
  @Delete(':id')
  async remove(
    @Param('id') _id: string,
    @Req() request: RequestWithUser,
  ): Promise<any> {
    return this.voucherService.remove(
      _id,
      request,
      new Types.ObjectId(request?.headers['branch']),
    );
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.create_receipt_voucher })
  @UseInterceptors(PolicyInterceptor)
  @serviceName([
    'receipt_voucher',
    'purchase_tax_account',
    'sales_discount_account',
  ])
  async createRpc(
    @Payload() payload: { data: CreateVoucherDto; policyData: any },
  ) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { code, policyData, ...result } = payload as any;

    return this.voucherService.create(
      result,
      payload.policyData,
      null,
      code,
      result.branch,
    );
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.rollback_receipt_voucher })
  async rollbackVoucher(@Payload() payload: RollbackVoucherRpcDto) {
    return this.voucherService.rollback(payload._id);
  }

  @publicRpc()
  @EventPattern({
    cmd: accountingMessagePattern.rollback_receipt_voucher_action,
  })
  async rollbackVoucherAction(@Payload() payload: any) {
    console.log({ payload });

    return this.voucherService.rollbackAction(
      payload._id,
      payload.action,
      payload.erp_document,
    );
  }
}
