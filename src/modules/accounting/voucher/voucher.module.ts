import { Module } from '@nestjs/common';
import { VoucherService } from './voucher.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Voucher, voucherSchema } from './schema/voucher.schema';
import { GeneralLedgerModule } from '../general-ledger/general-ledger.module';
import { AccountingNodeModule } from '../accounting-node/accounting-node.module';
import { AutoNumberingModule } from '../auto-numbering/auto-numbering.module';
import { PaymentVoucherController } from './payment-voucher/payment-voucher.controller';
import { ReceiptVoucherController } from './receipt-voucher/receipt-voucher.controller';
import { MemoVoucherController } from './memo-voucher/memo-voucher.controller';
import { ProxyPatternModule } from '../proxy-pattern/proxy-pattern.module';
import { RpcModule } from '../../rpc/rpc.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { VoucherModel } from './model/voucher.model';
import { VoucherTransactionModel } from './model/voucher-transaction.model';

@Module({
  imports: [
    AutoNumberingModule,
    SequelizeModule.forFeature([VoucherModel, VoucherTransactionModel]),
    MongooseModule.forFeature([{ name: Voucher.name, schema: voucherSchema }]),
    AccountingNodeModule,
    GeneralLedgerModule,
    ProxyPatternModule,
    RpcModule,
  ],
  controllers: [
    PaymentVoucherController,
    ReceiptVoucherController,
    MemoVoucherController,
  ],
  providers: [VoucherService],
  exports: [VoucherService],
})
export class VoucherModule {}
