import { Types } from 'mongoose';
import { IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';
import { RpcDto } from '../../../../utils/dto/rpc.dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class RollbackVoucherRpcDto extends RpcDto {
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  _id?: Types.ObjectId;
}
