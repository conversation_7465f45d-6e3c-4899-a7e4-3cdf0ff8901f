import { Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsDate,
  IsEnum,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { IsInt } from '@nestjs/class-validator';
import { Transform, Type } from 'class-transformer';
import { voucherType } from './voucher-type.enum';
import { voucherPartyType } from './voucher-party.enum';
import { settlementType } from './settlment-type.enum';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class FindAllVoucherDto {
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public from_date?: string;

  @Type(() => Date)
  @IsDate()
  @IsOptional()
  public to_date?: string;

  @ApiProperty({
    description: 'credit_account',
    example: '63400372c582a633284bf24c',
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  credit_account?: Types.ObjectId;

  @ApiProperty({
    description: 'debit_account',
    example: '63400372c582a633284bf24c',
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  debit_account?: Types.ObjectId;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public page?: number = 1;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  public limit?: number = 10;

  @IsOptional()
  @IsString()
  public queries?: string;

  @IsOptional()
  @IsNumber()
  number?: number;

  @ApiProperty({
    description: 'type of voucher',
    example: 'payment',
    required: false,
  })
  @IsOptional()
  @IsEnum(voucherType)
  type?: voucherType;

  @ApiProperty({
    description: 'amount of voucher',
    example: 1200,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  amount?: number;

  @ApiProperty({
    description: 'journal id of voucher',
    example: '63400372c582a633284bf24c',
    required: false,
    type: 'string',
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  journal?: Types.ObjectId;

  @ApiProperty({
    description: 'branch id of form account of voucher',
    example: '63400372c582a633284bf24c',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'description of voucher',
    example: 'test',
    required: false,
  })
  description?: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    description: 'amount of discount in voucher',
    example: 10,
    required: false,
  })
  discount?: number;

  @ApiProperty({
    description: 'invoice_party_type of voucher',
    example: 'customer',
    required: false,
  })
  @IsEnum(voucherPartyType)
  @IsOptional()
  invoice_party_type?: voucherPartyType;

  by_user?: Types.ObjectId;

  @IsString()
  @IsOptional()
  fromNumber?: string;

  @ApiProperty({
    description: 'Statment to date',
    example: '2022-2-30',
    format: 'date',
  })
  @IsString()
  @IsOptional()
  toNumber?: string;

  @IsEnum(settlementType)
  @IsOptional()
  @ApiProperty({
    description: 'settlement_type',
    example: settlementType.on_account,
    required: false,
  })
  settlement_type?: settlementType;

  createdAt?: any;
  transaction_date?: any;
}
