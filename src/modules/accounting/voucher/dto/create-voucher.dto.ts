import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { IsNumber } from 'class-validator';
import { Types } from 'mongoose';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { voucherType } from './voucher-type.enum';
import { voucherPartyType } from './voucher-party.enum';
import { Transform, Type } from 'class-transformer';
import { settlementType } from './settlment-type.enum';
import { PrintOptionsDto } from './print-options.dto';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class Accounts {
  @ApiProperty({
    description: 'id of account',
    required: true,
    example: 'test',
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  // TODO: remove string and solve errors
  accounting_node: string | Types.ObjectId;

  @ApiProperty({
    description: 'type of voucher',
    example: 100,
    required: true,
  })
  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  amount: number;

  @ApiProperty()
  @IsOptional()
  // TODO: remove string and solve errors
  partner_id?: string | Types.ObjectId;

  @ApiProperty({ type: String, example: 'vendor' })
  @IsOptional()
  @IsString()
  partner_type?: string;
}

export class Invoice {
  @ApiProperty({
    description: 'id of invoice',
    required: true,
    example: 'test',
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  Invoice: Types.ObjectId;

  @ApiProperty({
    description: 'type of voucher',
    example: 100,
    required: true,
  })
  @IsNumber()
  amount: number;
}

export class CreateVoucherDto {
  @IsOptional()
  @IsNumber()
  number?: number;

  @ApiProperty({
    description: 'type of voucher',
    example: 'payment',
    required: true,
  })
  @IsEnum(voucherType)
  type: voucherType;

  @ValidateIf(
    (o) =>
      o.type == voucherType.payment ||
      o.type == voucherType.debit_memo ||
      o.type == voucherType.credit_memo,
  )
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => Accounts)
  debit_account: Accounts[];

  @ValidateIf(
    (o) =>
      o.type == voucherType.receipt ||
      o.type == voucherType.credit_memo ||
      o.type == voucherType.debit_memo,
  )
  @ValidateNested()
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => Accounts)
  credit_account: Accounts[];

  @ValidateIf((o) => o.type === voucherType.payment)
  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    description: 'tax toggle ',
    example: false,
    required: false,
  })
  is_include_tax: boolean;

  @ValidateIf((o) => o.is_include_tax === true)
  @ApiProperty({
    description: 'amount of voucher',
    example: 1200,
    required: true,
  })
  @IsNumber()
  tax_amount: number;

  @ApiProperty({
    description: 'amount of voucher',
    example: 1200,
    required: true,
  })
  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  amount: number;

  @ApiProperty({
    description: 'payment type id',
    example: '63400372c582a633284bf24c',
    required: false,
  })
  @ValidateIf(
    (o) => o.type == voucherType.receipt || o.type == voucherType.payment,
  )
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  payment_type?: Types.ObjectId;

  @ApiProperty({
    description: 'sales representative id',
    example: '63400372c582a633284bf24c',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  sales_representative?: Types.ObjectId;

  @ApiProperty({
    description: 'branch id of form account of voucher',
    example: '63400372c582a633284bf24c',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'description of voucher',
    example: 'test',
    required: false,
  })
  description?: string;

  // eslint-disable-next-line @typescript-eslint/naming-convention
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  @ApiProperty({
    description: 'amount of discount in voucher',
    example: 10,
    required: false,
  })
  discount?: number;

  @ApiProperty({
    description: 'invoice_party_type of voucher',
    example: 'customer',
    required: true,
  })
  @IsEnum(voucherPartyType)
  @IsOptional()
  invoice_party_type?: voucherPartyType;

  @ApiHideProperty()
  memo?: Types.ObjectId | string;

  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsOptional()
  reference?: string;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  @ApiProperty({
    description: 'date journal',
    example: new Date(),
    required: false,
  })
  transaction_date?: Date;

  @IsArray()
  @IsOptional()
  @ApiProperty({
    description: 'invoice id',
    required: false,
    example: Invoice,
    isArray: true,
    type: Invoice,
  })
  @ValidateNested()
  @Type(() => Invoice)
  invoice: Invoice[];

  @IsEnum(settlementType)
  @IsOptional()
  @ApiProperty({
    description: 'settlement_type',
    example: settlementType.on_account,
    required: false,
  })
  settlement_type: settlementType;

  @ApiHideProperty()
  by_user?: Types.ObjectId;

  @ApiHideProperty()
  journal?: Types.ObjectId;

  @ApiProperty()
  @ValidateNested()
  @Type(() => PrintOptionsDto)
  printOptions: PrintOptionsDto;
}

export class CreateVoucherRpcDto {
  @IsOptional()
  _id;
  @IsOptional()
  @IsNumber()
  number?: number;

  @ApiProperty({
    description: 'type of voucher',
    example: 'payment',
    required: true,
  })
  @IsEnum(voucherType)
  type: voucherType;

  @ValidateIf(
    (o) =>
      o.type == voucherType.payment ||
      o.type == voucherType.debit_memo ||
      o.type == voucherType.credit_memo,
  )
  @ValidateNested({ each: true })
  @Type(() => Accounts)
  debit_account: Accounts;

  @ValidateIf(
    (o) =>
      o.type == voucherType.receipt ||
      o.type == voucherType.credit_memo ||
      o.type == voucherType.debit_memo,
  )
  @ValidateNested()
  @Type(() => Accounts)
  credit_account: Accounts;

  @ApiProperty({
    description: 'amount of voucher',
    example: 1200,
    required: true,
  })
  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  amount: number;

  @ApiProperty({
    description: 'sales representative id',
    example: '63400372c582a633284bf24c',
    required: false,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  sales_representative?: Types.ObjectId;

  @ApiProperty({
    description: 'branch id of form account of voucher',
    example: '63400372c582a633284bf24c',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch?: Types.ObjectId;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'description of voucher',
    example: 'test',
    required: false,
  })
  description?: string;

  // eslint-disable-next-line @typescript-eslint/naming-convention
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  @ApiProperty({
    description: 'amount of discount in voucher',
    example: 10,
    required: false,
  })
  discount?: number;

  @ApiProperty({
    description: 'invoice_party_type of voucher',
    example: 'customer',
    required: true,
  })
  @IsEnum(voucherPartyType)
  @IsOptional()
  invoice_party_type?: voucherPartyType;

  @ApiHideProperty()
  memo?: Types.ObjectId | string;

  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsOptional()
  reference?: string;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  @ApiProperty({
    description: 'date journal',
    example: new Date(),
    required: false,
  })
  transaction_date?: Date;

  @IsArray()
  @IsOptional()
  @ApiProperty({
    description: 'invoice id',
    required: false,
    example: Invoice,
    isArray: true,
    type: Invoice,
  })
  @ValidateNested()
  @Type(() => Invoice)
  invoice: Invoice[];

  @IsEnum(settlementType)
  @IsOptional()
  @ApiProperty({
    description: 'settlement_type',
    example: settlementType.on_account,
    required: false,
  })
  settlement_type: settlementType;

  @ApiHideProperty()
  by_user?: Types.ObjectId;

  @ApiHideProperty()
  journal?: Types.ObjectId;

  @ApiHideProperty()
  @IsOptional()
  @IsBoolean()
  auto_generated?: boolean;
}
