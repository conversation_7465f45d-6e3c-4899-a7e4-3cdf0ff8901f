import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { IsNumber } from 'class-validator';
import { Types } from 'mongoose';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { voucherType } from './voucher-type.enum';
import { Transform, Type } from 'class-transformer';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class Accounts {
  @ApiProperty({
    description: 'id of account',
    required: true,
    example: 'test',
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  // TODO: remove string and solve the errors???
  accounting_node: string | Types.ObjectId;

  @ApiProperty({
    description: 'amount',
    example: 1100,
    required: true,
  })
  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  amount: number;

  @ApiHideProperty()
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  // TODO: remove string and solve the errors???
  partner_id?: string | Types.ObjectId;

  @ApiHideProperty()
  @IsOptional()
  partner_type?: string;
}
export class UpdateMomoDto {
  @ApiProperty({
    description: 'type of voucher',
    example: 'debit_memo',
    required: true,
  })
  @IsEnum(voucherType)
  type: voucherType;

  @ApiProperty({
    description: 'branch id',
    example: '64956ef5f506e9edf21df4e8',
    type: 'string',
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  branch: Types.ObjectId;

  @ValidateIf(
    (o) =>
      (o.type === voucherType.payment && o.debit_account.length === 1) ||
      (o.type === voucherType.receipt && o.debit_account.length >= 1),
  )
  @ValidateNested()
  @Type(() => Accounts)
  debit_account: Accounts[];

  @ValidateNested()
  @ValidateIf(
    (o) =>
      (o.type === voucherType.receipt && o.credit_account.length === 1) ||
      (o.type === voucherType.payment && o.credit_account.length >= 1),
  )
  @Type(() => Accounts)
  credit_account: Accounts[];

  @ApiProperty({
    description: 'amount of voucher',
    example: 1200,
    required: true,
  })
  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  amount: number;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'description of voucher',
    example: 'test',
    required: false,
  })
  description?: string;

  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsOptional()
  reference?: string;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  @ApiProperty({
    description: 'date journal',
    example: new Date(),
    required: false,
  })
  transaction_date?: Date;

  @ApiHideProperty()
  journal?: Types.ObjectId;

  @ApiHideProperty()
  by_user?: Types.ObjectId;
}
