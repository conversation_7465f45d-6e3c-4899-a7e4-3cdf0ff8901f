import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsIn,
  IsOptional,
  IsString,
} from 'class-validator';

export enum templateType {
  tax_invoice = 'tax_invoice',
  simplified_tax_invoice = 'simplified_tax_invoice',
  partner_payment_voucher = 'partner_payment_voucher',
  general_payment_voucher = 'general_payment_voucher',
  partner_receipt_voucher = 'partner_receipt_voucher',
  general_receipt_voucher = 'general_receipt_voucher',
  account_journal = 'account_journal',
  debit_credit_memo = 'debit_credit_memo',
}

export class PrintOptionsDto {
  @ApiProperty({ example: 'en' })
  @IsString()
  @IsIn(['en', 'ar'])
  templateLang: string;

  @ApiProperty({ example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty({ example: '+************', required: false })
  @IsOptional()
  @IsString()
  phoneNumber: string;

  @ApiProperty()
  @IsBoolean()
  sendEmail: boolean;

  @ApiProperty()
  @IsBoolean()
  sendSms: boolean;

  @ApiProperty()
  @IsBoolean()
  print: boolean;
}
