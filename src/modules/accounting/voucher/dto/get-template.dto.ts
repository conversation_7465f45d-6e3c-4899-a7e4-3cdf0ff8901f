import { ApiProperty } from '@nestjs/swagger';
import { PrintOptionsDto } from './print-options.dto';
import { Transform } from 'class-transformer';

import { Types } from 'mongoose';
import { IsNotEmpty } from 'class-validator';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class GetTemplateDto extends PrintOptionsDto {
  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  voucherId: Types.ObjectId;
}
