import { HttpException, Inject, Injectable, Scope } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import {
  CreateVoucherDto,
  CreateVoucherRpcDto,
  Invoice,
} from './dto/create-voucher.dto';
import { UpdateVoucherDto } from './dto/update-voucher.dto';
import { Voucher, VoucherDocument } from './schema/voucher.schema';
import { journalTypes } from '../general-ledger/schema/general-ledger.schema';
import { FindAllVoucherDto } from './dto/get-voucher.dto';
import { voucherType } from './dto/voucher-type.enum';
import { AccountingNodeDocument } from '../accounting-node/schema/accounting-node.schema';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { REQUEST } from '@nestjs/core';
import { ITransaction } from '../general-ledger/dto/transaction.dto';
import { voucherPartyType } from './dto/voucher-party.enum';
import { settlementType } from './dto/settlment-type.enum';
import { AutoNumberingService } from '../auto-numbering/auto-numbering.service';

import {
  PrintOptionsDto,
  templateType as templateEnum,
} from './dto/print-options.dto';
import { GetTemplateDto } from './dto/get-template.dto';

import { TransactionsService } from '../transactions/transactions.service';
import { action, documentType, steps } from '../transactions/dto';
import { UpdateMomoDto } from './dto/update-memo.dto';
import * as salesTax from 'sales-tax';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import {
  checkVoucherAmount,
  findAccountingNodes,
  handleVoucherCreation,
} from './voucher.helper';
import { cloneDeep, extend } from 'lodash';
import {
  compareAccounts,
  compareInvoices,
  getInvoiceDifference,
} from '../../../utils/compare-voucher';
import { IRollbackSteps } from '../transactions/interface/rollback-steps.interface';
import { documentPolicyEnum } from '../../policy/enum/document-policy.enum';
import { AccountingNodeProxyPatternService } from '../proxy-pattern/accounting-node-pattern.service';
import { SoftDeleteModel } from '../../../utils/schemas';
import { accountingMessagePattern } from '../../../utils/queues.enum';
import { handleGlobalEditRestriction } from '../../../utils/global-edit-restriction';
import { nameOf } from '../../../utils/object-key-name';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { isEmpty } from '../../../utils/is-object-empty';
import { paginate } from '../../../utils/dto';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class VoucherService {
  constructor(
    @InjectModel(Voucher.name)
    private voucherModel: SoftDeleteModel<VoucherDocument>,
    private readonly accountingNodeProxyPatternService: AccountingNodeProxyPatternService,
    @Inject(REQUEST) private readonly request: RequestWithUser | any,
    private readonly userRpcService: UserRpcService,
    private readonly tradeRpcService: TradeRpcService,
    @Inject(AutoNumberingService)
    private autoNumberingService: AutoNumberingService,

    public readonly notificationRpcService: NotificationRpcService,
    private readonly transactionsService: TransactionsService,
  ) {}

  async create(
    createVoucherDto: CreateVoucherDto,
    policyData: any = undefined,
    request: RequestWithUser,
    code: number = null,
    branch: Types.ObjectId = null,
  ) {
    if (
      createVoucherDto.transaction_date === undefined ||
      createVoucherDto.transaction_date === null
    ) {
      createVoucherDto.transaction_date = new Date();
    }

    const correlationId = await this.transactionsService.executeERPDocumentSaga(
      request ? request.headers['branch'] : createVoucherDto.branch,
      documentType.voucher,
      action.create,
      [steps.erp_document, steps.accounting_journaling],
      [
        steps.sales_invoice,
        steps.purchase_invoice,
        steps.credit_debit_memo_voucher,
        steps.credit_debit_memo_voucher_journaling,
      ],
    );
    console.log('himsadasdasd');

    let documentId,
      voucher,
      branchId,
      voucherTypeRollback = null;
    try {
      if (request) {
        createVoucherDto.by_user = request.user.user_id;
      }

      const policies = policyData || request.policyData;
      branchId = branch || new Types.ObjectId(request.headers['branch']);
      const tenantCode = code || request?.user?.code;

      const validated = await handleVoucherCreation(
        createVoucherDto,
        tenantCode,
        branchId,
        policies,
        this.accountingNodeProxyPatternService,
        this.tradeRpcService,
        this.userRpcService,
      );
      const {
        type,
        transactions,
        debitPartnerId,
        creditPartnerId,
        discountAccount,
        partnerAccountData,
      } = validated;

      const receiptVoucherDocument = policies.receipt_voucher;
      const debitMemoDocument = policies.debit_memo;
      const creditMemoDocument = policies.credit_memo;
      const paymentVoucherCashDocument = policies.payment_voucher_cash;

      // numbering logic
      console.log(createVoucherDto.type, 'createVoucherDto.type');

      switch (createVoucherDto.type) {
        case 'receipt':
          voucherTypeRollback =
            accountingMessagePattern.rollback_receipt_voucher_action;
          if (receiptVoucherDocument.auto_serial == true) {
            if (receiptVoucherDocument.same_serial == true) {
              createVoucherDto.number =
                await this.autoNumberingService.getNextSequence(
                  this.voucherModel,
                  [
                    'receipt_voucher.cash_serial',
                    'receipt_voucher.credit_serial',
                  ],
                  createVoucherDto.branch,
                  voucherType.receipt,
                );
            } else {
              createVoucherDto.number =
                await this.autoNumberingService.getNextSequence(
                  this.voucherModel,
                  ['receipt_voucher.cash_serial'],
                  createVoucherDto.branch,
                  voucherType.receipt,
                );
            }
          } else {
            if (createVoucherDto.number) {
              const numberExist = await this.findOneNumbering(
                createVoucherDto.number,
                createVoucherDto.branch,
                voucherType.receipt,
              );
              if (numberExist) {
                throw new HttpException(
                  nameOf(erpExceptionCode, (x) => x.numberDuplicated),
                  erpExceptionCode.numberDuplicated,
                );
              }
            }
            if (typeof createVoucherDto.number === 'undefined') {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.numberNotExist),
                erpExceptionCode.numberNotExist,
              );
            }
            await this.autoNumberingService.updateSequence(
              createVoucherDto.number,
              'receipt_voucher.cash_serial',
              createVoucherDto.branch,
            );
          }

          break;
        case 'payment':
          voucherTypeRollback =
            accountingMessagePattern.rollback_payment_voucher_action;
          if (paymentVoucherCashDocument.auto_serial == true) {
            if (paymentVoucherCashDocument.same_serial == true) {
              createVoucherDto.number =
                await this.autoNumberingService.getNextSequence(
                  this.voucherModel,
                  [
                    'payment_voucher_cash.cash_serial',
                    'payment_voucher_cash.credit_serial',
                  ],
                  createVoucherDto.branch,
                  voucherType.payment,
                );
            } else {
              createVoucherDto.number =
                await this.autoNumberingService.getNextSequence(
                  this.voucherModel,
                  ['payment_voucher_cash.cash_serial'],
                  createVoucherDto.branch,
                  voucherType.payment,
                );
            }
          } else {
            if (createVoucherDto.number) {
              const numberExist = await this.findOneNumbering(
                createVoucherDto.number,
                createVoucherDto.branch,
                voucherType.payment,
              );
              if (numberExist) {
                throw new HttpException(
                  nameOf(erpExceptionCode, (x) => x.numberDuplicated),
                  erpExceptionCode.numberDuplicated,
                );
              }
            }
            if (typeof createVoucherDto.number === 'undefined') {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.numberNotExist),
                erpExceptionCode.numberNotExist,
              );
            }
            await this.autoNumberingService.updateSequence(
              createVoucherDto.number,
              'payment_voucher_cash.cash_serial',
              createVoucherDto.branch,
            );
          }
          break;
        case 'debit_memo':
          voucherTypeRollback =
            accountingMessagePattern.rollback_memo_voucher_action;
          if (debitMemoDocument.auto_serial == true) {
            if (debitMemoDocument.same_serial == true) {
              createVoucherDto.number =
                await this.autoNumberingService.getNextSequence(
                  this.voucherModel,
                  ['debit_memo.cash_serial', 'debit_memo.credit_serial'],
                  createVoucherDto.branch,
                  voucherType.debit_memo,
                );
            } else {
              createVoucherDto.number =
                await this.autoNumberingService.getNextSequence(
                  this.voucherModel,
                  ['debit_memo.cash_serial'],
                  createVoucherDto.branch,
                  voucherType.debit_memo,
                );
            }
          } else {
            if (createVoucherDto.number) {
              const numberExist = await this.findOneNumbering(
                createVoucherDto.number,
                createVoucherDto.branch,
                voucherType.debit_memo,
              );
              if (numberExist) {
                throw new HttpException(
                  nameOf(erpExceptionCode, (x) => x.numberDuplicated),
                  erpExceptionCode.numberDuplicated,
                );
              }
            }
            if (typeof createVoucherDto.number === 'undefined') {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.numberNotExist),
                erpExceptionCode.numberNotExist,
              );
            }
            await this.autoNumberingService.updateSequence(
              createVoucherDto.number,
              'debit_memo.cash_serial',
              createVoucherDto.branch,
            );
          }
          break;
        case 'credit_memo':
          voucherTypeRollback =
            accountingMessagePattern.rollback_memo_voucher_action;
          if (creditMemoDocument.auto_serial == true) {
            if (creditMemoDocument.same_serial == true) {
              createVoucherDto.number =
                await this.autoNumberingService.getNextSequence(
                  this.voucherModel,
                  ['credit_memo.cash_serial', 'credit_memo.credit_serial'],
                  createVoucherDto.branch,
                  voucherType.credit_memo,
                );
            } else {
              createVoucherDto.number =
                await this.autoNumberingService.getNextSequence(
                  this.voucherModel,
                  ['credit_memo.cash_serial'],
                  createVoucherDto.branch,
                  voucherType.credit_memo,
                );
            }
          } else {
            if (createVoucherDto.number) {
              const numberExist = await this.findOneNumbering(
                createVoucherDto.number,
                createVoucherDto.branch,
                voucherType.credit_memo,
              );
              if (numberExist) {
                throw new HttpException(
                  nameOf(erpExceptionCode, (x) => x.numberDuplicated),
                  erpExceptionCode.numberDuplicated,
                );
              }
            }
            if (typeof createVoucherDto.number === 'undefined') {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.numberNotExist),
                erpExceptionCode.numberNotExist,
              );
            }
            await this.autoNumberingService.updateSequence(
              createVoucherDto.number,
              'credit_memo.cash_serial',
              createVoucherDto.branch,
            );
          }
          break;
        default:
          break;
      }

      voucher = await this.voucherModel.create(createVoucherDto);
      console.log(voucher, 'created');

      documentId = voucher._id;
      await this.transactionsService.createdERPDocument(correlationId, {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentId: documentId,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocument: voucher,

        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentPattern: voucherTypeRollback,
      });

      if (
        createVoucherDto.settlement_type == settlementType.settlement_of_invoice
      ) {
        await this.updateInvoice(
          tenantCode,
          createVoucherDto.invoice,
          createVoucherDto.type,
          createVoucherDto.amount,
        );
      }

      //discount logic
      if (
        createVoucherDto.discount > 0 &&
        createVoucherDto.discount <= createVoucherDto.amount
      ) {
        if (createVoucherDto.type === voucherType.payment) {
          const memoData = await this.createInternalMemo(
            tenantCode,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            debitMemoDocument.same_serial
              ? ['debit_memo.cash_serial', 'debit_memo.credit_serial']
              : ['debit_memo.cash_serial'],
            createVoucherDto.discount,
            voucherType.debit_memo,
            discountAccount,
            partnerAccountData,
            createVoucherDto.number,
            new Types.ObjectId(createVoucherDto.branch),
            createVoucherDto.invoice_party_type,
            createVoucherDto.transaction_date,
            createVoucherDto.by_user,
            debitPartnerId,
            null,
          );
          voucher.memo = memoData._id;
          voucher.save();
        }

        if (createVoucherDto.type === voucherType.receipt) {
          const memoData = await this.createInternalMemo(
            tenantCode,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            creditMemoDocument.same_serial
              ? ['credit_memo.cash_serial', 'credit_memo.credit_serial']
              : ['credit_memo.cash_serial'],

            createVoucherDto.discount,
            voucherType.credit_memo,
            partnerAccountData,
            discountAccount,
            createVoucherDto.number,
            new Types.ObjectId(createVoucherDto.branch),
            createVoucherDto.invoice_party_type,
            createVoucherDto.transaction_date,
            createVoucherDto.by_user,
            null,
            creditPartnerId,
          );
          voucher.memo = memoData._id;
          voucher.save();
        }
      }

      if (typeof createVoucherDto.transaction_date === 'string') {
        createVoucherDto.transaction_date = new Date(
          createVoucherDto.transaction_date,
        );
      }
      createVoucherDto.transaction_date.setSeconds(
        createVoucherDto.transaction_date.getSeconds() + 3,
      );

      const journalData = await this.transactionsService.journalAccounting(
        {
          by_user: createVoucherDto.by_user,
          type,
          transaction_date: createVoucherDto.transaction_date,
          transactions: transactions,
          note: createVoucherDto.note,
          reference: createVoucherDto.reference,
          branch: createVoucherDto.branch,
          document: voucher._id,
          document_code: voucher.number,
        } as any,
        tenantCode,
      );

      voucher.journal = new Types.ObjectId(journalData._id);

      await voucher.save();

      await this.transactionsService.transactionCompleted(correlationId);

      if (createVoucherDto?.printOptions?.templateLang) {
        const renderedTemplate = await this.createTemplate(
          tenantCode,
          voucher._id,
          createVoucherDto.printOptions.templateLang,
        );

        await this.sendTemplate(
          renderedTemplate,
          createVoucherDto.printOptions,
          createVoucherDto.type,
        );

        return { voucher: voucher, template: renderedTemplate };
      } else {
        return voucher.populate(['journal']);
      }
    } catch (error) {
      await this.transactionsService.handleFailure(
        request.user?.code,
        correlationId,
        error,
      );

      throw error;
    }
  }

  private async createInternalMemo(
    code: number,
    autoNumberingPolicy: string[],
    discountAmount: number,
    type: voucherType,
    creditAccount: AccountingNodeDocument,
    debitAccount: AccountingNodeDocument,
    number: number,
    branch: Types.ObjectId,
    invoice_party_type: any,
    transaction_date: any,
    user: any,
    debitPartnerId: any,
    creditPartnerId: any,
  ): Promise<VoucherDocument> {
    // create voucher
    // create journal

    const numberOfMemo = await this.autoNumberingService.getNextSequence(
      this.voucherModel,
      autoNumberingPolicy,
      branch,
      type,
    );

    const memoDto = {
      code: code,
      number: numberOfMemo,
      type,
      debit_account: {
        accounting_node: debitAccount._id,
        amount: +Number(discountAmount).toFixed(2),
        partner_id: debitPartnerId,
        partner_type: debitPartnerId ? invoice_party_type : null,
      },
      credit_account: {
        accounting_node: creditAccount._id,
        amount: +Number(discountAmount).toFixed(2),
        partner_id: creditPartnerId,
        partner_type: creditPartnerId ? invoice_party_type : null,
      },
      amount: discountAmount,
      branch,
      description: `Discount for Voucher #${number}`,
      auto_generated: true,
      transaction_date: transaction_date,
    };
    const result = await this.transactionsService.creditMemoVoucher(
      memoDto as any,
    );

    const journalData = await this.transactionsService.voucherJournalAccounting(
      {
        by_user: new Types.ObjectId(user),
        type: journalTypes.memo,
        transactions: [
          {
            code: creditAccount.code,
            credit: +Number(discountAmount).toFixed(2),
            debit: 0,
            accountingNode: creditAccount._id,
            by_user: new Types.ObjectId(user),
            description: 'memo',
            current_balance: 0,
            partner_id: creditPartnerId,
            partner_type: creditPartnerId ? invoice_party_type : undefined,
          },
          {
            code: debitAccount.code,
            credit: 0,
            debit: +Number(discountAmount).toFixed(2),
            accountingNode: debitAccount._id,
            by_user: new Types.ObjectId(user),
            description: 'memo',
            current_balance: 0,
            partner_id: debitPartnerId,
            partner_type: debitPartnerId ? invoice_party_type : undefined,
          },
        ],
        transaction_date: transaction_date,
        document: result._id,
        document_code: result.number,
        branch,
        note: `Discount for Voucher #${number} `,
      } as any,
      code,
    );

    const rs = await this.voucherModel.findOneAndUpdate(
      { _id: result._id },
      { journal: new Types.ObjectId(journalData._id) },
      { new: true },
    );
    return rs;
  }

  async findAll(pagination: FindAllVoucherDto) {
    //from request
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { page, limit, queries, from_date, to_date, ...rest } = pagination;

    let query: FindAllVoucherDto = rest || {};
    if (query.journal) {
      query.journal = new Types.ObjectId(query.journal);
    }
    if (query.branch) {
      query.branch = new Types.ObjectId(query.branch);
    }
    if (from_date) {
      query.transaction_date = { $gte: from_date };
    }
    if (to_date) {
      query.transaction_date.$lte = to_date;
    }
    if (queries) {
      const searchText = {
        $regex: new RegExp(`(^|\\s)${queries}`),
        $options: 'i',
      };

      query = {
        ...query,
        $or: [
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$number' },
                regex: new RegExp(`^${queries}`),
                options: 'i',
              },
            },
          },
          {
            type: searchText,
          },
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$amount' },
                regex: new RegExp(`^${queries}`),
                options: 'i',
              },
            },
          },
        ],
      } as any;
    }

    const data = await this.voucherModel.aggregate([
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'generalledgers',
          localField: 'journal',
          foreignField: '_id',
          as: 'journal',
        },
      },
      {
        $addFields: { journal: { $arrayElemAt: ['$journal', 0] } },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'journal.transactions.accountingNode',
          foreignField: '_id',
          as: 'acnodez',
        },
      },
      {
        $addFields: {
          'journal.transactions': {
            $map: {
              input: '$journal.transactions',
              as: 'transactions',
              in: {
                $mergeObjects: [
                  '$$transactions',
                  {
                    accountingNode: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: '$acnodez',
                            cond: {
                              $eq: [
                                '$$this._id',
                                '$$transactions.accountingNode',
                              ],
                            },
                          },
                        },
                        0,
                      ],
                    },
                  },
                ],
              },
            },
          },
          acnodez: '$$REMOVE',
        },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'from_account',
          foreignField: '_id',
          as: 'from_account',
        },
      },
      {
        $addFields: { from_account: { $arrayElemAt: ['$from_account', 0] } },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'to_account',
          foreignField: '_id',
          as: 'to_account',
        },
      },
      {
        $addFields: { to_account: { $arrayElemAt: ['$to_account', 0] } },
      },
      {
        $lookup: {
          from: 'vouchers',
          localField: 'memo',
          foreignField: '_id',
          as: 'memo',
        },
      },
      {
        $addFields: { memo: { $arrayElemAt: ['$memo', 0] } },
      },
      {
        $skip: (page - 1) * limit,
      },
      {
        $limit: limit,
      },
    ]);
    const meta = await paginate(limit, page, this.voucherModel, query);

    for (const voucher of data) {
      for (const transaction of voucher.journal.transactions) {
        if (
          transaction.partner_id &&
          (transaction.partner_type === 'customer' ||
            transaction.partner_type === 'vendor')
        ) {
          const partner = await this.tradeRpcService.getPartnersAccounts(
            this.request.tenantId,
            {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              partnerType: transaction.partner_type,
              id: transaction.partner_id,
            },
          );

          transaction.accountingNode.name = partner[0]?.name;
          transaction.accountingNode.code = partner[0]?.number;
          transaction.code = partner[0]?.number;
        }
      }
    }

    return { result: data, meta };
  }

  async findOne(query): Promise<Voucher> {
    const data = await this.voucherModel.aggregate([
      {
        $match: {
          _id: new Types.ObjectId(query._id),
        },
      },
      {
        $lookup: {
          from: 'generalledgers',
          localField: 'journal',
          foreignField: '_id',
          as: 'journal',
        },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'journal.transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNode',
        },
      },
      {
        $lookup: {
          from: 'vouchers',
          localField: 'memo',
          foreignField: '_id',
          as: 'memo',
        },
      },
    ]);

    for (let index = 0; index < data.length; index++) {
      let partner;
      data[index].journal = data[index].journal[index];
      for (const transaction of data[index].journal.transactions) {
        const matchingNode = data[index].accountingNode.find(
          (accountingNode) =>
            accountingNode._id.toString() ===
            transaction.accountingNode.toString(),
        );
        if (
          transaction.partner_id &&
          (transaction.partner_type === 'customer' ||
            transaction.partner_type === 'vendor')
        ) {
          partner = await this.tradeRpcService.getPartner(
            this.request.tenantId,
            {
              _id: transaction.partner_id,
              type: transaction.partner_type,
              branch: data[index].branch,
            },
          );

          matchingNode.name = partner?.name;
          matchingNode.code = partner?.number;
          transaction.code = partner?.number;
        }

        if (matchingNode) {
          transaction.accountingNode = matchingNode;
        }
      }

      if (data[index]?.sales_representative) {
        data[index].sales_representative =
          await this.tradeRpcService.getSalesRepresentative(
            this.request.tenantId,
            { id: data[index].sales_representative },
          );
      }

      if (data[index].type === voucherType.payment) {
        data[index].tax_percentage =
          (await salesTax.getSalesTax('SA'))?.rate * 100;
        const tax = data[index].journal.transactions.find(
          (transaction) => transaction.isTax === true,
        )?.debit;
        data[index].tax_amount = tax;
        data[index].subtotal = data[index].amount - tax;
        data[index].total_amount_after_tax = data[index].amount;
      }

      if (data[index].invoice.length > 0) {
        const ids = data[index].invoice.map((invoice) => invoice.Invoice);
        if (data[index].type === voucherType.payment) {
          const purchases = await this.tradeRpcService.getPurchases(
            this.request.tenantId,
            { ids },
          );
          data[index].invoice.forEach((invoice) => {
            invoice.invoiceObject = purchases.find(
              (purchase) => String(invoice.Invoice) === String(purchase._id),
            );
          });
        } else if (data[index].type === voucherType.receipt) {
          const sales = await this.tradeRpcService.getSales(
            this.request.tenantId,
            { ids },
          );
          data[index].invoice.forEach((invoice) => {
            invoice.invoiceObject = sales.find(
              (sale) => String(invoice.Invoice) === String(sale._id),
            );
          });
        }
      }

      if (
        data[index].type === voucherType.payment ||
        data[index].type === voucherType.receipt
      ) {
        if (
          data[index]?.invoice_party_type === voucherPartyType.customer ||
          data[index]?.invoice_party_type === voucherPartyType.vendor
        ) {
          if (partner) {
            data[index].partner = partner;
          }
        }
      }

      if (data[index].payment_type) {
        const paymentTypeData = await this.userRpcService.getpaymentType(
          this.request.tenantId,
          { _id: data[index].payment_type },
        );
        if (paymentTypeData) {
          data[index].payment_type = paymentTypeData;
        }
      }
    }
    return data[0] || {};
  }

  async findOneNumbering(
    number,
    branch: string | Types.ObjectId,
    type: voucherType,
  ): Promise<VoucherDocument> {
    return await this.voucherModel.findOne({ number, branch, type });
  }

  async rollback(_id: Types.ObjectId): Promise<VoucherDocument> {
    return await this.voucherModel.findOneAndDelete({ _id });
  }

  async findAllRaw(pagination: FindAllVoucherDto): Promise<VoucherDocument[]> {
    const {
      queries,
      fromNumber,
      toNumber,
      //request
      // eslint-disable-next-line @typescript-eslint/naming-convention
      credit_account,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      debit_account,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      from_date,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      to_date,
      ...rest
    } = pagination;

    let query: FindAllVoucherDto = rest || {};
    if (rest.type === voucherType.payment && credit_account) {
      query['credit_account.accounting_node'] = credit_account;
    }
    if (rest.type === voucherType.payment && debit_account) {
      query['credit_account.partner_id'] = new Types.ObjectId(debit_account);
    }
    if (rest.type === voucherType.receipt && credit_account) {
      query['credit_account.partner_id'] = new Types.ObjectId(credit_account);
    }
    if (rest.type === voucherType.receipt && debit_account) {
      query['debit_account.accounting_node'] = debit_account;
    }
    if (query.journal) {
      query.journal = new Types.ObjectId(query.journal);
    }
    if (query.branch) {
      query.branch = new Types.ObjectId(query.branch);
    }
    if (fromNumber) {
      query.number = { $gte: [{ $toInt: '$$this' }, fromNumber] } as any;
    }
    if (toNumber) {
      query.number = { $lte: [{ $toInt: '$$this' }, toNumber] } as any;
    }
    if (from_date) {
      query.createdAt = { $gte: new Date(from_date) };
    }
    if (to_date) {
      query.createdAt.$lte = new Date(to_date);
    }

    if (queries) {
      const searchText = new RegExp(queries);
      query = {
        ...query,
        $or: [
          {
            number: searchText,
          },
          {
            type: searchText,
          },
          {
            $expr: {
              $regexMatch: {
                input: { $toString: '$amount' },
                regex: searchText,
                options: 'i',
              },
            },
          },
        ],
      } as any;
    }
    const data = await this.voucherModel.aggregate([
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'generalledgers',
          localField: 'journal',
          foreignField: '_id',
          as: 'journal',
        },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'journal.transactions.accountingNode',
          foreignField: '_id',
          as: 'accountingNode',
        },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'from_account',
          foreignField: '_id',
          as: 'from_account',
        },
      },
      {
        $lookup: {
          from: 'accountingnodes',
          localField: 'to_account',
          foreignField: '_id',
          as: 'to_account',
        },
      },
    ]);
    data.forEach((doc, index) => {
      data[index].to_account = data[index].to_account[0] || undefined;
      data[index].from_account = data[index].from_account[0] || undefined;
      data[index].journal = data[index].journal[0];
      data[index].accountingNode.forEach((doc2, index2) => {
        data[index].journal.transactions[index2].accountingNode = doc2;
        delete data[index].accountingNode[index2];
      });
    });
    return data;
  }

  async createTemplate(code: number, _id, lang) {
    const data = (await this.findOne({ _id })) as any;

    if (isEmpty(data)) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.voucherNotFound),
        erpExceptionCode.voucherNotFound,
      );
    }

    if (
      data.type === voucherType.receipt ||
      data.type === voucherType.payment
    ) {
      data.payment_type = data?.payment_type?.name[lang];
      data.partnerName = data?.partner?.name[lang];
      data.sales_representative = data?.sales_representative?.name;
    }

    let templateType;

    switch (data.type) {
      case voucherType.receipt:
        templateType =
          data.invoice_party_type === voucherPartyType.general
            ? templateEnum.general_receipt_voucher
            : templateEnum.partner_receipt_voucher;
        break;
      case voucherType.payment:
        templateType =
          data.invoice_party_type === voucherPartyType.general
            ? templateEnum.general_payment_voucher
            : templateEnum.partner_payment_voucher;
        break;
      case voucherType.credit_memo:
      case voucherType.debit_memo:
        templateType = templateEnum.debit_credit_memo;
        break;
      default:
        templateType = null;
    }

    if (!templateType) {
      return '';
    }
    const company = await this.userRpcService.getCompany(code);
    const branch = await this.userRpcService.getBranches(code, [data.branch]);
    const branchSetting = branch.find((branch) => {
      return String(branch._id) === String(data.branch);
    });
    data.logo =
      company.logo !== '' ? company.logo : 'https://svgur.com/i/wKH.svg';
    data.company_name = company.name[lang];
    data.company_info = {
      logo: data.logo,
      name: company.name,
      phone: company.phone,
      email: company.email,
      address: company.address,
      contact_person: company.contact_person,
      branch: {
        general_information: {
          tax_code: branchSetting?.general_information?.tax_code,
          name: branchSetting.general_information.name,
        },
      },
    };
    data.Date = data.transaction_date;
    if (branchSetting) {
      data.tax_id = branchSetting?.general_information?.tax_code;
      data.branch_name = branchSetting?.general_information?.name?.[lang];
    }

    if (templateType === templateEnum.debit_credit_memo) {
      let account,
        transactions = [];
      const totalDebit = data.journal.transactions.reduce(
        (acc, transaction) => acc + transaction.debit,
        0,
      );
      const totalCredit = data.journal.transactions.reduce(
        (acc, transaction) => acc + transaction.credit,
        0,
      );
      data.totalDebit = totalDebit;
      data.totalCredit = totalCredit;
      if (data.type === voucherType.credit_memo) {
        account = data.debit_account[0].accounting_node;
        transactions = data.journal.transactions.filter(
          (transaction) => transaction.credit > 0,
        );
      } else if (data.type === voucherType.debit_memo) {
        account = data.credit_account[0].accounting_node;
        transactions = data.journal.transactions.filter(
          (transaction) => transaction.debit > 0,
        );
      }
      data.journal.transactions = transactions;
      const populatedAccount =
        await this.accountingNodeProxyPatternService.findOne(
          {
            _id: account,
          },
          branchSetting._id,
        );
      if (data.type === voucherType.credit_memo) {
        data.debit_account = populatedAccount;
      } else if (data.type === voucherType.debit_memo) {
        data.credit_account = populatedAccount;
      }
    }

    if (data.settlement_type === settlementType.settlement_of_invoice) {
      data.transactions = data.invoice;
    }
    return await this.tradeRpcService.getRendered(code, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      templateType,
      data,
      lang,
    });
  }

  async printOne(getTemplateDto: GetTemplateDto) {
    const { voucherId, ...sendOptions } = getTemplateDto;
    const template = await this.createTemplate(
      this.request.tenantId,
      voucherId,
      sendOptions.templateLang,
    );

    const voucher = await this.voucherModel
      .findOne({ _id: voucherId })
      .select('id type');

    await this.sendTemplate(template, sendOptions, voucher.type);

    return { template: sendOptions.print ? template : null };
  }

  async createMemoRpc(createMemoDto: CreateVoucherRpcDto) {
    createMemoDto.journal = new Types.ObjectId(createMemoDto.journal);
    createMemoDto.branch = new Types.ObjectId(createMemoDto.branch);
    return await (
      await new this.voucherModel(createMemoDto).save()
    ).populate(['journal']);
  }

  async updateMemo(
    _id: Types.ObjectId | string,
    updateMemoDto: UpdateMomoDto,
    request: RequestWithUser,
  ) {
    const changedLedger = [];
    if (
      updateMemoDto.transaction_date === undefined ||
      updateMemoDto.transaction_date === null
    ) {
      updateMemoDto.transaction_date = new Date();
    }

    const voucher = await this.voucherModel.findOne({ _id });

    if (!voucher) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.voucherNotFound),
        erpExceptionCode.voucherNotFound,
      );
    }

    const original = cloneDeep(voucher?.toObject());
    const editObject = request.policyData?.allow_documents_edition;
    handleGlobalEditRestriction(
      editObject.type,
      editObject.parameter || 0,
      voucher?.createdAt,
    );

    if (
      (voucher.type === voucherType.credit_memo ||
        voucher.type === voucherType.debit_memo) &&
      voucher.auto_generated
    ) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.memoIsNotEditable),
        erpExceptionCode.memoIsNotEditable,
      );
    }

    checkVoucherAmount(updateMemoDto);

    const correlationId = await this.transactionsService.executeERPDocumentSaga(
      request ? request.headers['branch'] : voucher.branch,
      documentType.voucher,
      action.edit,
      [steps.erp_document],
      [steps.update_accounting_journaling],
    );

    try {
      const creditAccounts = updateMemoDto.credit_account.map((account) => {
        return {
          accounting_node: account.accounting_node,
          partner_id: account.partner_id,
          partner_type: account.partner_type,
        };
      });
      const debitAccounts = updateMemoDto.debit_account.map((account) => {
        return {
          accounting_node: account.accounting_node,
          partner_id: account.partner_id,
          partner_type: account.partner_type,
        };
      });

      if (updateMemoDto.type === voucherType.credit_memo) {
        for (const account of updateMemoDto.credit_account) {
          if (account.partner_id) {
            const partner = await this.tradeRpcService.getPartner(
              request?.user?.code,
              {
                _id: account.partner_id,
                type: account.partner_type,
                branch: updateMemoDto.branch,
              },
            );

            if (!partner) {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.partnerNotFound),
                erpExceptionCode.partnerNotFound,
              );
            }
            account.partner_id = new Types.ObjectId(partner._id);
            account.accounting_node =
              partner.accounting_info.general_account._id;
            // creditPartnerId = partner._id;
            // creditPartnerType = partner.type;

            const targetAccount = creditAccounts.find(
              (account) => String(account.partner_id) === String(partner._id),
            );

            if (targetAccount) {
              targetAccount.accounting_node =
                partner.accounting_info.general_account._id;
            }
          }
        }
      }

      if (updateMemoDto.type === voucherType.debit_memo) {
        for (const account of updateMemoDto.debit_account) {
          if (account.partner_id) {
            const partner = await this.tradeRpcService.getPartner(
              request?.user?.code,
              {
                _id: account.partner_id,
                type: account.partner_type,
                branch: updateMemoDto.branch,
              },
            );

            if (!partner) {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.partnerNotFound),
                erpExceptionCode.partnerNotFound,
              );
            }
            account.partner_id = new Types.ObjectId(partner._id);
            account.accounting_node =
              partner.accounting_info.general_account._id;
            // creditPartnerId = partner._id;
            // creditPartnerType = partner.type;

            const targetAccount = debitAccounts.find(
              (account) => String(account.partner_id) === String(partner._id),
            );

            if (targetAccount) {
              targetAccount.accounting_node =
                partner.accounting_info.general_account._id;
            }
          }
        }
      }

      const accountingData = await findAccountingNodes(
        debitAccounts,
        creditAccounts,
        new Types.ObjectId(request.headers['branch']),
        this.accountingNodeProxyPatternService,
      );

      updateMemoDto.by_user = request.user.user_id;
      updateMemoDto.branch = new Types.ObjectId(updateMemoDto.branch);

      const rs = await this.voucherModel.findOneAndUpdate(
        { _id },
        updateMemoDto,
        {
          new: true,
        },
      );

      await this.transactionsService.createdERPDocument(correlationId, {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentId: _id,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentPattern:
          accountingMessagePattern.rollback_payment_voucher_action,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocument: original,
      });

      const transactions = [];
      accountingData.forEach((element: AccountingNodeDocument) => {
        if (updateMemoDto.type === voucherType.credit_memo) {
          const accounts = updateMemoDto.credit_account.filter((account) => {
            return String(account.accounting_node) === String(element._id);
          });

          if (accounts.length > 0) {
            for (const account of accounts) {
              transactions.push({
                code: element.code,
                credit: +Number(account.amount).toFixed(2),
                debit: 0,
                accountingNode: new Types.ObjectId(element._id),
                by_user: new Types.ObjectId(updateMemoDto.by_user),
                description: updateMemoDto.note || 'voucher',
                current_balance: 0,
                partner_id: account.partner_id,
                partner_type: account.partner_type,
              } as ITransaction);
            }
          }

          const account = updateMemoDto.debit_account.find((account) => {
            return String(account.accounting_node) === String(element._id);
          });
          if (account) {
            transactions.push({
              code: element.code,
              credit: 0,
              debit: +Number(account.amount).toFixed(2),
              accountingNode: new Types.ObjectId(element._id),
              by_user: new Types.ObjectId(updateMemoDto.by_user),
              description: updateMemoDto.note || 'voucher',
              current_balance: 0,
              partner_id: undefined,
              partner_type: undefined,
            } as ITransaction);
          }
        }

        if (updateMemoDto.type === voucherType.debit_memo) {
          const accounts = updateMemoDto.debit_account.filter((account) => {
            return String(account.accounting_node) === String(element._id);
          });

          if (accounts.length > 0) {
            for (const account of accounts) {
              transactions.push({
                code: element.code,
                credit: 0,
                debit: +Number(account.amount).toFixed(2),
                accountingNode: new Types.ObjectId(element._id),
                by_user: new Types.ObjectId(updateMemoDto.by_user),
                description: updateMemoDto.note || 'memo voucher',
                current_balance: 0,
                partner_id: account.partner_id,
                partner_type: account.partner_type,
              } as ITransaction);
            }
          }

          const account = updateMemoDto.credit_account.find((account) => {
            return String(account.accounting_node) === String(element._id);
          });
          if (account) {
            transactions.push({
              code: element.code,
              credit: +Number(account.amount).toFixed(2),
              debit: 0,
              accountingNode: new Types.ObjectId(element._id),
              by_user: new Types.ObjectId(updateMemoDto.by_user),
              description: updateMemoDto.note || 'voucher',
              current_balance: 0,
              partner_id: undefined,
              partner_type: undefined,
            } as ITransaction);
          }
        }
      });

      if (request.policyData.document_policy === documentPolicyEnum.override) {
        changedLedger.push(voucher.journal);
      }

      await this.transactionsService.updateAccountingJournalAccounting(
        {
          _id: voucher._id,
          type: journalTypes.memo,
          updated_transactions: transactions,
          code: request.user.code,
        },
        request,
        changedLedger,
      );

      await this.transactionsService.transactionCompleted(correlationId);

      return rs;
    } catch (error) {
      await this.transactionsService.handleFailure(
        request.user?.code,
        correlationId,
        error,
      );

      throw error;
    }
  }

  async update(
    _id: string,
    updateVoucherDto: UpdateVoucherDto,
    req: RequestWithUser,
  ) {
    const allowDocumentsEdition = req.policyData.allow_documents_edition;
    const voucher = await this.voucherModel.findOne({ _id });

    const changedLedger = [];
    let newMemoData;
    let originalMemo;
    let originalMemoAcLedger;
    let voucherTypeRollback;
    let voucherJournaType;
    const originalVoucher = cloneDeep(voucher);
    const editObject = allowDocumentsEdition;
    handleGlobalEditRestriction(
      editObject.type,
      editObject.parameter || 0,
      voucher.createdAt,
    ); //sd
    const rollbackSteps: IRollbackSteps[] = [];
    const correlationId = await this.transactionsService.executeERPDocumentSaga(
      req ? req.headers['branch'] : voucher.branch,
      documentType.voucher,
      action.edit,
      [steps.erp_document],
      [
        steps.update_accounting_journaling,
        steps.sales_invoice,
        steps.purchase_invoice,
        steps.credit_debit_memo_voucher,
      ],
    );

    if (
      voucher.type !== voucherType.payment &&
      voucher.type !== voucherType.receipt
    ) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.voucherIsNotEditable),
        erpExceptionCode.voucherIsNotEditable,
      );
    }

    if (voucher.type === voucherType.payment) {
      voucherTypeRollback =
        accountingMessagePattern.rollback_payment_voucher_action;
      voucherJournaType = journalTypes.payment_voucher;
    } else {
      voucherTypeRollback =
        accountingMessagePattern.rollback_receipt_voucher_action;
      voucherJournaType = journalTypes.receipt_voucher;
    }

    try {
      const policies = req.policyData;
      const branchId = new Types.ObjectId(req.headers['branch']);
      const tenantCode = req?.user?.code;

      const debitMemoDocument = policies.debit_memo;
      const creditMemoDocument = policies.credit_memo;

      const validated = await handleVoucherCreation(
        updateVoucherDto,
        tenantCode,
        branchId,
        policies,
        this.accountingNodeProxyPatternService,
        this.tradeRpcService,
        this.userRpcService,
      );

      const {
        transactions,
        debitPartnerId,
        creditPartnerId,
        discountAccount,
        partnerAccountData,
      } = validated;

      extend(voucher, updateVoucherDto);

      voucher.journal = originalVoucher.journal;

      await voucher.save();

      await this.transactionsService.createdERPDocument(correlationId, {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentId: voucher._id,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentPattern: voucherTypeRollback,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocument: originalVoucher,
      });

      const comparedInvoices = compareInvoices(
        voucher.invoice,
        updateVoucherDto.invoice,
      );

      if (
        voucher.settlement_type !== updateVoucherDto.settlement_type ||
        comparedInvoices
      ) {
        //change from partner to general account, we need to reduce the paid amount for all invoices in the voucher
        //change from settlement of invoice  to general, we need to reduce the paid amount for all invoices in the voucher
        if (
          voucher.settlement_type !== updateVoucherDto.settlement_type &&
          updateVoucherDto.settlement_type !==
            settlementType.settlement_of_invoice
        ) {
          await this.updateInvoice(
            req?.user?.code,
            voucher.invoice,
            voucher.type,
            voucher.amount,
            true,
            true,
          );
        }

        //change from general account to partner with settlement of invoice, update the paid amount
        //change from general settlement to settlement of invoice, update the paid amount
        if (
          voucher.settlement_type !== updateVoucherDto.settlement_type &&
          updateVoucherDto.settlement_type ===
            settlementType.settlement_of_invoice
        ) {
          await this.updateInvoice(
            req?.user?.code,
            updateVoucherDto.invoice,
            updateVoucherDto.type,
            updateVoucherDto.amount,
            false,
            true,
          );
        }

        if (
          voucher.settlement_type === updateVoucherDto.settlement_type &&
          comparedInvoices &&
          voucher.invoice.length > 0
        ) {
          const addArray = [];
          const subArray = [];

          for (const invoiceObject of voucher.invoice) {
            const updatedInvoice = updateVoucherDto.invoice.find(
              (updatedInvoice) =>
                String(updatedInvoice.Invoice) ===
                String(invoiceObject.Invoice),
            );

            if (updatedInvoice) {
              //if the updated invoice is more that the old invoice, we need to add
              if (updatedInvoice.amount > invoiceObject.amount) {
                const difference = updatedInvoice.amount - invoiceObject.amount;
                addArray.push({
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  Invoice: updatedInvoice.Invoice,
                  amount: difference,
                });
              }

              //if the updated invoice is lees than the old invoice, we need to reduce
              if (updatedInvoice.amount < invoiceObject.amount) {
                const difference = invoiceObject.amount - updatedInvoice.amount;
                subArray.push({
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  Invoice: updatedInvoice.Invoice,
                  amount: difference,
                });
              }
            } else {
              //if some invoices were removed in the new update, we need to reduce the old invoices
              subArray.push({
                // eslint-disable-next-line @typescript-eslint/naming-convention
                Invoice: invoiceObject.Invoice,
                amount: invoiceObject.amount,
              });
            }
          }

          //if some invoices were add in the new update, we need to add the new invoices
          const invoicesAdded = getInvoiceDifference(
            updateVoucherDto.invoice,
            voucher.invoice,
          );
          if (invoicesAdded.length > 0) {
            addArray.push(...invoicesAdded);
          }

          if (addArray.length > 0) {
            await this.updateInvoice(
              req?.user?.code,
              addArray,
              updateVoucherDto.type,
              updateVoucherDto.amount,
              false,
              true,
            );
          }

          if (subArray.length > 0) {
            await this.updateInvoice(
              req?.user?.code,
              subArray,
              updateVoucherDto.type,
              updateVoucherDto.amount,
              true,
              true,
            );
          }
        }
      }

      const comparedDebitAccount = compareAccounts(
        voucher.debit_account,
        updateVoucherDto.debit_account,
      );

      const comparedCreditAccount = compareAccounts(
        voucher.credit_account,
        updateVoucherDto.credit_account,
      );

      //if amount is different and the debit and credit are different from the original
      if (
        voucher.amount !== updateVoucherDto.amount ||
        comparedDebitAccount ||
        comparedCreditAccount
      ) {
        if (req.policyData.document_policy === documentPolicyEnum.override) {
          changedLedger.push(voucher.journal);
        }

        await this.transactionsService.updateAccountingJournalAccounting(
          {
            _id: voucher._id,
            type: voucherJournaType,
            updated_transactions: transactions,
            code: req.user.code,
          },
          req,
          changedLedger,
        );
      }

      //if discount value not equal to voucher discount or the updated voucher has new discount, or any account from debit or credit accounts has been changed
      if (
        voucher.discount !== updateVoucherDto.discount ||
        comparedCreditAccount ||
        comparedDebitAccount
      ) {
        const memo = await this.voucherModel.findOne({
          _id: voucher.memo,
          auto_generated: true,
        });

        //if the voucher changed from general account to customer or vendor with discount
        if (!memo) {
          if (updateVoucherDto.discount <= updateVoucherDto.amount) {
            if (voucher.type === voucherType.payment) {
              newMemoData = await this.createInternalMemo(
                tenantCode,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                debitMemoDocument.same_serial
                  ? ['debit_memo.cash_serial', 'debit_memo.credit_serial']
                  : ['debit_memo.cash_serial'],
                updateVoucherDto.discount,
                voucherType.debit_memo,
                discountAccount,
                partnerAccountData,
                voucher.number,
                new Types.ObjectId(voucher.branch),
                updateVoucherDto.invoice_party_type,
                voucher.transaction_date,
                req?.user?.user_id,
                debitPartnerId,
                null,
              );
              voucher.memo = newMemoData._id;
            }

            if (voucher.type === voucherType.receipt) {
              newMemoData = await this.createInternalMemo(
                tenantCode,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                creditMemoDocument.same_serial
                  ? ['credit_memo.cash_serial', 'credit_memo.credit_serial']
                  : ['credit_memo.cash_serial'],
                updateVoucherDto.discount,
                voucherType.credit_memo,
                partnerAccountData,
                discountAccount,
                voucher.number,
                new Types.ObjectId(voucher.branch),
                updateVoucherDto.invoice_party_type,
                voucher.transaction_date,
                req?.user?.user_id,
                null,
                creditPartnerId,
              );
              voucher.memo = newMemoData._id;
            }
          }
        } else {
          //if the voucher changed from customer or vendor to general account and has discount
          if (
            updateVoucherDto.discount === undefined ||
            updateVoucherDto.discount === 0
          ) {
            await this.remove(memo._id, req, req?.headers['branch']);
            voucher.discount = 0;
            voucher.memo = undefined;
          } else {
            //if the voucher changed from customer or vendor to customer or vendor, or change the discount amount
            // eslint-disable-next-line @typescript-eslint/no-unused-vars

            originalMemo = cloneDeep(memo.toObject());
            await memo.updateOne({
              $set: {
                debit_account: {
                  ...updateVoucherDto.debit_account,
                  amount: updateVoucherDto.discount,
                },
                credit_account: {
                  ...updateVoucherDto.credit_account,
                  amount: updateVoucherDto.discount,
                },
                amount: updateVoucherDto.discount,
              },
            });
            //update general ledger
            let transactions;
            if (voucher.type === voucherType.receipt) {
              transactions = [
                {
                  code: partnerAccountData.code,
                  credit: +Number(updateVoucherDto.discount).toFixed(2),
                  debit: 0,
                  accountingNode: partnerAccountData._id,
                  by_user: new Types.ObjectId(req?.user?.user_id),
                  description: 'memo',
                  current_balance: 0,
                  partner_id: updateVoucherDto.debit_account[0].partner_id,
                  partner_type: updateVoucherDto.debit_account[0].partner_type,
                },
                {
                  code: discountAccount.code,
                  credit: 0,
                  debit: +Number(updateVoucherDto.discount).toFixed(2),
                  accountingNode: discountAccount._id,
                  by_user: new Types.ObjectId(req?.user?.user_id),
                  description: 'memo',
                  current_balance: 0,
                  partner_id: undefined,
                  partner_type: undefined,
                },
              ];

              if (
                req.policyData.document_policy === documentPolicyEnum.override
              ) {
                changedLedger.push(memo.journal);
              }

              await this.transactionsService.updateAccountingJournalAccounting(
                {
                  _id: memo._id,
                  type: journalTypes.memo,
                  updated_transactions: transactions,
                  code: req.user.code,
                },
                req,
                changedLedger,
              );
            }
            if (voucher.type === voucherType.payment) {
              transactions = [
                {
                  code: partnerAccountData.code,
                  credit: 0,
                  debit: +Number(updateVoucherDto.discount).toFixed(2),
                  accountingNode: partnerAccountData._id,
                  by_user: new Types.ObjectId(req?.user?.user_id),
                  description: 'memo',
                  current_balance: 0,
                  partner_id: updateVoucherDto.debit_account[0].partner_id,
                  partner_type: updateVoucherDto.debit_account[0].partner_type,
                },
                {
                  code: discountAccount.code,
                  credit: +Number(updateVoucherDto.discount).toFixed(2),
                  debit: 0,
                  accountingNode: discountAccount._id,
                  by_user: new Types.ObjectId(req?.user?.user_id),
                  description: 'memo',
                  current_balance: 0,
                  partner_id: undefined,
                  partner_type: undefined,
                },
              ];
            }

            if (
              req.policyData.document_policy === documentPolicyEnum.override
            ) {
              changedLedger.push(memo.journal);
            }

            await this.transactionsService.updateAccountingJournalAccounting(
              {
                _id: memo._id,
                type: journalTypes.memo,
                updated_transactions: transactions,
                code: req.user.code,
              },
              req,
              changedLedger,
            );
          }
        }
      }

      if (
        updateVoucherDto.invoice === undefined ||
        updateVoucherDto.invoice.length < 1
      ) {
        voucher.invoice = [];
      }
      updateVoucherDto.journal = voucher.journal;

      await voucher.save();

      await this.transactionsService.transactionCompleted(correlationId);

      return { new: updateVoucherDto, old: voucher };
    } catch (err) {
      rollbackSteps.push({
        name: steps.accounting_journaling,
        condition: originalMemo?.journal,
        data: {
          branch: new Types.ObjectId(req.headers['branch']),
          // eslint-disable-next-line @typescript-eslint/naming-convention
          accountingId: originalMemo?.journal,
          accounting_doc: originalMemoAcLedger,
        },
      });
      rollbackSteps.push({
        name: steps.credit_debit_memo_voucher,
        condition: originalMemo?._id,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        data: { creditMemoId: originalMemo?._id, memo: originalMemo },
      });

      await this.transactionsService.handleFailure(
        req.user.code,
        correlationId,
        err,
      );
      throw err;
    }
  }

  async removeMemo(
    _id: Types.ObjectId | string,
    request: RequestWithUser,
    branch: Types.ObjectId,
  ): Promise<Voucher> {
    const voucher = await this.voucherModel.findOne({ _id });
    if (
      (voucher.type === voucherType.credit_memo ||
        voucher.type === voucherType.debit_memo) &&
      voucher.auto_generated
    ) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.memoIsNotDeletable),
        erpExceptionCode.memoIsNotDeletable,
      );
    }
    return this.remove(voucher._id, request, branch);
  }

  async remove(
    _id: Types.ObjectId | string,
    request: RequestWithUser,
    branch: Types.ObjectId,
    soft: boolean = true,
  ): Promise<any> {
    let voucherTypeRollback = null,
      voucherJournalType = null;
    const changedLedger = [];

    const voucher = await this.voucherModel.findOne({ _id });

    const correlationId = await this.transactionsService.executeERPDocumentSaga(
      voucher.branch,
      documentType.voucher,
      action.delete,
      [steps.erp_document, steps.update_accounting_journaling],
    );

    try {
      switch (voucher.type) {
        case voucherType.receipt:
          voucherTypeRollback =
            accountingMessagePattern.rollback_receipt_voucher_action;
          voucherJournalType = journalTypes.receipt_voucher;
          break;
        case voucherType.payment:
          voucherTypeRollback =
            accountingMessagePattern.rollback_payment_voucher_action;
          voucherJournalType = journalTypes.payment_voucher;
          break;
        case voucherType.debit_memo:
        case voucherType.credit_memo:
          voucherTypeRollback = accountingMessagePattern.rollback_memo_voucher;
          voucherJournalType = journalTypes.memo;
          break;
      }

      if (soft === true) {
        voucher.softDelete();
      } else {
        await this.voucherModel.findOneAndDelete({ _id: voucher._id });
      }

      await this.transactionsService.createdERPDocument(correlationId, {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentId: voucher._id,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ERPDocumentPattern: voucherTypeRollback,
      });

      if (request.policyData.document_policy === documentPolicyEnum.override) {
        changedLedger.push(voucher.journal);
      }

      await this.transactionsService.updateAccountingJournalAccounting(
        {
          _id: voucher._id,
          type: voucherJournalType,
          code: request.user.code,
        },
        request,
        changedLedger,
      );

      await this.transactionsService.transactionCompleted(correlationId);

      return voucher;
    } catch (error) {
      await this.transactionsService.handleFailure(
        request.user?.code,
        correlationId,
        error,
      );

      throw error;
    }
  }

  async sendTemplate(
    renderedTemplate: string,
    sendOptions: PrintOptionsDto,
    type: string,
  ) {
    let emailName;

    switch (type) {
      case voucherType.payment:
        emailName = 'Payment Voucher';
        break;
      case voucherType.receipt:
        emailName = 'Receipt Voucher';
        break;
      case voucherType.credit_memo:
      case voucherType.debit_memo:
        emailName = 'Memo Voucher';
        break;
    }

    if (sendOptions.sendEmail) {
      this.notificationRpcService.sendReport({
        template: renderedTemplate,
        email: sendOptions.email,
        subject: emailName,
        phone: sendOptions.phoneNumber,
        whatsapp: false,
      });
    }
    if (sendOptions.sendSms) {
      // send template via sms
    }
  }

  async updateInvoice(
    code,
    invoice,
    type,
    amount,
    reduce: boolean = false,
    onUpdate: boolean = false,
  ) {
    const invoiceAmount = invoice.reduce((pr, cr: Invoice) => {
      return pr + cr.amount;
    }, 0);

    if (!onUpdate && amount.toFixed(2) !== Number(invoiceAmount).toFixed(2)) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.voucherNotBalanced),
        erpExceptionCode.voucherNotBalanced,
      );
    }
    if (type == voucherType.receipt) {
      await this.transactionsService.salesInvoice({
        code,
        invoice,
        reduce,
      });
    }
    if (type == voucherType.payment) {
      await this.transactionsService.purchaseInvoice({
        code,
        invoice,
        reduce,
      });
    }
  }

  async removeDoc(_id) {
    await this.voucherModel.findOneAndDelete({ _id });
  }

  async rollbackAction(id: Types.ObjectId, action_type: action, erp_document) {
    switch (action_type) {
      case action.create:
        return this.removeDoc(id);
      case action.delete:
        const voucherData = await this.voucherModel.findByIdWithDeleted(
          String(id),
        );
        if (voucherData) {
          return voucherData.restore();
        }
      case action.edit:
        await this.removeDoc(id);
        const voucherDoc = new this.voucherModel({
          ...erp_document,
          branch: new Types.ObjectId(erp_document.branch),
          journal: new Types.ObjectId(erp_document.journal),
        });
        return await voucherDoc.save();
      default:
    }
  }
}
