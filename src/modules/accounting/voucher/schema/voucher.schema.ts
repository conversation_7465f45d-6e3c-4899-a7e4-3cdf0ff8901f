import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { voucherType } from '../dto/voucher-type.enum';
import { voucherPartyType } from '../dto/voucher-party.enum';
import { AccountingNode } from '../../accounting-node/schema/accounting-node.schema';
import { settlementType } from '../dto/settlment-type.enum';
import { GeneralLedger } from '../../general-ledger/schema/general-ledger.schema';
import {
  SoftDeleteDocument,
  softDeletePlugin,
} from '../../../../utils/schemas';

export class Accounts {
  @Prop({ type: Types.ObjectId, ref: AccountingNode.name, required: true })
  accounting_node: AccountingNode;

  @Prop()
  amount: number;

  @Prop({ type: Types.ObjectId })
  partner_id?: string | Types.ObjectId;

  @Prop()
  partner_type?: string;
}

export class Invoice {
  @Prop({ type: Types.ObjectId })
  Invoice: string | Types.ObjectId;

  @Prop()
  amount: number;
}

export type VoucherDocument = HydratedDocument<Voucher> & SoftDeleteDocument;

@Schema({ timestamps: true })
export class Voucher {
  @Prop({ type: Number })
  number: number;

  @Prop({ type: String, enum: voucherType })
  type: voucherType;

  @Prop([{ type: Types.ObjectId, ref: AccountingNode.name, required: true }])
  debit_account: Accounts[];

  @Prop([{ type: Types.ObjectId, ref: AccountingNode.name, required: true }])
  credit_account: Accounts[];

  @Prop()
  amount: number;

  @Prop({ default: 0 })
  tax_amount: number;
  @Prop({ type: Types.ObjectId, ref: GeneralLedger.name })
  journal: Types.ObjectId;

  @Prop({ type: Types.ObjectId })
  branch: Types.ObjectId;

  @Prop()
  discount: number;

  @Prop()
  description: string;

  @Prop({ enum: voucherPartyType })
  invoice_party_type: voucherPartyType;

  @Prop({ type: Types.ObjectId, ref: Voucher.name, required: false })
  memo: Voucher;

  @Prop({ type: String })
  reference: string;

  @Prop({ type: String })
  note: string;

  @Prop({ type: Date, default: Date.now() })
  transaction_date: Date;

  @Prop([{ type: Invoice }])
  invoice: Invoice[];

  @Prop({ type: String, enum: settlementType })
  settlement_type: settlementType;

  @Prop({ type: String })
  payment_type: string;

  @Prop({ type: String })
  sales_representative: string;

  @Prop({ type: Boolean, default: false })
  is_include_tax: boolean;

  @Prop({ type: Boolean, default: false })
  auto_generated: boolean;
  createdAt;
  updatedAt;
}

export const voucherSchema = SchemaFactory.createForClass(Voucher);
voucherSchema.index({ number: 1, branch: 1, type: 1 }, { unique: true });
voucherSchema.plugin(softDeletePlugin);
