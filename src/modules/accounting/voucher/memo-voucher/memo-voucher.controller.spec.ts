import { MemoVoucherController } from './memo-voucher.controller';
import { VoucherService } from '../voucher.service';
import { Test, TestingModule } from '@nestjs/testing';
import { PolicyService } from '../../../policy/policy.service';
import { getModelToken } from '@nestjs/mongoose';
import { Policy } from '../../../policy/schema/policy.schema';
import { UserRpcService } from '../../../rpc/user-rpc.service';
import { TenantRpcService } from '../../../rpc/tenant-rpc.service';
import { userClientMock } from '../../../__mocks__/services/user-rpc.service';
import { Voucher } from '../schema/voucher.schema';
import {
  creditMemoVoucherStub,
  debitMemoVoucherStub,
  paymentVoucherStub,
  receiptVoucherStub,
} from '../../../__mocks__/stubs/voucher.stub';
import { requestWithUserStub } from '../../../__mocks__/stubs/request-with-user.stub';
import { CreateVoucherDto } from '../dto/create-voucher.dto';
import { CaslAbilityFactory } from '../../../casl/casl-ability.factory';

describe('MemoVoucherController', () => {
  let controller: MemoVoucherController;
  let service: VoucherService;

  beforeEach(async () => {
    const { VoucherService: mockVoucherService } = await import(
      '../../../__mocks__/services/voucher.service'
    );

    const module: TestingModule = await Test.createTestingModule({
      controllers: [MemoVoucherController],
      providers: [
        {
          provide: VoucherService,
          useFactory: () => mockVoucherService(),
        },
        { provide: UserRpcService, useValue: userClientMock() },
        { provide: CaslAbilityFactory, useValue: userClientMock() },
        { provide: PolicyService, useValue: userClientMock() },
        {
          provide: TenantRpcService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<MemoVoucherController>(MemoVoucherController);
    service = module.get<VoucherService>(VoucherService);
    jest.clearAllMocks();
  });

  describe('create', () => {
    describe('when create is called', () => {
      describe('when create is called with receipt', () => {
        let voucher: Voucher | any;

        beforeEach(async () => {
          voucher = await controller.create(
            receiptVoucherStub() as unknown as CreateVoucherDto,
            requestWithUserStub(),
          );
        });

        test('then it should call voucher service with receipt', async () => {
          expect(service.create).toHaveBeenCalledWith(
            expect.objectContaining(
              receiptVoucherStub() as unknown as CreateVoucherDto,
            ),
            null,
            expect.objectContaining(requestWithUserStub()),
          );
        });

        test('then it should return a payment voucher', async () => {
          expect(voucher).toEqual(receiptVoucherStub());
        });
      });

      describe('when create is called with payment', () => {
        let voucher: Voucher | any;

        beforeEach(async () => {
          voucher = await controller.create(
            paymentVoucherStub() as unknown as CreateVoucherDto,
            requestWithUserStub(),
          );
        });

        test('then it should call voucher service with payment', async () => {
          expect(service.create).toHaveBeenCalledWith(
            expect.objectContaining(
              paymentVoucherStub() as unknown as CreateVoucherDto,
            ),
            null,
            expect.objectContaining(requestWithUserStub()),
          );
        });

        test('then it should return a payment voucher', async () => {
          expect(voucher).toEqual(paymentVoucherStub());
        });
      });

      describe('when create is called with credit memo', () => {
        let voucher: Voucher | any;

        beforeEach(async () => {
          voucher = await controller.create(
            creditMemoVoucherStub() as unknown as CreateVoucherDto,
            requestWithUserStub(),
          );
        });

        test('then it should call voucher service with credit memo', async () => {
          expect(service.create).toHaveBeenCalledWith(
            expect.objectContaining(
              creditMemoVoucherStub() as unknown as CreateVoucherDto,
            ),
            null,
            expect.objectContaining(requestWithUserStub()),
          );
        });

        test('then it should return a credit memo voucher', async () => {
          expect(voucher).toEqual(creditMemoVoucherStub());
        });
      });

      describe('when create is called with debit memo', () => {
        let voucher: Voucher | any;

        beforeEach(async () => {
          voucher = await controller.create(
            debitMemoVoucherStub() as unknown as CreateVoucherDto,
            requestWithUserStub(),
          );
        });

        test('then it should call voucher service with debit memo', async () => {
          expect(service.create).toHaveBeenCalledWith(
            expect.objectContaining(
              debitMemoVoucherStub() as unknown as CreateVoucherDto,
            ),
            null,
            expect.objectContaining(requestWithUserStub()),
          );
        });

        test('then it should return a debit memo voucher', async () => {
          expect(voucher).toEqual(debitMemoVoucherStub());
        });
      });
    });
  });
});
