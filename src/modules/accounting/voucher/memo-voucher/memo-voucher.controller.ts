import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { EventPattern, MessagePattern, Payload } from '@nestjs/microservices';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { VoucherService } from '../voucher.service';
import { FeatureAccessibilityGuard } from '../../../casl/guards/feature-accessibility.guard';
import { CaslGuard } from '../../../casl/guards/casl.guard';
import { feature } from '../../../casl/guards/feature.decorator';
import { abilities } from '../../../casl/guards/abilities.decorator';
import { PolicyInterceptor } from '../../../policy/policy.interceptor';
import { serviceName } from '../../../policy/service-name.decorator';
import { RequestWithUser } from '../../../auth/interfaces/authorize.interface';
import { VoucherWithMeta } from '../exportTypes/voucherResponse.types';
import { FindAllVoucherDto } from '../dto/get-voucher.dto';
import {
  CreateVoucherDto,
  CreateVoucherRpcDto,
} from '../dto/create-voucher.dto';
import { GetTemplateDto } from '../dto/get-template.dto';
import { Voucher } from '../schema/voucher.schema';
import { UpdateMomoDto } from '../dto/update-memo.dto';
import { publicRpc } from '../../../auth/guards/public-event.decorator';
import { RollbackVoucherRpcDto } from '../dto/rollback-voucher-rpc.dto';
import { RouteRules } from '../../../../interceptor/routes-rule.interceptor';
import { accountingMessagePattern } from '../../../../utils/queues.enum';

@ApiTags('memo-voucher')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@Controller('vouchers/memo')
export class MemoVoucherController {
  constructor(private readonly voucherService: VoucherService) {}

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'memo_voucher' })
  @abilities({ action: 'create', subject: 'memo_voucher' })
  @Post()
  @UseInterceptors(PolicyInterceptor)
  @serviceName([
    'debit_memo',
    'credit_memo',
    'purchase_tax_account',
    'sales_discount_account',
  ])
  async create(
    @Body() createVoucherDto: CreateVoucherDto,
    @Req() request: RequestWithUser,
  ) {
    return this.voucherService.create(createVoucherDto, null, request);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'memo_voucher' })
  @abilities({ action: 'list', subject: 'memo_voucher' })
  @Get()
  findAll(@Query() query: FindAllVoucherDto): Promise<VoucherWithMeta> {
    return this.voucherService.findAll(query);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'memo_voucher' })
  @abilities({ action: 'read', subject: 'memo_voucher' })
  @Get(':id')
  findOne(@Param('id') _id: string): Promise<Voucher> {
    return this.voucherService.findOne({ _id });
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'memo_voucher' })
  @abilities({ action: 'read', subject: 'memo_voucher' })
  @Post('template')
  printOne(@Body() getTemplateDto: GetTemplateDto): Promise<any> {
    return this.voucherService.printOne(getTemplateDto);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'memo_voucher' })
  @abilities({ action: 'edit', subject: 'memo_voucher' })
  @Patch('memo/:id')
  @UseInterceptors(PolicyInterceptor, RouteRules)
  @serviceName([
    'allow_documents_edition',
    'debit_memo',
    'credit_memo',
    'document_policy',
    'document_policy_status',
  ])
  updateMemo(
    @Param('id') _id: string,
    @Body() updateMemoDto: UpdateMomoDto,
    @Req() request: RequestWithUser,
  ) {
    return this.voucherService.updateMemo(_id, updateMemoDto, request);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'memo_voucher' })
  @abilities({ action: 'delete', subject: 'memo_voucher' })
  @UseInterceptors(PolicyInterceptor, RouteRules)
  @serviceName([
    'allow_documents_deletion',
    'document_policy',
    'document_policy_status',
  ])
  @Delete('memo/:id')
  async removeMemo(
    @Param('id') _id: string,
    @Req() request: RequestWithUser,
  ): Promise<any> {
    return this.voucherService.removeMemo(
      _id,
      request,
      new Types.ObjectId(request?.headers['branch']),
    );
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.create_memo })
  async createMemoRpc(@Payload() payload: CreateVoucherRpcDto) {
    return await this.voucherService.createMemoRpc(payload);
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.rollback_memo_voucher })
  async rollbackVoucher(@Payload() payload: RollbackVoucherRpcDto) {
    return this.voucherService.rollback(payload._id);
  }

  @publicRpc()
  @EventPattern({ cmd: accountingMessagePattern.rollback_memo_voucher_action })
  async rollbackVoucherAction(@Payload() payload: any) {
    return this.voucherService.rollbackAction(
      payload._id,
      payload.action,
      payload.erp_document,
    );
  }
}
