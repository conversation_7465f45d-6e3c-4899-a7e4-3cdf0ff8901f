import { Controller, Get, HttpException, Query } from '@nestjs/common';
import { publicRoute } from '../../casl/guards/public-route.decorator';

import { GetDto } from './dto/get.dto';
import { nameOf } from '../../../utils/object-key-name';
import { generalExceptionCode } from '../../../exceptions/exception-code.general';
import { getObjectKey } from '../../../utils/get-object-key';
import { skipSchemaTransaction } from '../../postgres-database/schema-transaction.decorator';

@Controller('dummy-routes')
export class DummyRoutesController {
  constructor() {}

  @skipSchemaTransaction()
  @publicRoute()
  @Get('/give_me_err')
  throw500(@Query() query: GetDto) {
    console.log(query, 'qqqq');

    throw new HttpException(
      nameOf(
        generalExceptionCode,
        (x) =>
          x[
            getObjectKey(generalExceptionCode, query.code) ||
              'internalServerError'
          ],
      ),
      query.code || 500,
    );
  }
}
