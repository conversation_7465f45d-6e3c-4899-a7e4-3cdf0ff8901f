import { ApiProperty } from '@nestjs/swagger';
import { IsInt } from 'class-validator';
import { Type } from 'class-transformer';
import { generalExceptionCode } from 'src/exceptions/exception-code.general';

export class GetDto {
  @ApiProperty({
    description: 'Customer Accounting Node ID',
    example: generalExceptionCode.badRequest,
    type: () => generalExceptionCode,
    enum: generalExceptionCode,
  })
  @Type(() => Number)
  @IsInt()
  code: number;
}
