import { Injectable } from '@nestjs/common';
import { Worker } from 'worker_threads';
@Injectable()
export class WorkerService {
  async runWorker(filePath: string, data: any) {
    return new Promise((resolve, reject) => {
      const worker = new Worker(filePath, {
        workerData: data,
      });

      worker.on('message', (result) => {
        return resolve(result);
      });
      worker.on('error', (e) => reject(e));
      worker.on('exit', (code) => resolve(code));
    });
  }
}
