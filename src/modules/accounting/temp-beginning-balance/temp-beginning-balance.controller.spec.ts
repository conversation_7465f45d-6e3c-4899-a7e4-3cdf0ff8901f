import { Test, TestingModule } from '@nestjs/testing';
import { TempBeginningBalanceController } from './temp-beginning-balance.controller';
import { TempBeginningBalanceService } from './temp-beginning-balance.service';
import { getModelToken } from '@nestjs/mongoose';
import { TempBeginningBalance } from './schema/temp-beginning-balance.schema';
import { AccountingNodeService } from '../accounting-node/accounting-node.service';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { GeneralLedgerRecalculateService } from '../general-ledger/recalculate.service';
import { PolicyService } from '../../policy/policy.service';
import { Policy } from '../../policy/schema/policy.schema';
import { UserRpcService } from '../../rpc/user-rpc.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { TenantRpcService } from '../../rpc/tenant-rpc.service';
import { JournalRecalculateProxyPatternService } from '../proxy-pattern/journal-recalculate-pattern.service';
import { CaslAbilityFactory } from '../../casl/casl-ability.factory';

describe('TempBeginningBalanceController', () => {
  let controller: TempBeginningBalanceController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TempBeginningBalanceController],
      providers: [
        TempBeginningBalanceService,
        PolicyService,
        { provide: getModelToken(Policy.name), useValue: {} },
        { provide: getModelToken(TempBeginningBalance.name), useValue: {} },
        { provide: AccountingNodeService, useValue: {} },
        { provide: GeneralLedgerService, useValue: {} },
        { provide: GeneralLedgerRecalculateService, useValue: {} },
        { provide: UserRpcService, useValue: {} },
        { provide: TradeRpcService, useValue: {} },
        { provide: NotificationRpcService, useValue: {} },
        { provide: CaslAbilityFactory, useValue: {} },
        { provide: JournalRecalculateProxyPatternService, useValue: {} },
        {
          provide: TenantRpcService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<TempBeginningBalanceController>(
      TempBeginningBalanceController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
