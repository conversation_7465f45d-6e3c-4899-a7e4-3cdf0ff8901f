import {
  Body,
  Controller,
  Get,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { TempBeginningBalanceService } from './temp-beginning-balance.service';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { BeginningBalanceDto } from './dto/beginning-balance.dto';
import { UpdateBeginningBalanceDto } from './dto/update-beginning-balance.dto';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';
import { ResetBeginningBalanceDto } from './dto/reset-beginning-balance.dto';
import { PolicyInterceptor } from '../../policy/policy.interceptor';
import { serviceName } from '../../policy/service-name.decorator';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ImportPartnersDto } from './dto/import-partners.dto';
import { publicRpc } from '../../auth/guards/public-event.decorator';
import { FeatureAccessibilityGuard } from '../../casl/guards/feature-accessibility.guard';
import { feature } from '../../casl/guards/feature.decorator';
import { accountingMessagePattern } from '../../../utils/queues.enum';

@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@ApiTags('temp-beginning-balance')
@Controller('temp-beginning-balance')
export class TempBeginningBalanceController {
  constructor(
    private readonly beginningBalanceService: TempBeginningBalanceService,
  ) {}

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'temp_beginning_balance' })
  @abilities({ action: 'list', subject: 'temp_beginning_balance' })
  @Get()
  async findAll(
    @Query() query: BeginningBalanceDto,
    @Req() request: RequestWithUser,
  ) {
    return await this.beginningBalanceService.findAll(
      query,
      request?.user?.code,
      request?.headers['branch'],
    );
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'temp_beginning_balance' })
  @abilities({ action: 'edit', subject: 'temp_beginning_balance' })
  @Patch()
  async update(@Body() dto: UpdateBeginningBalanceDto) {
    return await this.beginningBalanceService.update(dto);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'temp_beginning_balance' })
  @abilities({ action: 'create', subject: 'temp_beginning_balance' })
  @UseInterceptors(PolicyInterceptor)
  @serviceName(['year_beginning_date'])
  @Post()
  async post(@Req() request: RequestWithUser) {
    return await this.beginningBalanceService.post(request);
  }

  @UseGuards(CaslGuard, FeatureAccessibilityGuard)
  @feature({ name: 'temp_beginning_balance' })
  @abilities({ action: 'create', subject: 'temp_beginning_balance' })
  @Post('/reset')
  async reset(
    @Body() dto: ResetBeginningBalanceDto,
    @Req() request: RequestWithUser,
  ) {
    const { type } = dto;
    await this.beginningBalanceService.findAll(
      {
        reset: true,
        type,
      },
      request?.user?.code,
      request?.headers['branch'],
    );
    return true;
  }

  @publicRpc()
  @MessagePattern({ cmd: accountingMessagePattern.import_partners })
  async importPartners(@Payload() payload: ImportPartnersDto) {
    return await this.beginningBalanceService.importPartners(payload);
  }
}
