import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';
import { AccountingNodeModel } from '../../accounting-node/model/accounting-node.model';
//import { PartnerModel } from '../../../trade/partners/model/partner.model';
import { GeneralLedgerModel } from '../../general-ledger/model/general-ledger.model';

@Table({
  tableName: 'beginning_balances',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class BeginningBalanceModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => AccountingNodeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  accounting_node_id: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: true,
  })
  beginning_credit: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: true,
  })
  beginning_debit: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  type: string;

  //   @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  partner_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  is_posted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // Relations
  @BelongsTo(() => AccountingNodeModel, 'accounting_node_id')
  accountingNode: AccountingNodeModel;

  /*   @BelongsTo(() => PartnerModel, 'partner_id')
  partner: PartnerModel; */

  @HasMany(() => GeneralLedgerModel, {
    foreignKey: 'referenceable_id',
    constraints: false,
    scope: {
      referenceable_type: 'beginning_balance',
    },
  })
  generalLedgers: GeneralLedgerModel[];
}
