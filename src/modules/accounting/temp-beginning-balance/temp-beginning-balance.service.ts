import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { TempBeginningBalance } from './schema/temp-beginning-balance.schema';
import { BeginningBalanceDto } from './dto/beginning-balance.dto';

import { AccountingNodeService } from '../accounting-node/accounting-node.service';
import { accountingTypeEnum } from '../accounting-node/interfaces/accounting-type.enum';
import { BeginningBalanceAccountTypes } from './enum/account-types.enum';
import { UpdateBeginningBalanceDto } from './dto/update-beginning-balance.dto';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { journalTypes } from '../general-ledger/schema/general-ledger.schema';
import { CreateGeneralLedgerDto } from '../general-ledger/dto/create-general-ledger.dto';

import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { partnerType } from '../partner-general-ledger/enum/partner-type.enum';
import { ImportPartnersDto } from './dto/import-partners.dto';
import {
  ctaActions,
  ctaSubjects,
  eventType,
  severity,
} from '../../rpc/enum/notification.enum';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { JournalRecalculateProxyPatternService } from '../proxy-pattern/journal-recalculate-pattern.service';
import { paginate } from '../../../utils/dto/pagination.dto';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
import { Calculator } from '../../../utils/calculator';
import { nameOf } from '../../../utils/object-key-name';

@Injectable()
export class TempBeginningBalanceService {
  constructor(
    @InjectModel(TempBeginningBalance.name)
    private beginningBalanceModel: Model<TempBeginningBalance>,

    private readonly accountingNodeService: AccountingNodeService,

    private readonly generalLedgerService: GeneralLedgerService,
    private readonly journalRecalculateProxyPatternService: JournalRecalculateProxyPatternService,
    private readonly tradeRpcService: TradeRpcService,
    private readonly notificationRpcService: NotificationRpcService,
  ) {}

  public async findAll(
    dto: BeginningBalanceDto,
    code: number,
    branch: Types.ObjectId,
  ) {
    const { limit, page, reset, queries, group, type } = dto;
    await this.syncAccounts(reset, type, code, branch);

    const summations = await this.calculateBeginningBalanceSum();

    let accountingNodeIds = [];

    if (group || queries) {
      const accountingNodes = await this.accountingNodeService.findAllRaw(
        null,
        null,
        null,
        group,
        queries,
        branch,
      );
      accountingNodeIds = accountingNodes.map((node) => node._id);
    }
    const search = {} as any;
    if (type) {
      search.type = type;
    }

    if ((group || queries) && accountingNodeIds?.length < 1) {
      return {
        meta: {
          hasNextPage: false,
          hasPrevPage: false,
          next: 2,
          page: 1,
          pages: 1,
          prev: 0,
          total: 0,
        },
        result: [],
        summations,
      };
    }

    if (accountingNodeIds && accountingNodeIds?.length > 0) {
      search.accountingNode = { $in: accountingNodeIds };
    }

    const result = await this.beginningBalanceModel
      .find(search)
      .skip((+page - 1) * +limit)
      .limit(+limit)
      .populate({
        path: 'accountingNode',
        select: 'id code name beginning_balance',
      })
      .lean();

    if (
      type === BeginningBalanceAccountTypes.customer ||
      type === BeginningBalanceAccountTypes.vendor
    ) {
      const partnerTypeEnum =
        type === BeginningBalanceAccountTypes.customer
          ? partnerType.customer
          : partnerType.vendor;

      const partnersIds = result.map(
        (beginningBalance) => beginningBalance.partner,
      );

      const partners = await this.tradeRpcService.getAllPartners(
        code,
        partnersIds,
        partnerTypeEnum,
      );

      for (const beginningBalance of result) {
        const existsPartner = partners.find(
          (partner) => String(partner._id) === String(beginningBalance.partner),
        );

        if (existsPartner) {
          beginningBalance.accountingNode = {
            _id: existsPartner._id,
            code: existsPartner.number,
            name: existsPartner.name,
            balance_type:
              existsPartner.type === partnerType.customer ? 'debit' : 'credit',
            accountingNode: existsPartner.accounting_info.general_account,
            beginning_balance: existsPartner.accounting_info.beginning_balance,
          } as any;

          /*           beginningBalance.partner = {
            _id: existsPartner._id,
            code: existsPartner.number,
            // name: existsPartner.name,
            balance_type:
              existsPartner.type === partnerType.customer ? 'debit' : 'credit',
            accountingNode: existsPartner.accounting_info.general_account,
            beginning_balance: existsPartner.accounting_info.beginning_balance,
          } as any; */
        }
      }
    }

    const meta = await paginate(
      +limit,
      +page,
      this.beginningBalanceModel,
      search,
    );
    return { meta, result, summations } as any;
  }

  public async syncAccounts(
    reset: boolean = false,
    type: BeginningBalanceAccountTypes,
    code: number,
    branch: Types.ObjectId,
  ) {
    const beginningBalanceAccounts = await this.beginningBalanceModel.find({
      type: type,
    });

    const bulkOperations = [];

    if (
      type === BeginningBalanceAccountTypes.customer ||
      type === BeginningBalanceAccountTypes.vendor
    ) {
      const partnerType =
        type === BeginningBalanceAccountTypes.customer
          ? BeginningBalanceAccountTypes.customer
          : BeginningBalanceAccountTypes.vendor;

      const partners = await this.tradeRpcService.getPartnersAbstract(code, [
        partnerType,
      ]);

      for (const partner of partners) {
        const existingPartner = beginningBalanceAccounts.find((acc) => {
          return (
            String(acc.partner) === String(partner._id) &&
            String(acc.accountingNode) ===
              String(partner.accounting_info.general_account)
          );
        });

        const isNegative = partner.accounting_info.beginning_balance < 0;
        const beginningCredit = isNegative
          ? -1 * partner.accounting_info.beginning_balance
          : 0;
        const beginningDebit = isNegative
          ? 0
          : partner.accounting_info.beginning_balance;

        if (existingPartner && reset) {
          bulkOperations.push({
            updateOne: {
              filter: { _id: existingPartner._id },
              update: {
                $set: {
                  beginning_credit: beginningCredit,
                  beginning_debit: beginningDebit,
                  is_posted: true,
                },
              },
            },
          });
        }
        if (!existingPartner) {
          bulkOperations.push({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            insertOne: {
              document: {
                accountingNode: new Types.ObjectId(
                  partner.accounting_info.general_account,
                ),
                partner: new Types.ObjectId(partner._id),
                beginning_credit: beginningCredit,
                beginning_debit: beginningDebit,
                type: partner.type,
              },
            },
          });
        }
      }

      for (const account of beginningBalanceAccounts) {
        const existingPartner = partners.find(
          (partner) =>
            String(partner._id) === String(account.partner) &&
            String(partner.accounting_info.general_account) ===
              String(account.accountingNode),
        );
        if (!existingPartner) {
          bulkOperations.push({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            deleteOne: {
              filter: { _id: account._id },
            },
          });
        }
      }
    } else {
      const accountingNodes = await this.accountingNodeService.findAllRaw(
        null,
        [],
        undefined,
        null,
        null,
        branch,
        false,
        [],
        true,
      );

      for (const node of accountingNodes) {
        const existingAccount = beginningBalanceAccounts.find(
          (acc) => acc.accountingNode.toString() === node._id.toString(),
        );

        const isNegative = node.beginning_balance < 0;
        const beginningCredit = isNegative ? -1 * node.beginning_balance : 0;
        const beginningDebit = isNegative ? 0 : node.beginning_balance;
        if (existingAccount && reset) {
          bulkOperations.push({
            updateOne: {
              filter: { _id: existingAccount._id },
              update: {
                $set: {
                  beginning_credit: beginningCredit,
                  beginning_debit: beginningDebit,
                  is_posted: true,
                },
              },
            },
          });
        }
        if (!existingAccount) {
          bulkOperations.push({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            insertOne: {
              document: {
                accountingNode: node._id,
                beginning_credit: beginningCredit,
                beginning_debit: beginningDebit,
                type:
                  node.type === accountingTypeEnum.party
                    ? node.invoice_party_type
                    : accountingTypeEnum.general,
              },
            },
          });
        }
      }

      for (const account of beginningBalanceAccounts) {
        const existingNode = accountingNodes.find(
          (node) => node._id.toString() === account.accountingNode.toString(),
        );
        if (!existingNode) {
          bulkOperations.push({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            deleteOne: {
              filter: { _id: account._id },
            },
          });
        }
      }
    }

    await this.beginningBalanceModel.bulkWrite(bulkOperations);
  }

  public async calculateBeginningBalanceSum(): Promise<any> {
    const beginningBalanceSum = await this.beginningBalanceModel
      .aggregate([
        {
          $group: {
            _id: null,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            generalCreditSum: {
              $sum: {
                $cond: [{ $eq: ['$type', 'general'] }, '$beginning_credit', 0],
              },
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            generalDebitSum: {
              $sum: {
                $cond: [{ $eq: ['$type', 'general'] }, '$beginning_debit', 0],
              },
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            vendorCreditSum: {
              $sum: {
                $cond: [{ $eq: ['$type', 'vendor'] }, '$beginning_credit', 0],
              },
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            vendorDebitSum: {
              $sum: {
                $cond: [{ $eq: ['$type', 'vendor'] }, '$beginning_debit', 0],
              },
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            customerCreditSum: {
              $sum: {
                $cond: [{ $eq: ['$type', 'customer'] }, '$beginning_credit', 0],
              },
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            customerDebitSum: {
              $sum: {
                $cond: [{ $eq: ['$type', 'customer'] }, '$beginning_debit', 0],
              },
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            overallCreditSum: { $sum: '$beginning_credit' },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            overallDebitSum: { $sum: '$beginning_debit' },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            allPosted: {
              $push: '$is_posted',
            },
          },
        },
        {
          $project: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            generalCreditSum: 1,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            generalDebitSum: 1,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            vendorCreditSum: 1,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            vendorDebitSum: 1,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            customerCreditSum: 1,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            customerDebitSum: 1,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            overallCreditSum: 1,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            overallDebitSum: 1,
            is_posted: {
              $allElementsTrue: ['$allPosted'],
            },
          },
        },
      ])
      .exec();

    return beginningBalanceSum[0];
  }

  public async update(dto: UpdateBeginningBalanceDto) {
    const result = await this.beginningBalanceModel.findOneAndUpdate(
      {
        $or: [
          { accountingNode: dto.accountingNode },
          { partner: dto.accountingNode },
        ],
      },
      {
        beginning_credit: dto.beginning_credit,
        beginning_debit: dto.beginning_debit,
        is_posted: false,
      },
      { new: true },
    );

    return result;
  }

  public async importPartners(dto: ImportPartnersDto) {
    await this.findAll(
      {
        type: dto.type,
      },
      dto.code,
      dto.branch,
    );

    const bulkOperations = dto.partners.map((data) => ({
      updateOne: {
        filter: { partner: data.partner },
        update: {
          $set: {
            beginning_credit: data.beginning_credit,
            beginning_debit: data.beginning_debit,
            is_posted: false,
          },
        },
        upsert: true,
      },
    }));

    await this.beginningBalanceModel.bulkWrite(bulkOperations);

    await this.notificationRpcService.createNotification(dto.code, {
      user: new Types.ObjectId(dto.user_id),
      title: `${dto.type}_import_completed`,
      message: `${dto.type}_import_finished_successfully`,
      severity: severity.high,
      event_type: eventType.reports,
      cta: {
        action: ctaActions.view,
        subject: ctaSubjects.beginning_balance,
        feature_id: undefined,
        query: '',
      },
    });
    return true;
  }

  public async post(request: RequestWithUser) {
    const transactionDate = await this.getTransactionDate(request);

    const beginningBalances = await this.beginningBalanceModel
      .find()
      .populate('accountingNode');

    this.checkIfBalanced(beginningBalances);

    await this.updateBeginningBalance(
      request,
      beginningBalances as UpdateBeginningBalanceDto[],
    );

    const transactions = [];
    for (const beginningBalance of beginningBalances) {
      let currentBalance = 0;
      if (
        (beginningBalance.beginning_credit > 0 &&
          beginningBalance.beginning_debit === 0) ||
        (beginningBalance.beginning_credit === 0 &&
          beginningBalance.beginning_debit > 0)
      ) {
        currentBalance =
          beginningBalance.beginning_credit > 0
            ? -beginningBalance.beginning_credit
            : beginningBalance.beginning_debit;

        transactions.push({
          code: (beginningBalance.accountingNode as any).code,
          credit: beginningBalance.beginning_credit,
          debit: beginningBalance.beginning_debit,
          accountingNode: (beginningBalance.accountingNode as any)._id,
          by_user: request?.user?.user_id,
          description: 'beginning balance journal',
          current_balance: currentBalance,
          partner_id: beginningBalance?.partner ?? undefined,
          partner_type: beginningBalance?.partner
            ? beginningBalance?.type
            : undefined,
          before_balance: 0,
          is_beginning_balance: true,
        });
      }
    }

    if (transactions.length > 0) {
      await this.generalLedgerService.addBeginningBalance(
        request?.user?.code,
        {
          code: request.user?.code,
          transactions: transactions,
          type: journalTypes.temp_beginning_balance,
          by_user: request?.user?.user_id,
          branch: null,
          note: 'beginning balance journal',
          reference: 'beginning balance journal',
          transaction_date: transactionDate,
          is_beginning_balance: true,
        } as CreateGeneralLedgerDto,
        new Types.ObjectId(request?.headers['branch']),
      );
    }

    await this.recalculateZeroBeginningBalances(request, beginningBalances);

    await this.updateIsPosted();

    return beginningBalances;
  }

  private async getTransactionDate(request: RequestWithUser) {
    const fiscalYear = request.policyData.year_beginning_date;

    if (fiscalYear === '' || fiscalYear === undefined) {
      return new Date(new Date().getFullYear(), 0, 1, 0, 0, 0, 0);
    }

    return fiscalYear;
  }

  private checkIfBalanced(transactions: any[]) {
    let totalCredits = new Calculator(0);
    let totalDebits = new Calculator(0);

    for (const record of transactions) {
      totalCredits = totalCredits.plus(record.beginning_credit);
      totalDebits = totalDebits.plus(record.beginning_debit);
    }

    if (totalDebits.round().number() !== totalCredits.round().number()) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.beginningBalanceIsNotBalanced),
        erpExceptionCode.beginningBalanceIsNotBalanced,
      );
    }
  }

  private async recalculateZeroBeginningBalances(
    request: RequestWithUser,
    beginningBalances: any[],
  ) {
    const zeroBeginningBalances = beginningBalances
      .filter(
        (beginningBalance) =>
          beginningBalance.beginning_credit === 0 &&
          beginningBalance.beginning_debit === 0,
      )
      .map((beginningBalance) => (beginningBalance.accountingNode as any)._id);

    if (zeroBeginningBalances.length === beginningBalances.length) {
      const beginningBalanceJournal = await this.generalLedgerService.findOne({
        is_beginning_balance: true,
      });
      if (beginningBalanceJournal) {
        await this.generalLedgerService.remove(
          request?.user?.code,
          beginningBalanceJournal._id,
          false,
          new Types.ObjectId(request?.headers['branch']),
        );
      }
    }

    await this.journalRecalculateProxyPatternService.dispatchRecalculateJob(
      request?.user?.code,
      zeroBeginningBalances,
      new Types.ObjectId(request?.headers['branch']),
    );
  }

  public async updateIsPosted() {
    await this.beginningBalanceModel.updateMany({}, { is_posted: true });
  }

  public async updateBeginningBalance(
    request: RequestWithUser,
    tempBeginningBalances: UpdateBeginningBalanceDto[],
  ) {
    const bulkOperations = [];
    const partnersBulkOperations = [];

    //group all the same accounting node to one group
    const groupedBalances: Record<string, any[]> = tempBeginningBalances.reduce(
      (acc, item) => {
        const key = String(item.accountingNode._id);
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      },
      {},
    );

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const [accountingNodeId, group] of Object.entries(groupedBalances)) {
      // Calculate total debit - credit for the group
      const totalBalance = group.reduce((sum, item) => {
        return sum + (item.beginning_debit - item.beginning_credit);
      }, 0);

      const lastItem = group[group.length - 1];
      bulkOperations.push({
        updateOne: {
          filter: {
            _id: lastItem.accountingNode._id,
          },
          update: {
            $set: {
              beginning_balance: totalBalance,
            },
          },
        },
      });
    }

    await this.accountingNodeService.addBeginningBalance(bulkOperations);

    const partnerTempBeginningBalances = tempBeginningBalances.filter(
      (beginning) => beginning.type !== BeginningBalanceAccountTypes.general,
    );

    for (const tempBeginningBalance of partnerTempBeginningBalances) {
      partnersBulkOperations.push({
        updateOne: {
          filter: {
            _id: tempBeginningBalance.partner,
          },
          update: {
            $set: {
              // eslint-disable-next-line @typescript-eslint/naming-convention
              'accounting_info.beginning_balance':
                tempBeginningBalance.beginning_credit > 0
                  ? -tempBeginningBalance.beginning_credit
                  : tempBeginningBalance.beginning_debit,
            },
          },
        },
      });
    }

    await this.tradeRpcService.updatePartnersBeginningBalance(
      request?.user?.code,
      partnersBulkOperations,
    );
  }
}
