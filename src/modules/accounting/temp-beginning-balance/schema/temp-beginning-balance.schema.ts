import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { AccountingNode } from '../../accounting-node/schema/accounting-node.schema';

@Schema({ timestamps: true })
export class TempBeginningBalance {
  @Prop({ type: Types.ObjectId, ref: AccountingNode.name })
  accountingNode: Types.ObjectId;

  @Prop({ type: Number, required: true })
  beginning_credit: number;

  @Prop({ type: Number, required: true })
  beginning_debit: number;

  @Prop({ type: String, required: true })
  type: string;

  @Prop({ type: Types.ObjectId, required: false })
  partner: Types.ObjectId;

  @Prop({ type: Boolean, default: true })
  is_posted: boolean;
}

export const tempBeginningBalanceSchema =
  SchemaFactory.createForClass(TempBeginningBalance);
