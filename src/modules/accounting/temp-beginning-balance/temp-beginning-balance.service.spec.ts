import { Test, TestingModule } from '@nestjs/testing';
import { TempBeginningBalanceService } from './temp-beginning-balance.service';
import { getModelToken } from '@nestjs/mongoose';
import { AccountingNodeService } from '../accounting-node/accounting-node.service';
import { GeneralLedgerService } from '../general-ledger/general-ledger.service';
import { TempBeginningBalance } from './schema/temp-beginning-balance.schema';
import { GeneralLedgerRecalculateService } from '../general-ledger/recalculate.service';
import { TradeRpcService } from '../../rpc/trade-rpc.service';
import { NotificationRpcService } from '../../rpc/notification-rpc.service';
import { JournalRecalculateProxyPatternService } from '../proxy-pattern/journal-recalculate-pattern.service';

describe('TempBeginningBalanceService', () => {
  let service: TempBeginningBalanceService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TempBeginningBalanceService,
        { provide: getModelToken(TempBeginningBalance.name), useValue: {} },
        { provide: AccountingNodeService, useValue: {} },
        { provide: GeneralLedgerService, useValue: {} },
        { provide: GeneralLedgerRecalculateService, useValue: {} },
        { provide: TradeRpcService, useValue: {} },
        { provide: NotificationRpcService, useValue: {} },
        { provide: JournalRecalculateProxyPatternService, useValue: {} },
      ],
    }).compile();

    service = module.get<TempBeginningBalanceService>(
      TempBeginningBalanceService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
