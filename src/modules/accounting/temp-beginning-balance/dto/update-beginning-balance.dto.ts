import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { Types } from 'mongoose';
import { Transform } from 'class-transformer';
import { BeginningBalanceAccountTypes } from '../enum/account-types.enum';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class UpdateBeginningBalanceDto {
  @ApiProperty({
    required: true,
    type: Types.ObjectId,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  accountingNode: Types.ObjectId;

  @ApiProperty({
    required: true,
    type: Number,
  })
  @IsNumber()
  @Min(0)
  @isBeginningBalanceValid()
  beginning_debit?: number;

  @ApiProperty({
    required: true,
    type: Number,
  })
  @IsNumber()
  @Min(0)
  @isBeginningBalanceValid()
  beginning_credit?: number;

  @ApiProperty({
    required: true,
    type: Types.ObjectId,
  })
  @IsOptional()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  partner: Types.ObjectId;

  @ApiProperty({
    required: false,
    enum: BeginningBalanceAccountTypes,
  })
  @IsOptional()
  @IsEnum(BeginningBalanceAccountTypes)
  type?: BeginningBalanceAccountTypes;
}

export function isBeginningBalanceValid(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isBeginningBalanceValid',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const beginningDebit = args.object['beginning_debit'];
          const beginningCredit = args.object['beginning_credit'];
          const invalid =
            (beginningDebit > 0 && beginningCredit > 0) ||
            (beginningDebit < 0 && beginningCredit < 0);
          if (invalid) {
            return false;
          }
          return true;
        },
        defaultMessage() {
          return 'Only Beginning debit or Beginning credit must be > 0';
        },
      },
    });
  };
}
