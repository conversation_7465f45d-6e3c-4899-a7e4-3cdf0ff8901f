import { BeginningBalanceAccountTypes } from '../enum/account-types.enum';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDefined,
  IsEnum,
  IsMongoId,
  IsOptional,
  IsString,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Types } from 'mongoose';
import { PaginationDto } from '../../../../utils/dto/pagination.dto';
export class BeginningBalanceDto extends PaginationDto {
  @ApiProperty({
    required: false,
    default: BeginningBalanceAccountTypes.general,
  })
  @IsEnum(BeginningBalanceAccountTypes)
  @IsDefined()
  type: BeginningBalanceAccountTypes;

  @ApiProperty({
    required: false,
    type: Boolean,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  reset?: boolean;

  @ApiProperty({
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  queries?: string;

  @ApiProperty({
    required: false,
    type: String,
  })
  @IsOptional()
  @IsMongoId()
  group?: string;

  @ApiHideProperty()
  accountingNode?: string | Types.ObjectId;
}
