import { BeginningBalanceAccountTypes } from '../enum/account-types.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsDefined, IsEnum } from 'class-validator';

export class ResetBeginningBalanceDto {
  @ApiProperty({
    required: true,
    default: BeginningBalanceAccountTypes.general,
  })
  @IsEnum(BeginningBalanceAccountTypes)
  @IsDefined()
  type: BeginningBalanceAccountTypes;
}
