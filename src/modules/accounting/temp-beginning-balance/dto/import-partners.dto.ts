import { BeginningBalanceAccountTypes } from '../enum/account-types.enum';
import {
  ArrayMinSize,
  IsArray,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

import { RpcDto } from '../../../../utils/dto/rpc.dto';

export class Partners {
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  partner: string | Types.ObjectId;

  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  @IsNotEmpty()
  beginning_credit: number;

  @IsNumber(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    { maxDecimalPlaces: 2 },
    { message: 'should be number with maximum of 2 decimal' },
  )
  @IsNotEmpty()
  beginning_debit: number;
}

export class ImportPartnersDto extends RpcDto {
  @IsEnum(BeginningBalanceAccountTypes)
  @IsDefined()
  type: BeginningBalanceAccountTypes;

  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => Partners)
  partners: Partners[];
}
