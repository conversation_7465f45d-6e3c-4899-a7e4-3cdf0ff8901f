import { Module } from '@nestjs/common';
import { TempBeginningBalanceService } from './temp-beginning-balance.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  TempBeginningBalance,
  tempBeginningBalanceSchema,
} from './schema/temp-beginning-balance.schema';
import { TempBeginningBalanceController } from './temp-beginning-balance.controller';
import { AccountingNodeModule } from '../accounting-node/accounting-node.module';
import { GeneralLedgerModule } from '../general-ledger/general-ledger.module';
import { ProxyPatternModule } from '../proxy-pattern/proxy-pattern.module';
import { RpcModule } from '../../rpc/rpc.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { BeginningBalanceModel } from './model/beginning-balance.model';

@Module({
  imports: [
    SequelizeModule.forFeature([BeginningBalanceModel]),
    MongooseModule.forFeature([
      { name: TempBeginningBalance.name, schema: tempBeginningBalanceSchema },
    ]),
    AccountingNodeModule,
    ProxyPatternModule,
    GeneralLedgerModule,
    RpcModule,
  ],
  providers: [TempBeginningBalanceService],
  controllers: [TempBeginningBalanceController],
})
export class TempBeginningBalanceModule {}
