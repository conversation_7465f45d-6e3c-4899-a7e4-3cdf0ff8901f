/* eslint-disable */
import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { BranchModel } from './branch.model';

export const branchDocument = 'branches_document';
@Table({
  tableName: branchDocument,
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class BranchDocumentModel extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.INTEGER,
  })
  branch_id: number;

  @BelongsTo(() => BranchModel)
  branch: BranchModel;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  feature_name: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  auto_serial: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  same_serial: boolean;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  cash_serial: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
  })
  credit_serial: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  default_unit_as_biggest: boolean;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  updated_at: Date;
}
