import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { RolesListModel } from '../model/role-list.model';
import { RoleRepository } from '../../roles/repository/role.repository';

@Injectable()
export class RoleListRepository extends BaseRepository<RolesListModel> {
  constructor(
    @InjectModel(RolesListModel)
    rolesListModel: typeof RolesListModel,
    private readonly roleRepository: RoleRepository,
  ) {
    super(rolesListModel);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async createRoleList(createRoleList, mongoId: string) {
    // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars

    await this.create({
      user_branch_role_id: createRoleList.user_branch_role_id,
      role_id: (await this.roleRepository.getByMongoId(createRoleList.role_id))
        .id,
    });
  }

  public async getByMongoId(mongoId: string) {
    return (
      (await this.findOne(
        { mongo_id: mongoId },
        { attributes: ['id'], raw: true },
      )) || null
    );
  }
}
