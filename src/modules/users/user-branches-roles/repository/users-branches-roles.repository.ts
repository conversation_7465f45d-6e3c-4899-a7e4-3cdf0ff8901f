import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { UsersBranchesRolesModel } from '../model/user-branch-roles.model';
@Injectable()
export class UsersBranchesRoleRepository extends BaseRepository<UsersBranchesRolesModel> {
  constructor(
    @InjectModel(UsersBranchesRolesModel)
    usersBranchesRolesModel: typeof UsersBranchesRolesModel,
  ) {
    super(usersBranchesRolesModel);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async createUserBranchRole(createUserBranch, mongoId: string) {
    // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars

    return await this.create({
      user_id: createUserBranch.user_id,
      branch_id: createUserBranch.branch_id,
      // mongo_id: String(mongoId),
    });
  }

  public async getByMongoId(mongodid: string) {
    return (
      (await this.findOne(
        { mongo_id: mongodid },
        { attributes: ['id'], raw: true },
      )) || null
    );
  }
}
