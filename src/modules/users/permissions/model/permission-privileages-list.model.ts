import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { PermissionPrivilegesTypeModel } from './permission-privileges.model';
import { PermissionsModel } from './permissons.model';

export const permissionsTableListName = 'permission_privileges_list';
@Table({
  tableName: permissionsTableListName,
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class PermissionsPermListModel extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => PermissionsModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  permission_id: number;

  @BelongsTo(() => PermissionsModel)
  permission: PermissionsModel;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  updated_at: Date;

  @ForeignKey(() => PermissionPrivilegesTypeModel)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  privileges_id: number;

  @BelongsTo(() => PermissionPrivilegesTypeModel)
  privileges: PermissionPrivilegesTypeModel;
}
