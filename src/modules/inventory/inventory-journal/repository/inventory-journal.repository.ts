import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { InventoryJournalModel } from '../model/inventory-journal.model';
import { CreateInventoryJournalDto } from '../dto/create-inventory-journal.dto';
import { ItemRepository } from '../../inventory-item/repository/item.repository';
import { ItemUnitPriceRepository } from '../../inventory-item/repository/item-unit-price.repository';
import { StoreRepository } from '../../store/repository/store.repository';
import { BranchRepository } from '../../../users/branch/repository/branch.repository';
import { UserRepository } from '../../../users/users/repository/user.repository';
import { StoreAdjustmentRepository } from '../../store_adjustments/repository/store-adjustment.repository';
import { inventoryJournalReferenceTypeEnum } from '../enum/reference-type.enum';
import { erpExceptionCode } from '../../../../exceptions/exception-code.erp';
import { nameOf } from '../../../../utils/object-key-name';
import { CustomHttpException } from '../../../../utils/custom-http.exception';
import { inventoryJournalTransactionTypeEnum } from '../enum/journal-type.enum';

@Injectable()
export class InventoryJournalRepository extends BaseRepository<InventoryJournalModel> {
  // Private properties to store shared data for operations
  private store: any;
  private branch: any;
  private user: any;

  constructor(
    @InjectModel(InventoryJournalModel)
    inventoryJournalModel: typeof InventoryJournalModel,
    private readonly itemRepository: ItemRepository,
    private readonly itemUnitPriceRepository: ItemUnitPriceRepository,
    private readonly storeRepository: StoreRepository,
    private readonly branchRepository: BranchRepository,
    private readonly userRepository: UserRepository,
    private readonly storeAdjustmentRepository: StoreAdjustmentRepository,
  ) {
    super(inventoryJournalModel);
  }

  /**
   * Create inventory journals - SQL version of the service's createBulk method
   * This method replicates the MongoDB service logic but uses repository pattern
   * Converts MongoDB IDs to SQL IDs and handles all business logic
   */
  public async createBulkJournals(
    createDto: {
      code: number;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      journals: Array<CreateInventoryJournalDto & { mongo_id?: string }>;
      isFromImport: boolean;
    },
    policyData: any,
  ): Promise<InventoryJournalModel[]> {
    try {
      const documentPolicy = policyData.document_policy;

      const newArray = [];
      const number = await this.getNextJournalNumber();

      // Initialize shared data (store, branch, user) from MongoDB IDs early
      // Since all journals have the same store, branch, and user, get them once
      await this.initializeSharedData(createDto.journals[0]);

      // Step 1: Update transaction sequences if not from import and not beginning transaction
      if (
        (createDto.journals[0]
          .transaction_type as unknown as inventoryJournalTransactionTypeEnum) !==
          inventoryJournalTransactionTypeEnum.beginning &&
        !createDto.isFromImport
      ) {
        createDto.journals = await this.updateJournalsTransactionSequence(
          createDto.journals,
        );
      }

      // Step 2: Calculate journal calculations if not from import and not sales order
      if (
        !createDto.isFromImport &&
        createDto.journals[0].reference_doc_type !== 'sales_order'
      ) {
        createDto.journals = await this.calculateJournalCalculations(
          createDto.journals,
          documentPolicy,
        );
      }

      // Step 3: Process each journal (similar to service's for loop)
      let recalculateArray = [];
      for (let index = 0; index < createDto.journals.length; index++) {
        const obj = createDto.journals[index];

        // Note: Unit conversion logic for 'sub' transactions would go here
        // but it requires snapshot service which is MongoDB-specific
        // For now, we'll skip this step in SQL implementation

        // Convert to SQL format and add to array
        const sqlJournal = await this.convertSingleJournalToSqlFormat(
          obj,
          number,
        );
        newArray.push(sqlJournal);

        // Track journals that need recalculation
        if (!createDto.isFromImport) {
          // Note: hasJournalsAfterTransactionDate check would go here
          // For now, we'll add all to recalculate array for safety
          if (
            (createDto.journals[0]
              .transaction_type as unknown as inventoryJournalTransactionTypeEnum) !==
            inventoryJournalTransactionTypeEnum.beginning
          ) {
            recalculateArray.push(obj);
          }
        }
      }

      // Step 4: Create journals using repository pattern
      const journals = await this.bulkCreate(newArray);

      // Step 5: Handle beginning transaction recalculation
      if (
        (createDto.journals[0]
          .transaction_type as unknown as inventoryJournalTransactionTypeEnum) ===
          inventoryJournalTransactionTypeEnum.beginning &&
        !createDto.isFromImport
      ) {
        recalculateArray.push(...newArray);
        recalculateArray = recalculateArray.flat(1);
      }

      // Note: Snapshot recalculation logic would go here
      // but it requires snapshot services which are MongoDB-specific
      // For SQL implementation, this would need to be handled separately

      return journals;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Initialize shared data (store, branch, user) from MongoDB IDs
   */
  private async initializeSharedData(
    journal: CreateInventoryJournalDto,
  ): Promise<void> {
    this.store = await this.storeRepository.getOneByMongoId(
      String(journal.store),
    );

    this.branch = await this.branchRepository.getOneByMongoId(
      String(journal.branch),
    );

    this.user = await this.userRepository.getOneByMongoId(
      String(journal.by_user),
    );

    if (!this.store || !this.branch || !this.user) {
      throw new Error(
        `Missing required mappings: store=${this.store?.id}, branch=${this.branch?.id}, user=${this.user?.id}`,
      );
    }
  }

  /**
   * Get the last journal entry for a specific store/item combination
   */
  private async getLastJournalForStoreItem(
    storeId: number,
    itemId: number,
  ): Promise<InventoryJournalModel | null> {
    return await this.findOne(
      {
        store_id: storeId,
        item_id: itemId,
      },
      {
        order: [
          ['transaction_date', 'DESC'],
          ['transaction_sequence', 'DESC'],
        ],
      },
    );
  }

  /**
   * Get next journal number using repository pattern
   */
  private async getNextJournalNumber(): Promise<number> {
    const lastJournal = await this.findOne(
      {},
      {
        order: [['number', 'DESC']],
      },
    );
    return (lastJournal?.number || 0) + 1;
  }

  /**
   * Update journals transaction sequence - SQL version
   * Converts MongoDB IDs to SQL IDs for store/item lookups
   */
  private async updateJournalsTransactionSequence(
    journals: Array<CreateInventoryJournalDto>,
  ): Promise<Array<CreateInventoryJournalDto>> {
    // Group journals by item and store (using MongoDB IDs as keys)
    const groupedData = journals.reduce((acc, obj) => {
      const key = String(obj.item) + '-' + String(obj.store);
      if (!acc[key]) {
        acc[key] = {
          item: String(obj.item),
          store: String(obj.store),
          data: [],
        };
      }
      acc[key].data.push(obj);
      return acc;
    }, {});

    // Process each group
    for (const group of Object.values(groupedData)) {
      const { store: storeMongoId, item: itemMongoId, data }: any = group;

      const dateToSequenceMap = new Map();
      for (const record of data) {
        const dateOfJournal = new Date(record.transaction_date).toDateString();
        if (!dateToSequenceMap.has(dateOfJournal)) {
          // Pass MongoDB IDs to findLastSequence which will convert them internally
          const lastSequence = await this.findLastSequence(
            storeMongoId,
            itemMongoId,
            dateOfJournal,
          );
          dateToSequenceMap.set(
            dateOfJournal,
            lastSequence > 0 ? lastSequence + 1 : 1,
          );
        }

        record.transaction_sequence = dateToSequenceMap.get(dateOfJournal);
        dateToSequenceMap.set(
          dateOfJournal,
          dateToSequenceMap.get(dateOfJournal) + 1,
        );
      }
    }

    // Flatten and sort the updated data
    const updatedData = Object.values(groupedData).flatMap(
      (group: any) => group.data,
    );
    updatedData.sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime(),
    );
    return updatedData;
  }

  /**
   * Find last sequence for a specific store, item, and date using repository pattern
   */
  private async findLastSequence(
    _storeMongoId: string,
    itemMongoId: string,
    date: string,
  ): Promise<number> {
    const dateOnly = new Date(date);
    const startOfDay = new Date(
      dateOnly.getFullYear(),
      dateOnly.getMonth(),
      dateOnly.getDate(),
    );
    const endOfDay = new Date(
      dateOnly.getFullYear(),
      dateOnly.getMonth(),
      dateOnly.getDate(),
      23,
      59,
      59,
      999,
    );

    // Get SQL IDs for store and item using private properties
    const store = this.store;
    const itemMapping = await this.itemRepository.findByMongoIds([itemMongoId]);
    const itemId = itemMapping.get(itemMongoId);

    if (!store || !itemId) {
      return 0;
    }

    // Use repository pattern with date range filtering
    const lastJournal = await this.findOne(
      {
        store_id: store.id,
        item_id: itemId,
        transaction_date: {
          $gte: startOfDay,
          $lt: endOfDay,
        },
      },
      {
        order: [['transaction_sequence', 'DESC']],
      },
    );

    return lastJournal?.transaction_sequence || 0;
  }

  /**
   * Calculate journal calculations - SQL version
   */
  private async calculateJournalCalculations(
    journals: Array<CreateInventoryJournalDto>,
    documentPolicy: string,
  ): Promise<Array<CreateInventoryJournalDto>> {
    // Group journals by store and item
    const groupedData = await this.groupJournalsByStoreAndItem(journals);

    // Get all unique item mongo IDs
    const itemMongoIds = Object.values(groupedData).map((record: any) =>
      String(record.item),
    );

    // Get item mappings
    const itemMapping = await this.itemRepository.findByMongoIds(itemMongoIds);

    // Process each group
    for (const group of Object.values(groupedData)) {
      const { item, data }: any = group;

      const itemId = itemMapping.get(item);

      if (!itemId) {
        continue;
      }

      // Get last journal for this store/item combination using private properties
      const storeData = this.store;

      const lastJournal = await this.getLastJournalForStoreItem(
        storeData.id,
        itemId,
      );

      let totalCostInBaseUnit =
        lastJournal?.store_level_total_cost_in_base_unit ?? 0;
      let avgCostInBaseUnit =
        lastJournal?.store_level_avg_cost_in_base_unit ?? 0;
      let totalQuantitiesInBaseUnit =
        lastJournal?.store_level_total_quantity_in_base_unit ?? 0;

      // Process each record in the group
      for (const record of data) {
        // Get unit data
        const unitMapping =
          await this.itemUnitPriceRepository.findByItemIdsAndUnitMongoIds(
            [itemId],
            [String(record.unit)],
          );

        const unitData = unitMapping.get(`${itemId}_${String(record.unit)}`);
        const baseFactor = unitData?.base_factor || 1;

        const quantityInBaseUnit = record.quantity * baseFactor;
        const costInBaseUnit =
          record.cost === undefined ? 0 : record.cost * baseFactor;

        // Apply transaction type logic
        if (
          (record.transaction_type as unknown as inventoryJournalTransactionTypeEnum) ===
          inventoryJournalTransactionTypeEnum.beginning
        ) {
          if (documentPolicy === 'adjustment') {
            totalQuantitiesInBaseUnit += quantityInBaseUnit;
            totalCostInBaseUnit += quantityInBaseUnit * costInBaseUnit;
            avgCostInBaseUnit =
              avgCostInBaseUnit === 0
                ? costInBaseUnit
                : totalCostInBaseUnit / totalQuantitiesInBaseUnit;
          } else {
            totalQuantitiesInBaseUnit = quantityInBaseUnit;
            totalCostInBaseUnit = quantityInBaseUnit * costInBaseUnit;
            avgCostInBaseUnit = costInBaseUnit;
          }
        }

        if (
          (record.transaction_type as unknown as inventoryJournalTransactionTypeEnum) ===
          inventoryJournalTransactionTypeEnum.add
        ) {
          totalQuantitiesInBaseUnit += quantityInBaseUnit;
          totalCostInBaseUnit += quantityInBaseUnit * costInBaseUnit;
          avgCostInBaseUnit =
            avgCostInBaseUnit === 0
              ? costInBaseUnit
              : totalCostInBaseUnit / totalQuantitiesInBaseUnit;
        }

        if (
          (record.transaction_type as unknown as inventoryJournalTransactionTypeEnum) ===
          inventoryJournalTransactionTypeEnum.sub
        ) {
          totalQuantitiesInBaseUnit -= quantityInBaseUnit;
          const totalCost =
            costInBaseUnit !== 0
              ? quantityInBaseUnit * costInBaseUnit
              : quantityInBaseUnit * avgCostInBaseUnit;
          totalCostInBaseUnit -= totalCost;

          if (
            (record.reference_doc_type === 'purchase_invoice' && record.cost) ||
            (record.cost && record.related_journal_id)
          ) {
            avgCostInBaseUnit =
              totalQuantitiesInBaseUnit === 0
                ? 0
                : totalCostInBaseUnit / totalQuantitiesInBaseUnit;
          }
        }

        if (
          (record.transaction_type as unknown as inventoryJournalTransactionTypeEnum) ===
          inventoryJournalTransactionTypeEnum.override
        ) {
          totalQuantitiesInBaseUnit = quantityInBaseUnit;
          if (costInBaseUnit !== 0) {
            totalCostInBaseUnit = quantityInBaseUnit * costInBaseUnit;
            avgCostInBaseUnit = costInBaseUnit;
          } else {
            totalCostInBaseUnit = quantityInBaseUnit * avgCostInBaseUnit;
          }
        }

        // Set calculations as individual fields (not nested object like MongoDB)
        // In SQL model, calculations are stored as individual fields, not nested object
        record.price = record.price || 0;
        record.transaction_quantity_in_base_unit = quantityInBaseUnit;
        record.transaction_cost_in_base_unit = costInBaseUnit;
        record.store_level_total_quantity_in_base_unit =
          totalQuantitiesInBaseUnit;
        record.store_level_total_cost_in_base_unit = totalCostInBaseUnit;
        record.store_level_avg_cost_in_base_unit = avgCostInBaseUnit;
      }
    }

    // Flatten and sort the updated data
    const updatedData = Object.values(groupedData).flatMap(
      (group: any) => group.data,
    );
    updatedData.sort(
      (a, b) =>
        new Date(a.transaction_date).getTime() -
        new Date(b.transaction_date).getTime(),
    );
    return updatedData;
  }

  /**
   * Resolve polymorphic reference ID from MongoDB to SQL
   */
  private async resolveReferenceId(
    referenceMongoId: string,
    referenceType: string,
  ): Promise<number | null> {
    switch (referenceType) {
      case inventoryJournalReferenceTypeEnum.store_adjustment:
        const storeAdjustment = await this.storeAdjustmentRepository.findOne(
          { mongo_id: referenceMongoId },
          { attributes: ['id'], raw: true },
        );
        return storeAdjustment?.id || null;

      case inventoryJournalReferenceTypeEnum.physical_inventory_posting:
      case inventoryJournalReferenceTypeEnum.sales_invoice:
      case inventoryJournalReferenceTypeEnum.sales_return_invoice:
      case inventoryJournalReferenceTypeEnum.purchase_invoice:
      case inventoryJournalReferenceTypeEnum.purchase_return_invoice:
      case inventoryJournalReferenceTypeEnum.sales_order:

      default:
        // Unknown reference type, return null
        throw new CustomHttpException(
          nameOf(
            erpExceptionCode,
            (exception) => exception.referenceTypeNotSupported,
          ),
          erpExceptionCode.referenceTypeNotSupported,
          {
            reference_type: referenceType,
          },
        );
    }
  }

  /**
   * Convert a single journal to SQL format with ID mapping
   */
  private async convertSingleJournalToSqlFormat(
    // eslint-disable-next-line @typescript-eslint/naming-convention
    journal: CreateInventoryJournalDto & { mongo_id?: string },
    number: number,
  ): Promise<any> {
    // Get item and unit mappings
    const itemMapping = await this.itemRepository.findByMongoIds([
      String(journal.item),
    ]);
    const itemId = itemMapping.get(String(journal.item));

    if (!itemId) {
      throw new Error(`Missing item mapping: item=${String(journal.item)}`);
    }

    const unitPriceMapping =
      await this.itemUnitPriceRepository.findByItemIdsAndUnitMongoIds(
        [itemId],
        [String(journal.unit)],
      );
    const unitData = unitPriceMapping.get(`${itemId}_${String(journal.unit)}`);

    // Resolve polymorphic reference
    const referenceId = await this.resolveReferenceId(
      String(journal.reference_id),
      journal.reference_doc_type,
    );

    return {
      number: number,
      referenceable_id: referenceId,
      referenceable_type: journal.reference_doc_type,
      transaction_type:
        journal.transaction_type as unknown as inventoryJournalTransactionTypeEnum,
      store_id: this.store.id,
      item_id: itemId,
      unit_id: unitData?.unit_id || null,
      quantity: journal.quantity,
      beginning_quantity: journal.beginning_quantity || null,
      cost: journal.cost || null,
      note: journal.note || null,
      transaction_date: journal.transaction_date || new Date(),
      transaction_sequence: journal.transaction_sequence || null,
      branch_id: this.branch.id,
      by_user_id: this.user.id,
      price: journal.price || journal.cost || 0,
      transaction_cost_in_base_unit:
        (journal as any).transaction_cost_in_base_unit || 0,
      transaction_quantity_in_base_unit:
        (journal as any).transaction_quantity_in_base_unit || 0,
      store_level_total_quantity_in_base_unit:
        (journal as any).store_level_total_quantity_in_base_unit || 0,
      store_level_total_cost_in_base_unit:
        (journal as any).store_level_total_cost_in_base_unit || 0,
      store_level_avg_cost_in_base_unit:
        (journal as any).store_level_avg_cost_in_base_unit || 0,
      related_journal_id: journal.related_journal_id || null,
      mongo_id: journal.mongo_id || null, // Store the MongoDB ID for linking
      unposted: false,
    };
  }

  /**
   * Convert processed journals to SQL format with ID mapping
   */
  private async convertToSqlFormat(
    journals: Array<CreateInventoryJournalDto>,
    number: number,
  ): Promise<any[]> {
    const sqlJournals = [];

    // Get all unique MongoDB IDs for batch conversion (only for items and units)
    const itemMongoIds = [...new Set(journals.map((j) => j.item))];
    const unitMongoIds = [...new Set(journals.map((j) => j.unit))];

    // Since all journals have the same store, branch, and user, get them once and assign to private properties
    this.store = await this.storeRepository.getOneByMongoId(journals[0].store);
    this.branch = await this.branchRepository.getOneByMongoId(
      String(journals[0].branch),
    );
    this.user = await this.userRepository.getOneByMongoId(
      String(journals[0].by_user),
    );

    if (!this.store || !this.branch || !this.user) {
      throw new Error(
        `Missing required mappings: store=${this.store?.id}, branch=${this.branch?.id}, user=${this.user?.id}`,
      );
    }

    // Get batch mappings only for items and units (which can vary per journal)
    const itemMapping = await this.itemRepository.findByMongoIds(itemMongoIds);
    const itemIds = Array.from(itemMapping.values());
    const unitPriceMapping =
      await this.itemUnitPriceRepository.findByItemIdsAndUnitMongoIds(
        itemIds,
        unitMongoIds,
      );

    // Convert each journal
    for (const journal of journals) {
      const itemId = itemMapping.get(journal.item);
      const unitData = unitPriceMapping.get(`${itemId}_${journal.unit}`);

      // Resolve polymorphic reference
      const referenceId = await this.resolveReferenceId(
        journal.reference_id,
        journal.reference_doc_type,
      );

      if (!itemId) {
        throw new Error(`Missing item mapping: item=${itemId}`);
      }

      sqlJournals.push({
        number: number,
        referenceable_id: referenceId,
        referenceable_type: journal.reference_doc_type,
        transaction_type:
          journal.transaction_type as unknown as inventoryJournalTransactionTypeEnum,
        store_id: this.store.id,
        item_id: itemId,
        unit_id: unitData?.unit_id || null,
        quantity: journal.quantity,
        beginning_quantity: journal.beginning_quantity || null,
        cost: journal.cost || null,
        note: journal.note || null,
        transaction_date: journal.transaction_date || new Date(),
        transaction_sequence: journal.transaction_sequence || null,
        branch_id: this.branch.id,
        by_user_id: this.user.id, // Use private properties
        price: journal.price || journal.cost || 0,
        transaction_cost_in_base_unit:
          (journal as any).transaction_cost_in_base_unit || 0,
        transaction_quantity_in_base_unit:
          (journal as any).transaction_quantity_in_base_unit || 0,
        store_level_total_quantity_in_base_unit:
          (journal as any).store_level_total_quantity_in_base_unit || 0,
        store_level_total_cost_in_base_unit:
          (journal as any).store_level_total_cost_in_base_unit || 0,
        store_level_avg_cost_in_base_unit:
          (journal as any).store_level_avg_cost_in_base_unit || 0,
        related_journal_id: journal.related_journal_id || null,
        unposted: false,
      });
    }

    return sqlJournals;
  }

  /**
   * Group journals by store and item
   */
  private async groupJournalsByStoreAndItem(
    journals: CreateInventoryJournalDto[],
  ): Promise<Record<string, any>> {
    return journals.reduce(
      (acc: Record<string, any>, obj: CreateInventoryJournalDto) => {
        const key = String(obj.item) + '-' + String(obj.store);
        if (!acc[key]) {
          acc[key] = {
            item: String(obj.item),
            store: String(obj.store),
            data: [],
          };
        }
        acc[key].data.push(obj);
        return acc;
      },
      {},
    );
  }
}
