import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { StoreSnapshotUnitCostModel } from '../model/store-snapshot-unit-cost.model';

@Injectable()
export class StoreSnapshotUnitCostRepository extends BaseRepository<StoreSnapshotUnitCostModel> {
  constructor(
    @InjectModel(StoreSnapshotUnitCostModel)
    storeSnapshotUnitCostModel: typeof StoreSnapshotUnitCostModel,
  ) {
    super(storeSnapshotUnitCostModel);
  }
}
