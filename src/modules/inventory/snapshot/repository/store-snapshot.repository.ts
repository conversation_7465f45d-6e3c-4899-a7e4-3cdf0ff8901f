import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { StoreSnapshotModel } from '../model/store-snapshot.model';

@Injectable()
export class StoreSnapshotRepository extends BaseRepository<StoreSnapshotModel> {
  constructor(
    @InjectModel(StoreSnapshotModel)
    storeSnapshotModel: typeof StoreSnapshotModel,
  ) {
    super(storeSnapshotModel);
  }
}
