import { Module } from '@nestjs/common';
import { Bundle, bundleSchema } from './schema/bundle.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { SequelizeModule } from '@nestjs/sequelize';
import { BundlesController } from './bundles.controller';
import { BundlesService } from './bundles.service';
import { Item, itemSchema } from '../inventory-item/schema/item.schema';
import { Unit, unitSchema } from '../unit/schema/unit.schema';
import { BundleModel } from './model/bundle.model';
import { BundleItemModel } from './model/bundle-item.model';
import { BundleRepository } from './repository/bundle.repository';
import { BundleItemRepository } from './repository/bundle-item.repository';
import { ItemModel } from '../inventory-item/model/item.model';
import { UnitModel } from '../unit/model/unit.model';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Bundle.name, schema: bundleSchema },
      { name: Item.name, schema: itemSchema },
      { name: Unit.name, schema: unitSchema },
    ]),
    SequelizeModule.forFeature([
      BundleModel,
      BundleItemModel,
      ItemModel,
      UnitModel,
    ]),
  ],
  controllers: [BundlesController],
  providers: [BundlesService, BundleRepository, BundleItemRepository],
  exports: [BundlesService, BundleRepository, BundleItemRepository],
})
export class BundleModule {}
