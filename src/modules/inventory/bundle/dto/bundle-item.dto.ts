import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsNotEmpty, IsN<PERSON><PERSON>, Min } from 'class-validator';
import { Types } from 'mongoose';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class BundleItemDto {
  @ApiProperty({
    description: 'Item ID reference',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  item: Types.ObjectId;

  @ApiProperty({
    description: 'Unit ID reference',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  unit: Types.ObjectId;

  @ApiProperty({
    description: 'Quantity of the item in the bundle',
    example: 2,
    minimum: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  qty: number;

  @ApiProperty({
    description: 'Price per unit of the item',
    example: 25.5,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  price: number;

  @ApiProperty({
    description: 'Total price (qty * price)',
    example: 51.0,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  total: number;
}
