import { IsEnum, IsOptional, IsBoolean } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { bundleType } from '../types/bundle-types.enum';
import { PaginationDto } from '../../../../utils/dto';

export class GetBundlesPaginationDto extends PaginationDto {
  @IsEnum(bundleType)
  @IsOptional()
  type?: bundleType;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value == 'true')
  status?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value == 'true')
  nested_bundles?: boolean;
}
