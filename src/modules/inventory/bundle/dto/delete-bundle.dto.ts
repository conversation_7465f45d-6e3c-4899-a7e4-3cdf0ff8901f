import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Types } from 'mongoose';
import { IsNotEmpty } from 'class-validator';
import { safeMongoIdTransformer } from '../../../../utils/transformers/mongo-id-transformer';

export class DeleteBundleDto {
  @ApiProperty({
    description: 'bundle id',
    example: '507f1f77bcf86cd799439011',
    required: true,
  })
  @IsNotEmpty()
  @Transform(({ value, key }) =>
    safeMongoIdTransformer({ value, property: key }),
  )
  id: Types.ObjectId;
}
