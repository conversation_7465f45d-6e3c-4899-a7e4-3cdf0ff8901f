import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { CreateBundleDto } from './create-bundle.dto';
import { BundleItemDto } from './bundle-item.dto';

export class UpdateBundleDto extends PartialType(CreateBundleDto) {
  @ApiProperty({
    description: 'Array of bundle items with item, unit, qty, price, and total',
    required: false,
    type: [BundleItemDto],
    example: [
      {
        item: '68504d110b01f0fce473de4e',
        unit: '68504d110b01f0fce473dd36',
        qty: 2,
        price: 25.5,
        total: 51.0,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1, {
    message:
      'At least one bundle item is required when bundle_items is provided',
  })
  @ValidateNested({ each: true })
  @Type(() => BundleItemDto)
  bundle_items?: BundleItemDto[];
}
