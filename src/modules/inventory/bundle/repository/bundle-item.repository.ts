import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { BundleItemModel } from '../model/bundle-item.model';
import { ItemModel } from '../../inventory-item/model/item.model';
import { UnitModel } from '../../unit/model/unit.model';

@Injectable()
export class BundleItemRepository extends BaseRepository<BundleItemModel> {
  constructor(
    @InjectModel(BundleItemModel) bundleItem: typeof BundleItemModel,
    @InjectModel(ItemModel) private readonly itemModel: typeof ItemModel,
    @InjectModel(UnitModel) private readonly unitModel: typeof UnitModel,
  ) {
    super(bundleItem);
  }

  private async getItemId(
    mongoItemId: string,
    schemaName: string,
  ): Promise<number | null> {
    if (!mongoItemId) return null;

    const item = await this.itemModel.schema(schemaName).findOne({
      where: { mongo_id: mongoItemId.toString() },
      attributes: ['id'],
    });

    return item ? item.id : null;
  }

  private async getUnitId(
    mongoUnitId: string,
    schemaName: string,
  ): Promise<number | null> {
    if (!mongoUnitId) return null;

    const unit = await this.unitModel.schema(schemaName).findOne({
      where: { mongo_id: mongoUnitId.toString() },
      attributes: ['id'],
    });

    return unit ? unit.id : null;
  }

  private async mapBundleItemData(
    bundleId: number,
    bundleItem: any,
    schemaName: string,
  ): Promise<Partial<BundleItemModel> | null> {
    const itemId = await this.getItemId(String(bundleItem.item), schemaName);
    const unitId = await this.getUnitId(String(bundleItem.unit), schemaName);

    if (!itemId || !unitId) {
      console.warn(
        `Skipping bundle item - Item ID: ${itemId}, Unit ID: ${unitId}`,
      );
      return null;
    }

    return {
      bundle_id: bundleId,
      item_id: itemId,
      unit_id: unitId,
      qty: bundleItem.qty || 1,
      price: bundleItem.price || 0,
      total: bundleItem.total || 0,
      mongo_id: `${bundleId}_${bundleItem.item}`,
    };
  }

  public async bulkCreateBundleItems(
    bundleId: number,
    bundleItems: any[],
    schemaName: string,
  ) {
    const bundleItemsData = [];

    for (const bundleItem of bundleItems) {
      const mappedData = await this.mapBundleItemData(
        bundleId,
        bundleItem,
        schemaName,
      );
      if (mappedData) {
        bundleItemsData.push(mappedData);
      }
    }

    if (bundleItemsData.length === 0) {
      console.warn('No valid bundle items to create');
      return [];
    }

    return await this.bulkCreate(bundleItemsData);
  }
}
