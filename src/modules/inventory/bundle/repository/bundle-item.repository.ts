import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { BundleItemModel } from '../model/bundle-item.model';
import { ItemRepository } from '../../inventory-item/repository/item.repository';
import { UnitRepository } from '../../unit/repository/unit.repository';

@Injectable()
export class BundleItemRepository extends BaseRepository<BundleItemModel> {
  constructor(
    @InjectModel(BundleItemModel) bundleItem: typeof BundleItemModel,
    private readonly itemRepository: ItemRepository,
    private readonly unitRepository: UnitRepository,
  ) {
    super(bundleItem);
  }

  private async getItemId(mongoItemId: string): Promise<number | null> {
    if (!mongoItemId) return null;

    const itemMap = await this.itemRepository.findByMongoIds([mongoItemId.toString()]);
    return itemMap.get(mongoItemId.toString()) || null;
  }

  private async getUnitId(mongoUnitId: string): Promise<number | null> {
    if (!mongoUnitId) return null;

    const unitMap = await this.unitRepository.findByMongoIds([mongoUnitId.toString()]);
    return unitMap.get(mongoUnitId.toString()) || null;
  }

  private async mapBundleItemData(
    bundleId: number,
    bundleItem: any,
  ): Promise<Partial<BundleItemModel> | null> {
    const itemId = await this.getItemId(String(bundleItem.item));
    const unitId = await this.getUnitId(String(bundleItem.unit));

    if (!itemId || !unitId) {
      console.warn(
        `Skipping bundle item - Item ID: ${itemId}, Unit ID: ${unitId}`,
      );
      return null;
    }

    return {
      bundle_id: bundleId,
      item_id: itemId,
      unit_id: unitId,
      qty: bundleItem.qty || 1,
      price: bundleItem.price || 0,
      total: bundleItem.total || 0,
      mongo_id: `${bundleId}_${bundleItem.item}`,
    };
  }

  public async bulkCreateBundleItems(bundleId: number, bundleItems: any[]) {
    const bundleItemsData = [];

    for (const bundleItem of bundleItems) {
      const mappedData = await this.mapBundleItemData(bundleId, bundleItem);
      if (mappedData) {
        bundleItemsData.push(mappedData);
      }
    }

    if (bundleItemsData.length === 0) {
      return [];
    }

    return await this.bulkCreate(bundleItemsData);
  }
}
