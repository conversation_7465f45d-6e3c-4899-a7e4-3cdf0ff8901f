import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { BundleModel } from '../model/bundle.model';
import { CreateBundleDto } from '../dto/create-bundle.dto';
import { BundleItemRepository } from './bundle-item.repository';

@Injectable()
export class BundleRepository extends BaseRepository<BundleModel> {
  constructor(
    @InjectModel(BundleModel) bundle: typeof BundleModel,
    private readonly bundleItemRepository: BundleItemRepository,
  ) {
    super(bundle);
  }

  private mapBundleData(
    createBundleDto: CreateBundleDto,
    mongoId: string,
  ): Partial<BundleModel> {
    return {
      code: createBundleDto.code,
      name_en: createBundleDto.name?.en,
      name_ar: createBundleDto.name?.ar,
      nested_bundles: createBundleDto.nested_bundles || false,
      status:
        createBundleDto.status !== undefined ? createBundleDto.status : true,
      type: createBundleDto.type,
      number_of_items: createBundleDto.number_of_items,
      description: createBundleDto.description,
      bundle_price: createBundleDto.bundle_price || 0,
      original_price: createBundleDto.original_price || 0,
      discount_percentage: createBundleDto.discount_percentage || 0,
      mongo_id: mongoId,
    };
  }

  public async createBundle(createBundleDto: CreateBundleDto, mongoId: string) {
    // Create the bundle
    const data = this.mapBundleData(createBundleDto, mongoId);
    const sqlBundle = await this.create(data);

    // Bulk create bundle items if they exist
    let bundleItems = null;
    if (
      createBundleDto.bundle_items &&
      createBundleDto.bundle_items.length > 0
    ) {
      bundleItems = await this.bundleItemRepository.bulkCreateBundleItems(
        sqlBundle.id,
        createBundleDto.bundle_items,
      );
    }

    // Return both the bundle and bundle items
    return {
      bundle: sqlBundle,
      bundleItems,
    };
  }
}
