import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateBundleDto } from './dto/create-bundle.dto';
import { UpdateBundleDto } from './dto/update-bundle.dto';
import { GetBundlesPaginationDto } from './dto/get-bundles-pagination.dto';
import { BundlesService } from './bundles.service';
import { Bundle } from './schema/bundle.schema';

import { ApiHeader, ApiParam, ApiTags } from '@nestjs/swagger';
import { DeleteBundleDto } from './dto/delete-bundle.dto';
import { CaslGuard } from '../../casl/guards/casl.guard';
import { abilities } from '../../casl/guards/abilities.decorator';

@Controller('bundles')
@ApiHeader({
  name: 'branch',
  description: 'branch sent in request headers',
  required: true,
})
@ApiTags('bundles')
export class BundlesController {
  constructor(private readonly bundlesService: BundlesService) {}

  @UseGuards(CaslGuard)
  @abilities({ action: 'create', subject: 'bundle' })
  @Post()
  create(@Body() createDto: CreateBundleDto) {
    return this.bundlesService.create(createDto);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'abstract_bundles' })
  @Get('abstract')
  async abstract(@Query() query: GetBundlesPaginationDto) {
    return await this.bundlesService.abstract(query);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'list', subject: 'bundle' })
  @Get()
  findAll(@Query() query: GetBundlesPaginationDto) {
    return this.bundlesService.findAll(query);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'read', subject: 'bundle' })
  @Get(':id')
  @ApiParam({ name: 'id' })
  findOne(@Param('id') id: string): Promise<Bundle> {
    return this.bundlesService.findOne(id);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'edit', subject: 'bundle' })
  @Patch(':id')
  @ApiParam({ name: 'id' })
  update(
    @Param('id') id: string,
    @Body() updateBundleDto: UpdateBundleDto,
  ): Promise<Bundle> {
    return this.bundlesService.update(id, updateBundleDto);
  }

  @UseGuards(CaslGuard)
  @abilities({ action: 'delete', subject: 'bundle' })
  @Delete()
  delete(@Body() dto: DeleteBundleDto) {
    return this.bundlesService.delete(dto.id);
  }
}
