import {
  Table,
  Column,
  Model,
  DataType,
  HasMany,
} from 'sequelize-typescript';
import { bundleType } from '../types/bundle-types.enum';
import { BundleItemModel } from './bundle-item.model';

@Table({
  tableName: 'bundles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class BundleModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  code: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name_en: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  name_ar: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  nested_bundles: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
  })
  status: boolean;

  @Column({
    type: DataType.ENUM(...Object.values(bundleType)),
    allowNull: false,
  })
  type: bundleType;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
  })
  number_of_items: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  description: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0,
  })
  bundle_price: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0,
  })
  original_price: number;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true,
    defaultValue: 0,
  })
  discount_percentage: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  //TODO remove once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  mongo_id: string;

  // Relationships - commented out for migrations
  @HasMany(() => BundleItemModel, 'bundle_id')
  bundleItems: BundleItemModel[];
}
