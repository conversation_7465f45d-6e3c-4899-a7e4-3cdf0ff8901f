import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { BundleModel } from './bundle.model';
import { ItemModel } from '../../inventory-item/model/item.model';
import { UnitModel } from '../../unit/model/unit.model';

@Table({
  tableName: 'bundle_items',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class BundleItemModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => BundleModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  bundle_id: number;

  @ForeignKey(() => ItemModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  item_id: number;

  @ForeignKey(() => UnitModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  unit_id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
  })
  qty: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  price: number;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
  })
  total: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  deleted_at: Date;

  //TODO remove once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // Relationships - commented out for migrations
  @BelongsTo(() => BundleModel, 'bundle_id')
  bundle: BundleModel;

  @BelongsTo(() => ItemModel, 'item_id')
  item: ItemModel;

  @BelongsTo(() => UnitModel, 'unit_id')
  unit: UnitModel;
}
