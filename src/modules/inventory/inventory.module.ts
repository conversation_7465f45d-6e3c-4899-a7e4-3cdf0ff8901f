import { Module } from '@nestjs/common';
import { APP_FILTER, RouterModule } from '@nestjs/core';
import AppConfig from '../../config/app.config';
import { PhysicalInventoryPostingModule } from './physical-inventory-posting/physical-inventory-posting.module';
import { PhysicalInventoryModule } from './physical-inventory/physical-inventory.module';
import { GroupItemModule } from './group-item/group-item.module';
import { ItemsModule } from './inventory-item/items.module';
import { InventoryJournalModule } from './inventory-journal/inventory-journal.module';
import { InventorySnapshotModule } from './snapshot/inventory-snapshot.module';
import { StoreModule } from './store/store.module';
import { StoreAdjustmentsModule } from './store_adjustments/store-adjustments.module';
import { UnitModule } from './unit/unit.module';
import { TransactionsModule } from './transactions/transactions.module';
import { UpdateFeaturesCostModule } from './update-features-cost/update-features-cost.module';
import { ReportsModule } from './reports/reports.module';
import { ReportRequestModule } from './report-request/report-request.module';
import { ItemExplorerModule } from './item-explorer/item-explorer.module';
import { ProxyPatternModule } from './proxy-pattern/proxy-pattern.module';
import { ExceptionsModule } from '../../exceptions/exceptions.module';
import { FlexibleExceptionFilter } from '../../filters/flexible-exception.filter';
import { BundleModule } from './bundle/bundle.module';

@Module({
  imports: [
    ExceptionsModule,
    ItemsModule,
    GroupItemModule,
    StoreModule,
    StoreAdjustmentsModule,
    InventoryJournalModule,
    PhysicalInventoryModule,
    UnitModule,
    InventorySnapshotModule,
    PhysicalInventoryPostingModule,
    TransactionsModule,
    UpdateFeaturesCostModule,
    ReportsModule,
    ReportRequestModule,
    ItemExplorerModule,
    ProxyPatternModule,
    BundleModule,
    RouterModule.register([
      { path: 'inventory', module: ItemsModule },
      { path: 'inventory', module: GroupItemModule },
      { path: 'inventory', module: StoreModule },
      { path: 'inventory', module: StoreAdjustmentsModule },
      { path: 'inventory', module: InventoryJournalModule },
      { path: 'inventory', module: PhysicalInventoryModule },
      { path: 'inventory', module: UnitModule },
      { path: 'inventory', module: InventorySnapshotModule },
      { path: 'inventory', module: PhysicalInventoryPostingModule },
      { path: 'inventory', module: TransactionsModule },
      { path: 'inventory', module: UpdateFeaturesCostModule },
      { path: 'inventory', module: ReportsModule },
      { path: 'inventory', module: ReportRequestModule },
      { path: 'inventory', module: ItemExplorerModule },
      { path: 'inventory', module: BundleModule },
    ]),
  ],

  providers: [
    AppConfig,
    { provide: APP_FILTER, useClass: FlexibleExceptionFilter },
  ],
})
export class InventoryModule {}
