import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsIn,
  IsInt,
  Min,
  IsNotEmpty,
  IsEnum,
} from 'class-validator';
import { itemTypeEnum } from '../../inventory-item/enum/item-types.enum';

export class FindCommonDto {
  @ApiProperty({
    description: 'contact person name',
    example: '<PERSON>',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  sortBy? = 'number';

  @ApiProperty({
    description: 'contact person name',
    example: 'desc',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortType? = 'desc';
}

export class PaginationDto extends FindCommonDto {
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsNotEmpty()
  public page: number = 1;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsNotEmpty()
  public limit: number = 10;

  @IsString()
  @IsOptional()
  queries?: string;
}

export class GetGroupItemDto extends PartialType(PaginationDto) {
  @IsOptional()
  @IsEnum(itemTypeEnum)
  type?: itemTypeEnum;
}
