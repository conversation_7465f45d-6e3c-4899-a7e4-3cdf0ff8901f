import mongoose from 'mongoose';

import {
  Permission,
  Permission_Schema,
} from '../../../users/permissions/schema/permisstions.schema';
import { Role, Role_Schema } from '../../../users/roles/schemas/role.schema';

export default async function addBundlePermissions(db: mongoose.Connection) {
  const permissionModel = db.model(Permission.name, Permission_Schema);
  const roleModel = db.model(Role.name, Role_Schema);

  const newPermissions = [
    {
      name: 'bundle_create',
      privileges: [
        { action: 'create', subject: 'bundle' },
        { action: 'list', subject: 'abstract_items' },
        { action: 'list', subject: 'abstract_units' },
      ],
    },
    {
      name: 'bundle_list',
      privileges: [{ action: 'list', subject: 'bundle' }],
    },
    {
      name: 'bundle_details',
      privileges: [{ action: 'read', subject: 'bundle' }],
    },
    {
      name: 'bundle_update',
      privileges: [{ action: 'edit', subject: 'bundle' }],
    },
    {
      name: 'bundle_delete',
      privileges: [{ action: 'delete', subject: 'bundle' }],
    },
  ];
  const newPerms: any = await permissionModel.insertMany(newPermissions);
  const adminRole = await roleModel.findOne({ name: /admin/ });

  if (adminRole) {
    // Get current permissions
    const currentPermissions = adminRole.permissions || [];

    // Create a new array with both current permissions and new ones
    const updatedPermissions = [
      ...currentPermissions,
      ...newPerms.map((perm) => perm._id),
    ];

    // Update the role with the new permissions array
    await roleModel.updateOne(
      { _id: adminRole._id },
      { $set: { permissions: updatedPermissions } },
    );
  }
}
