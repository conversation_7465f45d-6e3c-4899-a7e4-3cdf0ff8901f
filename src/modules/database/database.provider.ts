import { Connection, Mongoose } from 'mongoose';

class DataBases extends Mongoose {
  private clientOption = {
    maxPoolSize: 10,
  };

  private databases: { [key: string]: Connection } = {};
  private getConnectionUri = (companyName = '') => companyName;

  public async getConnection(companyName = ''): Promise<Connection> {
    const connection = this.databases[companyName];
    return connection ? connection : await this.createDataBase(companyName);
  }

  private async createDataBase(companyName = ''): Promise<Connection> {
    const newConnection = this.createConnection(
      this.getConnectionUri(companyName),
      this.clientOption,
    );
    this.databases[companyName] = newConnection;
    return newConnection;
  }
}
export default new DataBases();
