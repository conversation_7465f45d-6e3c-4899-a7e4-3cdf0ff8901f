import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  MongooseOptionsFactory,
  MongooseModuleOptions,
} from '@nestjs/mongoose';

@Injectable()
export class CommonDbConfigService implements MongooseOptionsFactory {
  constructor(private readonly configService: ConfigService) {}

  createDbUrl(): string {
    const mongoDbUrl = new URL(
      `${this.configService.get('PREFIX_DB') || ''}common`,
      this.configService.get('DB_URL'),
    );
    mongoDbUrl.searchParams.set('authSource', 'admin');
    return mongoDbUrl.href;
  }

  createMongooseOptions(): MongooseModuleOptions {
    return {
      uri: this.createDbUrl(),
    };
  }
}
