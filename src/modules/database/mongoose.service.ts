import { Inject, Injectable, Scope } from '@nestjs/common';
import {
  MongooseOptionsFactory,
  MongooseModuleOptions,
} from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { ContextPayload } from '../auth/interfaces/context-payload.interface';
import { REQUEST } from '@nestjs/core';

@Injectable({ scope: Scope.REQUEST, durable: true })
export class MongooseConfigService implements MongooseOptionsFactory {
  constructor(
    @Inject(REQUEST) private readonly contextId: ContextPayload,
    private readonly configService: ConfigService,
  ) {}
  createDbUrl(database): string {
    console.log('tenant ID form mongoose config', database);

    const db = 'tenant_' + database;
    const mongoDbUrl = new URL(
      (this.configService.get('PREFIX_DB') || '') + db,
      this.configService.get('DB_URL'),
    );
    mongoDbUrl.searchParams.set('authSource', 'admin');

    console.log(mongoDbUrl.href);

    return mongoDbUrl.href;
  }

  async createMongooseOptions(): Promise<MongooseModuleOptions> {
    //TODO: get username and pass from MainDb
    console.log(this.contextId.tenantId);
    return {
      uri: this.createDbUrl(this.contextId.tenantId),
    };
  }
}
