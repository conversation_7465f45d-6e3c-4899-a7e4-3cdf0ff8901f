import { Injectable } from '@nestjs/common';
import { CaslAbilityFactory } from './casl-ability.factory';
import { CheckAbilityDto } from './dtos/check-ability-dto';

@Injectable()
export class CaslService {
  constructor(private readonly caslAbilityFactory: CaslAbilityFactory) {}

  public async checkAbilities(dto: CheckAbilityDto): Promise<boolean> {
    const ability = await this.caslAbilityFactory.createForUser(
      dto.user_id,
      dto.branch,
    );

    return dto.permissions.every((permission) =>
      ability.can(permission.action, permission.subject),
    );
  }
}
