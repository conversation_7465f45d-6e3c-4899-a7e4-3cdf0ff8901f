import { Global, Module } from '@nestjs/common';

import { CaslAbilityFactory } from './casl-ability.factory';
import { CaslGuard } from './guards/casl.guard';
import { UserBranchesRolesModule } from '../users/user-branches-roles/user-branch-role.module';
import { MongooseModule } from '@nestjs/mongoose';
import { User, User_Schema } from '../users/users/schemas/user.schema';
import { CaslService } from './casl.service';
import { CaslController } from './casl.controller';
import { FeatureAccessibilityGuard } from './guards/feature-accessibility.guard';
import { RpcModule } from '../rpc/rpc.module';

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: User_Schema }]),
    UserBranchesRolesModule,
    RpcModule,
  ],
  providers: [
    CaslGuard,
    CaslAbilityFactory,
    CaslService,
    FeatureAccessibilityGuard,
  ],
  controllers: [CaslController],
  exports: [CaslAbilityFactory, FeatureAccessibilityGuard],
})
export class CaslModule {}
