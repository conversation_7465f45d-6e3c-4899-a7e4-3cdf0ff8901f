import { <PERSON>, <PERSON> } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { usersMessagePattern } from '../../utils/queues.enum';
import { CaslService } from './casl.service';
import { CaslDto } from './dtos/check-ability-dto';
import { publicRpc } from '../auth/guards/public-event.decorator';

@ApiTags('permissions')
@Controller('permissions')
export class CaslController {
  constructor(private readonly caslService: CaslService) {}

  @Post('check')
  @publicRpc()
  @MessagePattern({ cmd: usersMessagePattern.check_abilities })
  async checkAbilities(
    @Payload()
    payload: CaslDto,
  ) {
    return await this.caslService.checkAbilities(payload.data);
  }
}
