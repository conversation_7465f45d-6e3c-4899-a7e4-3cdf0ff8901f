import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { TenantRpcService } from '../../rpc/tenant-rpc.service';

@Injectable()
export class FeatureAccessibilityGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    public readonly tenantRpcService: TenantRpcService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const feature: any = this.reflector.get<any[]>(
      'feature',
      context.getHandler(),
    );
    if (!feature) {
      return true;
    }

    const request = context.switchToHttp().getRequest<RequestWithUser>();

    return await this.tenantRpcService.checkFeatureAccessibility(
      request,
      feature.name,
    );
  }
}
