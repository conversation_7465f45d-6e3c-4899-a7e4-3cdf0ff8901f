import {
  CanActivate,
  ExecutionContext,
  HttpException,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CaslAbilityFactory } from '../casl-ability.factory';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import { nameOf } from '../../../utils/object-key-name';
import { generalExceptionCode } from '../../../exceptions/exception-code.general';

@Injectable()
export class CaslGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<any[]>(
      'abilities',
      context.getHandler(),
    );
    if (!requiredPermissions) {
      return true;
    }

    const request = context.switchToHttp().getRequest<RequestWithUser>();

    if (!request.headers['branch']) {
      throw new HttpException(
        {
          exceptionCode: nameOf(
            generalExceptionCode,
            (exception) => exception.branchNotFound,
          ),
        },
        generalExceptionCode.branchNotFound,
      );
    }
    const ability = await this.caslAbilityFactory.createForUser(
      request.user.user_id,
      request.headers['branch'],
    );

    return requiredPermissions.every((permission) =>
      ability.can(permission.action, permission.subject),
    );
  }
}
