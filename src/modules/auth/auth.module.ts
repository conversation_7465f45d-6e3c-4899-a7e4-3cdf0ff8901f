import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { BranchModule } from '../users/branch/branch.module';
import { AuthClass } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { DatabaseModule } from '../database/database.module';
import { UserBranchesRolesModule } from '../users/user-branches-roles/user-branch-role.module';
import { HeaderApiKeyStrategy } from './strategies/api-key.strategy';
import { ApiTokenModule } from '../api-token/api-token.module';
import { UserProfileModule } from '../users/users-profile/user-profile.module';
import { TenantModule } from '../users/tenants/tenant.module';
import { UserModule } from '../users/users/user.module';
import { RpcModule } from '../rpc/rpc.module';

@Module({
  imports: [
    BranchModule,
    UserModule,
    ApiTokenModule,
    PassportModule,
    DatabaseModule,
    UserProfileModule,
    TenantModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        return {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          privateKey: configService.get('JWT_PK'),
          publicKey: configService.get('JWT_PUBKEY'),
          signOptions: {
            expiresIn: `${configService.get('ACCESS_TOKEN_EXPIRE')}s`,
            issuer: 'authService',
            algorithm: 'RS256',
          },
        };
      },
    }),
    UserBranchesRolesModule,
    RpcModule,
  ],
  controllers: [AuthClass],
  providers: [AuthService, LocalStrategy, JwtStrategy, HeaderApiKeyStrategy],
  exports: [AuthService, PassportModule],
})
export class AuthModule {}
