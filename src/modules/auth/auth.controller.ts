import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { AuthService } from './auth.service';
import { ForgetPasswordDto, ValidateOtpDto } from './dto/forgot-password.dto';
import { OtpDto } from './dto/otp.dto';
import { ConfigService } from '@nestjs/config';
import { publicRoute } from '../casl/guards/public-route.decorator';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ServiceAuthenticationResponse } from './interfaces/serviceAuthentication.interface';
import { nameOf } from '../../utils/object-key-name';
import { erpExceptionCode } from '../../exceptions/exception-code.erp';
import { skipSchemaTransaction } from '../postgres-database/schema-transaction.decorator';

@ApiTags('Auth')
@Controller('users')
export class AuthClass {
  constructor(
    private authService: AuthService,
    private configService: ConfigService,
  ) {}

  @skipSchemaTransaction()
  @Get('logout')
  @publicRoute()
  logout(@Res({ passthrough: true }) response: Response) {
    this.authService.logOut(response);
    return true;
  }

  @skipSchemaTransaction()
  @Post('forget-password')
  @publicRoute()
  @ApiOkResponse({
    type: Boolean,
  })
  async forgetPassword(@Body() body: ForgetPasswordDto) {
    const rs = await this.authService.forgetPassword(body);
    if (!rs) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.invalidOtp),
        erpExceptionCode.invalidOtp,
      );
    }
    return rs;
  }

  @skipSchemaTransaction()
  @Post('request-otp')
  @publicRoute()
  async requestOtp(@Body() body: OtpDto) {
    await this.authService.generateOtp(body.email);
    return 'if you enter information correctly you should recived otp';
  }

  @Post('validate-otp')
  @publicRoute()
  validateOtp(@Body() body: ValidateOtpDto) {
    return this.authService.validateOtp(body);
  }

  @ApiOkResponse({
    type: Boolean,
  })
  @Post('refresh-token')
  refreshToken() {
    return true;
  }

  @Post('service-auth')
  @publicRoute()
  async serviceAuthentication(
    @Body() body,
  ): Promise<ServiceAuthenticationResponse> {
    return await this.authService.authenticateServiceUser(body);
  }
}
