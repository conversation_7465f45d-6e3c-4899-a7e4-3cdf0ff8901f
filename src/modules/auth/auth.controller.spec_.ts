// eslint-disable-next-line filenames/match-regex
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { MongooseModule } from '@nestjs/mongoose';
import { Test } from '@nestjs/testing';
import { Tenant, Tenant_Schema } from '../users/tenants/schemas/tenant.schema';
import { TenantModule } from '../users/tenants/tenant.module';
import { TenantServices } from '../users/tenants/tenant.service';
import { UserModule } from '../users/users/user.module';
import { UsersService } from '../users/users/users.service';
import { AuthClass } from './auth.controller';
import { AuthService } from './auth.service';
import { TenantServiceMock, UserServiceMock } from './auth.service.spec';
import { DatabaseModule } from '../database/database.module';

describe('AuthController', () => {
  beforeEach(async () => {
    await Test.createTestingModule({
      imports: [
        JwtModule,
        ConfigModule,
        UserModule,
        TenantModule,
        DatabaseModule,
        MongooseModule.forRoot('mongodb://localhost:27017/test', {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          connectionName: 'main',
        }),
        MongooseModule.forRoot('mongodb://localhost:27017/test'),
        MongooseModule.forFeature([
          { name: Tenant.name, schema: Tenant_Schema },
        ]),
        ClientsModule.registerAsync([
          {
            name: 'NOTIFICATION_SERVICE',
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
              transport: Transport.RMQ,
              options: {
                queue: 'notifications_queue',
                urls: [configService.get<string>('RABBIT_MQ')],
                queueOptions: { durable: false },
              },
            }),
            inject: [ConfigService],
          },
        ]),
      ],
      controllers: [AuthClass],
      providers: [
        AuthService,

        { provide: UsersService, useClass: UserServiceMock },
        {
          provide: TenantServices,
          useClass: TenantServiceMock,
        },
      ],
    }).compile();
  });
});
