import { ExecutionContext, HttpException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { generalExceptionCode } from '../../../exceptions/exception-code.general';
import { nameOf } from '../../../utils/object-key-name';
import { Observable } from 'rxjs';
import { authTypeName } from '../../casl/guards/auth-type.decorator';

@Injectable({})
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private readonly reflector: Reflector) {
    super(reflector);
  }
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.get<boolean>(
      'isPublic',
      context.getHandler(),
    );
    const ispublicRpc = this.reflector.get<boolean>(
      'ispublicRpc',
      context.getHandler(),
    );

    if (isPublic || ispublicRpc) {
      return true;
    }
    const authType = this.reflector.get<string>(
      authTypeName,
      context.getHandler(),
    );
    if (authType == 'api') {
      return false; // Skip JWT if API key is used
    }

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: any, status: any) {
    if (!user) {
      throw new HttpException(
        nameOf(generalExceptionCode, (exception) => exception.unauthorized),
        generalExceptionCode.unauthorized,
      );
    }
    return super.handleRequest(err, user, info, context, status);
  }
}
