import { HttpException, Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { HeaderAPIKeyStrategy } from 'passport-headerapikey';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import * as jwt from 'jsonwebtoken';
import { generalExceptionCode } from 'src/exceptions/exception-code.general';
import { nameOf } from '../../../utils/object-key-name';
import { UserRpcService } from '../../rpc/user-rpc.service';

@Injectable()
export class HeaderApiKeyStrategy extends PassportStrategy(
  HeaderAPIKeyStrategy,
  'api-key',
) {
  constructor(private readonly userRpcService: UserRpcService) {
    super(
      {
        header: 'authorization',
        prefix: 'Bearer ',
      },
      true,
    );
  }

  async validate(
    apiKey: string,
    done: (error: Error | null, user?: any, info?: any) => void,
    req: RequestWithUser,
  ): Promise<any> {
    try {
      const user = jwt.decode(apiKey) as any;
      if (!user) {
        throw new HttpException(
          nameOf(generalExceptionCode, (x) => x.unauthorized),
          generalExceptionCode.unauthorized,
        );
      }
      const rs = await this.userRpcService.validateToken(
        user.code,
        user.user_id,
        apiKey,
      );

      const jwtDec = apiKey.split('.');

      const jwtStr = `${jwtDec[0]}.${jwtDec[1]}.`;
      if (rs?.api_token !== jwtStr) {
        throw new HttpException(
          nameOf(generalExceptionCode, (x) => x.unauthorized),
          generalExceptionCode.unauthorized,
        );
      }
      req.user = user;

      return done(null, user);
    } catch (error) {
      //dont touch this plz
      done(error);
    }
  }
}
