import { HttpException, Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-jwt';
import { cookieOptionsCreator } from '../constants';
import { verify, sign } from 'jsonwebtoken';
import { ContextIdFactory, ModuleRef } from '@nestjs/core';
import { Request } from 'express';
import { AuthService } from '../auth.service';
import { ConfigService } from '@nestjs/config';
import { nameOf } from '../../../utils/object-key-name';
import { erpExceptionCode } from '../../../exceptions/exception-code.erp';
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  domain: string;
  env: string;
  servicePath: string;

  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly configService: ConfigService,
  ) {
    super({
      jwtFromRequest: JwtStrategy.extractJWT,
      passReqToCallback: true,
      passResToCallback: true,
      ignoreExpiration: true,
      algorithm: ['RS256'],
      secretOrKey: process.env.JWT_PUBKEY,
    });
    this.domain = this.configService.get('BASE_URL')[0];
    this.env = this.configService.get('NODE_ENV');
    this.servicePath = this.configService.get('APP_URL_PREFIX');
  }

  private static extractJWT(req: Request): string | null {
    return req.cookies.access_token || null;
  }

  async validate(req: Request, payload: any) {
    try {
      const contextId = ContextIdFactory.getByRequest(req);

      const accessToken = req.cookies.access_token;
      const refreshToken = req.cookies.refresh_token;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { code, user_id, tenant_user } = payload as any;
      try {
        verify(accessToken, this.configService.get('JWT_PUBKEY'));

        return payload;
      } catch (error) {
        const authService = await this.moduleRef.resolve(
          AuthService,
          contextId,
        );
        if (refreshToken) {
          try {
            const userData = await authService.getOneById(user_id, true);

            if (!userData) {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.tenantDisabled),
                erpExceptionCode.tenantDisabled,
              );
            }

            const tenantData = (
              await authService.getOneByEmailTenant(userData.email)
            ).tenant;

            if (tenantData.activation_status === false) {
              throw new HttpException(
                nameOf(erpExceptionCode, (x) => x.tenantDisabled),
                erpExceptionCode.tenantDisabled,
              );
            }

            const jwtData = (await verify(
              refreshToken,
              userData.secret,
            )) as any;

            const branch = await authService.getBranches();
            const newToken = sign(
              {
                tenant_user,
                user_id,
                code,
                branch,
              },
              this.configService.get('JWT_PK'),
              {
                algorithm: 'RS256',
                expiresIn: `${this.configService.get('ACCESS_TOKEN_EXPIRE')}s`,
                issuer: 'authService',
              },
            );
            req.res.cookie(
              'access_token',
              newToken,
              cookieOptionsCreator(this.env, this.domain, true),
            );

            await authService.updateUserLastActivity(user_id);

            return jwtData;
          } catch (refreshError) {
            req.res.clearCookie(
              'access_token',
              cookieOptionsCreator(this.env, this.domain, true),
            );
            req.res.clearCookie(
              'refresh_token',
              cookieOptionsCreator(
                this.env,
                this.domain,
                true,
                this.servicePath,
              ),
            );
            req.res.clearCookie(
              'user',
              cookieOptionsCreator(this.env, this.domain, false),
            );
            return undefined;
          }
        }
      }
    } catch (error) {
      req.res.clearCookie(
        'access_token',
        cookieOptionsCreator(this.env, this.domain, true),
      );
      req.res.clearCookie(
        'refresh_token',
        cookieOptionsCreator(this.env, this.domain, true, this.servicePath),
      );
      req.res.clearCookie(
        'user',
        cookieOptionsCreator(this.env, this.domain, false),
      );
      return undefined;
    }
  }
}
