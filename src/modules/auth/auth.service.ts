import { HttpException, Inject, Injectable } from '@nestjs/common';
import { UsersService } from '../users/users/users.service';
import { JwtService } from '@nestjs/jwt';
import { User } from '../users/users/schemas/user.schema';
import { ForgetPasswordDto, ValidateOtpDto } from './dto/forgot-password.dto';
import { LoginDto } from './dto/login.dto';
import { Response } from 'express';
import { TenantServices } from '../users/tenants/tenant.service';
import { ConfigService } from '@nestjs/config';
import { cookieOptionsCreator } from '../auth/constants';
import { userCriticalProps } from '../users/users/schemas/enums';
import { BranchService } from '../users/branch/branch.service';
import { Connection, Types } from 'mongoose';
import { MongooseConfigService } from '../database/mongoose.service';
import DataBases from '../database/database.provider';
import { nameOf } from '../../utils/object-key-name';
import { erpExceptionCode } from '../../exceptions/exception-code.erp';
import { generalExceptionCode } from '../../exceptions/exception-code.general';
import { RequestWithUser, UserCustom } from './interfaces/authorize.interface';
import * as jwt from 'jsonwebtoken';
import { parseCookie } from './strategies/aggregate-by-tenant-context-id.strategy';
import { UserBranchesRolesService } from '../users/user-branches-roles/user-branches-roles.service';
import { Branch } from '../users/branch/schemas/branch.schema';
import { NotificationRpcService } from '../rpc/notification-rpc.service';
import { generateToken } from '../../utils/refresh-token';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
    private tenantService: TenantServices,
    private jwtService: JwtService,
    private notificationRpcService: NotificationRpcService,
    @Inject(BranchService) private branchService: BranchService,
    @Inject(MongooseConfigService)
    private readonly mongooseConfigService: MongooseConfigService,
    private readonly userBranchesRolesService: UserBranchesRolesService,
  ) {}

  async getOneById(_id, allow = false): Promise<User> {
    const userCriticalPropsarr = [];
    if (allow) {
      userCriticalPropsarr.push(userCriticalProps.secret);
    }
    return await this.usersService.findOneUser({ _id }, userCriticalPropsarr);
  }

  async getOneByEmail(email: string): Promise<UserCustom> {
    const data = await this.tenantService.getTenantUser({ user_email: email });
    if (!data) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.userNotFound),
        erpExceptionCode.userNotFound,
      );
    }

    const userData = await this.usersService.findOneUser(
      { email },
      [userCriticalProps.password, userCriticalProps.secret],
      'role',
    );

    const response = userData as any;
    return response;
  }

  async updateUserLastActivity(id: Types.ObjectId) {
    const user = await this.usersService.findOneUser({ _id: id });
    user.last_activity = new Date();
    await user.save();
  }

  async getOneByEmailTenant(email: string): Promise<any> {
    const data = await this.tenantService.getTenantUser({ user_email: email });
    if (!data) {
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.userNotFound),
        erpExceptionCode.userNotFound,
      );
    }
    return data;
  }

  async getOneByEmailWithConnection(
    email: string,
    connection: Connection,
  ): Promise<UserCustom> {
    const userData = await this.usersService.findOneWithCustomConnection(
      { email },
      [userCriticalProps.password, userCriticalProps.secret],
      connection,
    );
    return userData as any;
  }

  async generateOtp(email: string): Promise<boolean> {
    const data = await this.tenantService.getTenantUser({ user_email: email });
    if (!data) {
      return false;
    }
    const dbConnection = await this.newDbConnection(data.tenant.code);
    const userData = await this.getOneByEmailWithConnection(
      email,
      dbConnection,
    );
    if (userData) {
      userData.otp = {
        value: String(Math.floor(100000 + Math.random() * 900000)),
        period: 12,
      };
      await userData.save();
      await this.notificationRpcService.sendOtpMail(email, userData);
      return true;
    }
    return false;
  }

  async forgetPassword(body: ForgetPasswordDto): Promise<boolean> {
    const { email, password, otp } = body;
    const data = await this.tenantService.getTenantUser({ user_email: email });

    const dbConnection = await this.newDbConnection(data.tenant.code);
    const userData = await this.getOneByEmailWithConnection(
      email,
      dbConnection,
    );
    if (userData.otp) {
      if (userData.otp?.retries > 5) {
        userData.otp = undefined;
        await userData.save();
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.otpExpired),
          erpExceptionCode.otpExpired,
        );
      }
      if (userData.otp?.value == otp) {
        userData.password = password;
        userData.otp = undefined;
        await userData.save();
        return true;
      } else {
        userData.otp.retries = (userData.otp.retries || 0) + 1;
        await userData.save();
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.invalidOtp),
          erpExceptionCode.invalidOtp,
        );
      }
    }
    throw new HttpException(
      nameOf(erpExceptionCode, (x) => x.invalidOtp),
      erpExceptionCode.invalidOtp,
    );
  }

  async verify(request: RequestWithUser) {
    let userId = null,
      userObj = null,
      branches = null;
    if (
      request.headers &&
      parseCookie(request.headers['cookie'])?.access_token
    ) {
      const accessToken: any = jwt.decode(
        parseCookie(request.headers['cookie']).access_token,
      );
      if (accessToken) {
        userId = accessToken.user_id;
      }
    }
    const user = await this.usersService.findOneUser({ _id: userId });
    if (user) {
      userObj = user.toObject();
      userObj.branches = await this.userBranchesRolesService.getByUser(
        new Types.ObjectId(user._id as string),
      );
      branches = userObj.branches.map((branch) => ({
        _id: branch._id,
        name: branch.general_information.name,
      }));
    } else {
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.unauthorized),
        generalExceptionCode.unauthorized,
      );
    }

    return { user_obj: userObj, branches };
  }

  async validateOtp(body: ValidateOtpDto): Promise<boolean | User> {
    const { email, otp } = body;
    const data = await this.tenantService.getTenantUser({ user_email: email });
    if (!data) {
      return false;
    }
    const dbConnection = await this.newDbConnection(data.tenant.code);
    const userData = await this.getOneByEmailWithConnection(
      email,
      dbConnection,
    );
    if (userData.otp) {
      if (userData.otp?.retries > 5) {
        userData.otp = undefined;
        await userData.save();
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.otpExpired),
          erpExceptionCode.otpExpired,
        );
      }
      if (userData && userData.otp?.value == otp) {
        return userData;
      } else {
        userData.otp.retries = (userData.otp.retries || 0) + 1;
      }
      await userData.save();
      throw new HttpException(
        nameOf(erpExceptionCode, (x) => x.invalidOtp),
        erpExceptionCode.invalidOtp,
      );
    }
    throw new HttpException(
      nameOf(erpExceptionCode, (x) => x.invalidOtp),
      erpExceptionCode.invalidOtp,
    );
  }

  async tokenGenerator(
    user_email,
    userObj?: User,
    branch?: any[],
  ): Promise<{
    // the response of this function is token that we send to user
    // eslint-disable-next-line @typescript-eslint/naming-convention
    access_token: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    refresh_token: string;
  }> {
    const tenantUser = await this.tenantService.getTenantUser({ user_email });

    return {
      access_token: this.jwtService.sign({
        tenant_user: tenantUser._id,
        user_id: userObj._id,
        code: tenantUser.tenant.code,
        branch,
      }),
      refresh_token: generateToken(
        {
          tenant_user: tenantUser._id,
          user_id: userObj._id,
          code: tenantUser.tenant.code,
        },
        userObj.secret,
        this.configService.get('REFRESH_TOKEN_EXPIRE'),
      ),
    };
  }

  private async newDbConnection(code): Promise<Connection> {
    const liveConnections = await DataBases.getConnection(
      this.mongooseConfigService.createDbUrl(code),
    );
    return liveConnections;
  }

  async login(userCriditionals: LoginDto): Promise<{
    // the response of this function is token that we send to user
    // eslint-disable-next-line @typescript-eslint/naming-convention
    access_token: string;
    // the response of this function is token that we send to user
    // eslint-disable-next-line @typescript-eslint/naming-convention
    refresh_token: string;
    // the response of this function is token that we send to user
    // eslint-disable-next-line @typescript-eslint/naming-convention
    user_obj: User;
    branch: Branch[];
  }> {
    try {
      const data = await this.tenantService.getTenantUser({
        user_email: userCriditionals.email,
      });

      if (!data) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.userNotFound),
          erpExceptionCode.userNotFound,
        );
      }
      const dbConnection = await this.newDbConnection(data.tenant.code);
      let user = await this.getOneByEmailWithConnection(
        userCriditionals.email,
        dbConnection,
      );

      if (!user)
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.userNotFound),
          erpExceptionCode.userNotFound,
        );
      user.tenant = data.tenant;

      if (user.tenant.activation_status === false) {
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.tenantNotFound),
          erpExceptionCode.tenantNotFound,
        );
      }
      if (!user.active)
        throw new HttpException(
          nameOf(erpExceptionCode, (x) => x.accountNotFound),
          erpExceptionCode.accountNotFound,
        );
      const rs = await user.comparePassword(userCriditionals.password);

      if (rs) {
        user = user.toObject();
        const branchData =
          await this.branchService.localFindAllRawWithConnection(
            {},
            dbConnection,
          );
        const token = await this.tokenGenerator(user.email, user, branchData);
        return { ...token, user_obj: user, branch: branchData };
      }
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.unauthorized),
        generalExceptionCode.unauthorized,
      );
    } catch (error) {
      console.log(error);
      throw new HttpException(
        nameOf(generalExceptionCode, (x) => x.incorrectCredentials),
        generalExceptionCode.incorrectCredentials,
      );
    }
  }

  async logOut(response: Response) {
    const domain = this.configService.get('BASE_URL')[0];
    const env = this.configService.get('NODE_ENV');
    const servicePath = this.configService.get('APP_URL_PREFIX');
    response.clearCookie(
      'access_token',
      cookieOptionsCreator(env, domain, true),
    );
    response.clearCookie('user', cookieOptionsCreator(env, domain, false));
    response.clearCookie(
      'refresh_token',
      cookieOptionsCreator(env, domain, true, servicePath),
    );
  }

  async getBranches(): Promise<any> {
    return await this.branchService.localFindAllRaw({});
  }

  /**
   * gets user id,role, permission and create a token for it
   */
  async authenticateServiceUser(body: {
    code: number;
    userId: Types.ObjectId;
  }): Promise<{ access: 'denied' | 'allowed' }> {
    const { userId } = body;

    const user = await this.usersService.findOneUser(
      {
        _id: userId,
      },
      [],
    );
    if (user) {
      return { access: 'allowed' };
    }
    return { access: 'denied' };
  }
}
