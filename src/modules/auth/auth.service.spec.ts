import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { BranchService } from '../users/branch/branch.service';
import { TenantServices } from '../users/tenants/tenant.service';
import { UsersService } from '../users/users/users.service';
import { AuthService } from './auth.service';
import * as dotenv from 'dotenv';
import { DatabaseModule } from '../database/database.module';
import { UserBranchesRolesService } from '../users/user-branches-roles/user-branches-roles.service';
import { notificationClientMock } from '../__mocks__/services/notification-rpc.service';
import { NotificationRpcService } from '../rpc/notification-rpc.service';
dotenv.config();

export class User {
  email = '<EMAIL>';
  password = 'a123456789';
  code = '0';
  comparePassword() {
    return true;
  }
  toObject() {
    return {
      _id: 0,
      email: this.email,
      password: this.password,
    };
  }
}
export class UserServiceMock {
  findOne() {
    return new User();
  }
}
export class TenantServiceMock {
  getTenantUser() {
    return {
      _id: '635faa55b35bf7260aa7ddd8',
      tenant: { code: '0' },
    };
  }
}
export class RbacServiceMock {
  getOneRole() {
    return {
      _id: '0',
      tenant: { code: '0' },
    };
  }
}
export class branchServiceMock {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  _findAllRaw() {
    return {
      _id: '0',
      tenant: { code: '0' },
    };
  }
}
export class userBranchRoleServiceMock {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  _findAllRaw() {
    return {
      _id: '0',
      tenant: { code: '0' },
    };
  }
}

describe('AuthService', () => {
  let service: AuthService;
  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule,
        DatabaseModule,
        JwtModule.registerAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: async (configService: ConfigService) => {
            return {
              privateKey: configService.get('JWT_PK'),
              publicKey: configService.get('JWT_PUBKEY'),
              signOptions: {
                expiresIn: `${configService.get('ACCESS_TOKEN_EXPIRE')}s`,
                issuer: 'authService',
                algorithm: 'RS256',
              },
            };
          },
        }),
      ],
      providers: [
        AuthService,
        {
          provide: UsersService,
          useClass: UserServiceMock,
        },
        {
          provide: TenantServices,
          useClass: TenantServiceMock,
        },
        {
          provide: BranchService,
          useClass: branchServiceMock,
        },
        {
          provide: NotificationRpcService,
          useValue: notificationClientMock(),
        },
        {
          provide: UserBranchesRolesService,
          useClass: userBranchRoleServiceMock,
        },
        // LocalStrategy,
        // JwtStrategy,
      ],
    }).compile();
    service = moduleRef.get<AuthService>(AuthService);
  });

  beforeEach(async () => {});

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  it('should generate token', async () => {
    // const loginSpy = jest.spyOn(controller, 'login');
    // const login = await controller.login(
    //   {
    //     email: '<EMAIL>',
    //     password: 'test123456789',
    //   },
    //   null,
    //   null,
    // );
    // //console.log(
    //   '%cauth.service.spec.ts line:87 login',
    //   'color: #007acc;',
    //   login,
    // );
  });
});

// describe('validateUser', () => {
//   let service: AuthService;

//   beforeEach(async () => {
//     const moduleRef: TestingModule = await Test.createTestingModule({
//       imports: [
//         UsersModule,
//         PassportModule,
//         JwtModule.register({
//           secret: jwtConstants.secret,
//           signOptions: { expiresIn: '60s' },
//         }),
//       ],
//       providers: [AuthService, LocalStrategy, JwtStrategy],
//     }).compile();

//     service = moduleRef.get<AuthService>(AuthService);
//   });

//   it('should return a user object when credentials are valid', async () => {
//     const res = await service.login({ email: 'maria', password: 'guess' });
//     // expect(res.userId).toEqual(3);
//   });

//   it('should return null when credentials are invalid', async () => {
//     const res = await service.login({ email: 'xxx', password: 'xxx' });
//     expect(res).toBeNull();
//   });
// });

// describe('validateLogin', () => {
//   let service: AuthService;

//   beforeEach(async () => {
//     const moduleRef: TestingModule = await Test.createTestingModule({
//       imports: [
//         UsersModule,
//         PassportModule,
//         JwtModule.register({
//           secret: jwtConstants.secret,
//           signOptions: { expiresIn: '60s' },
//         }),
//       ],
//       providers: [AuthService, LocalStrategy, JwtStrategy],
//     }).compile();

//     service = moduleRef.get<AuthService>(AuthService);
//   });

//   it('should return JWT object when credentials are valid', async () => {
//     const res = await service.login({ email: 'maria', password: '3' });
//     expect(res.access_token).toBeDefined();
//   });
// });
