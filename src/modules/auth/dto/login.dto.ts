import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class LoginDto {
  @Transform(({ value }: { value: string }) => value.toLowerCase())
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'emial of the user',
    example: '<EMAIL>',
    required: true,
  })
  public email: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'password of the user',
    example: 'a123456789',
    required: true,
  })
  public password: string;
}
