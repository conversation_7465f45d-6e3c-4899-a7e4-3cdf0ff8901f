import { Types } from 'mongoose';
import { User } from '../../users/users/schemas/user.schema';
import { Tenant } from '../../users/tenants/schemas';
import { level, pricingLevel } from '../../../utils/enums';

type CompanyType = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  average_cost_level: level;
  depth: number;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  displayed_decimals: number;
  isMultiBranch: boolean;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  pricing_level: pricingLevel;
};

export interface RequestWithUser extends Request {
  user: User & {
    token: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    user_id: Types.ObjectId;
    code: number;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    branch: [{ _id: string; name: string; general_information: any }];
  };
  tenantId: number;
  companySettings: CompanyType;
  settings: any[];
  policyData: any;
  branch?: Types.ObjectId;
}

export interface UserCustom extends User {
  tenant: Tenant;
}
