import { Module } from '@nestjs/common';
import { ApiTokenService } from './api-token.service';
import { MongooseModule } from '@nestjs/mongoose';
import { ApiToken, apiTokenSchema } from './schema/api-token.schema';
import { TenantModule } from '../users/tenants/tenant.module';
import { UserModule } from '../users/users/user.module';

@Module({
  imports: [
    TenantModule,
    UserModule,
    MongooseModule.forFeature([
      { name: ApiToken.name, schema: apiTokenSchema },
    ]),
  ],
  providers: [ApiTokenService],
  exports: [ApiTokenService],
})
export class ApiTokenModule {}
