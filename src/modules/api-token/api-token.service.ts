import { Injectable } from '@nestjs/common';
import { generateApiToken } from '../..//utils/refresh-token';
import { Model, Types } from 'mongoose';
import { TenantServices } from '../users/tenants/tenant.service';
import { UsersService } from '../users/users/users.service';
import { InjectModel } from '@nestjs/mongoose';
import { ApiToken } from './schema/api-token.schema';
import { CreateApiTokenDto } from '../users/users-profile/dto/create-api-token.dto';

@Injectable()
export class ApiTokenService {
  constructor(
    private readonly tenantService: TenantServices,
    private readonly usersService: UsersService,
    @InjectModel(ApiToken.name) private readonly apiTokenModel: Model<ApiToken>,
  ) {}
  async createApiToken(
    _id: Types.ObjectId,
    createApiTokenDto: CreateApiTokenDto,
  ) {
    const userDoc = await this.usersService.findOneLimited(_id);
    const tenantUser = await this.tenantService.getTenantUser({
      user_email: userDoc.email,
    });
    const apiToken = generateApiToken(
      {
        user_id: userDoc._id,
        code: tenantUser.tenant.code,
        api_token: true,
      },
      31536000, // a year
      // '365d' this.configService.get('REFRESH_TOKEN_EXPIRE'),
    );
    const rs = await this.apiTokenModel.create({
      api_token: apiToken,
      user: userDoc._id,
      ...createApiTokenDto,
    });

    return {
      api_token: `${apiToken}${rs.api_id}`,
      user: userDoc._id,
      ...createApiTokenDto,
    };
  }
  async revoke(_id: Types.ObjectId, user: Types.ObjectId) {
    await this.apiTokenModel.findOneAndDelete({ _id: _id, user });
  }

  async findOne(api_id: string, user: Types.ObjectId) {
    const rs = await this.apiTokenModel
      .findOne({ user, api_id })
      .select('api_token');

    return rs;
  }

  async find(user: Types.ObjectId) {
    return await this.apiTokenModel.find({ user: new Types.ObjectId(user) });
  }
}
