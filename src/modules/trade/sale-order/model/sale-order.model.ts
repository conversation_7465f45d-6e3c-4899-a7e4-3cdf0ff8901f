import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';
import { PartnerModel } from '../../partners/model/partner.model';
import { StoreModel } from '../../../inventory/store/model/store.model';
import { SalesRepresentativeModel } from '../../sales-representative/model/sales-representative.model';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { TransactionItemModel } from './transaction-item.model';
import { SalePaymentTypesModel } from './payment-type.model';

@Table({
  tableName: 'sale_orders',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class SaleOrderModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  invoice_no: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  invoice_type: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  registered_customer: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  free_tax: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_name_ar: string;

  @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  customer_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tax_no: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  invoice_date: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  expiration_date: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  due_date: Date;

  @ForeignKey(() => StoreModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  store_id: number;

  @ForeignKey(() => SalesRepresentativeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  sales_representative_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  note: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  reference: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  is_percentage_discount: boolean;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_discount_percentage: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_discount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  tax_amount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total_discounts: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total_vat: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_total: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  item_total: number;

  // TODO: handle relations
  // payment_types: PaymentTypes[];

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  paid: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  qr_code: string;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  branch_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  status: string;

  // TODO: handle transactions
  // transactions: TransactionItem[];

  // TODO: handle accounting journal
  // accounting_journal: Array<Types.ObjectId | string>;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @HasMany(() => TransactionItemModel, 'sale_order_id')
  transactionItems: TransactionItemModel[];

  @HasMany(() => SalePaymentTypesModel, 'sale_order_id')
  paymentTypes: SalePaymentTypesModel[];

  @BelongsTo(() => PartnerModel, 'customer_id')
  customer: PartnerModel;

  @BelongsTo(() => StoreModel, 'store_id')
  store: StoreModel;

  @BelongsTo(() => SalesRepresentativeModel, 'sales_representative_id')
  salesRepresentative: SalesRepresentativeModel;

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;
}
