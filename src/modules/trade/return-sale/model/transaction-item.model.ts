import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ItemModel } from '../../../inventory/inventory-item/model/item.model';
import { UnitModel } from '../../../inventory/unit/model/unit.model';
import { SaleTransactionItemModel as SaleTransactionItemModel } from '../../sale/model/transaction-item.model';
import { SaleReturnModel } from './sale-return.model';

@Table({
  tableName: 'sale_return_transaction_items',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class SaleReturnTransactionItemModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => SaleTransactionItemModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  transaction_reference_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  transaction_reference: string;

  @ForeignKey(() => ItemModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  item_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  item_invoice_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  item_type: string;

  @ForeignKey(() => UnitModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  unit_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  free_tax: boolean;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  sold_qty: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  qty: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  price: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  subtotal: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  discount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  vat: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  row_invoice_discount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  row_invoice_discount_vat: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  average_cost: number;

  @ForeignKey(() => SaleReturnModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  sale_return_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @BelongsTo(() => SaleReturnModel, 'sale_return_id')
  sale: SaleReturnModel;

  @BelongsTo(() => ItemModel, 'item_id')
  item: ItemModel;

  @BelongsTo(() => UnitModel, 'unit_id')
  unit: UnitModel;

  @BelongsTo(() => SaleTransactionItemModel, 'transaction_reference_id')
  saleTransactionItem: SaleTransactionItemModel;
}
