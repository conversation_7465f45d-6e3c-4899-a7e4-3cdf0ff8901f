import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';
import { PartnerModel } from '../../partners/model/partner.model';
import { StoreModel } from '../../../inventory/store/model/store.model';
import { SalesRepresentativeModel } from '../../sales-representative/model/sales-representative.model';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { SaleModel } from '../../sale/model/sale.model';
import { PaymentTypeModel } from '../../../users/payment-types/model/payment-type.model';
import { SaleReturnTransactionItemModel } from './transaction-item.model';

@Table({
  tableName: 'sale_returns',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class SaleReturnModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  invoice_no: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  invoice_type: string;

  @ForeignKey(() => PaymentTypeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  payment_type_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  registered_customer: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  free_tax: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_name_ar: string;

  @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  customer_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_phone: string;

  @ForeignKey(() => SaleModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  sale_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  sale_number: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tax_no: string;

  @ForeignKey(() => StoreModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  store_id: number;

  @ForeignKey(() => SalesRepresentativeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  sales_representative_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  note: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total_discounts: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total_vat: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_total: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  qr_code: string;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  branch_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @HasMany(() => SaleReturnTransactionItemModel, 'sale_return_id')
  transactionItems: SaleReturnTransactionItemModel[];

  @BelongsTo(() => SaleModel, 'sale_id')
  sale: SaleModel;

  @BelongsTo(() => PartnerModel, 'customer_id')
  customer: PartnerModel;

  @BelongsTo(() => StoreModel, 'store_id')
  store: StoreModel;

  @BelongsTo(() => SalesRepresentativeModel, 'sales_representative_id')
  salesRepresentative: SalesRepresentativeModel;

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;
}
