import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ItemModel } from '../../../inventory/inventory-item/model/item.model';
import { UnitModel } from '../../../inventory/unit/model/unit.model';
import { PurchaseTransactionItemModel as PurchaseTransactionItemModel } from '../../purchase/model/transaction-item.model';
import { PurchaseReturnModel } from './purchase-return.model';

@Table({
  tableName: 'purchase_return_transaction_items',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class PurchaseReturnTransactionItemModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => PurchaseTransactionItemModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  transaction_reference_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  transaction_reference: string;

  @ForeignKey(() => ItemModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  item_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  item_invoice_name: string;

  @ForeignKey(() => UnitModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  unit_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  free_tax: boolean;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  bought_qty: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_qty: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  description: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  price: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_subtotal: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_discount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_vat: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_total: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_row_invoice_discount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_row_invoice_discount_vat: number;

  @ForeignKey(() => PurchaseReturnModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  purchase_return_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @BelongsTo(() => PurchaseReturnModel, 'purchase_return_id')
  purchaseReturn: PurchaseReturnModel;

  @BelongsTo(() => PurchaseTransactionItemModel, 'transaction_reference_id')
  purchaseTransactionItem: PurchaseTransactionItemModel;

  @BelongsTo(() => ItemModel, 'item_id')
  item: ItemModel;

  @BelongsTo(() => UnitModel, 'unit_id')
  unit: UnitModel;
}
