import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';
import { PartnerModel } from '../../partners/model/partner.model';
import { StoreModel } from '../../../inventory/store/model/store.model';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { PaymentTypeModel } from '../../../users/payment-types/model/payment-type.model';
import { PurchaseModel } from '../../purchase/model/purchase.model';
import { PurchaseReturnTransactionItemModel } from './transaction-item.model';

@Table({
  tableName: 'purchase_returns',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class PurchaseReturnModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  invoice_no: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  invoice_type: string;

  @ForeignKey(() => PaymentTypeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  payment_type_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  registered_vendor: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  free_tax: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  vendor_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  vendor_name_ar: string;

  @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  vendor_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  vendor_phone: string;

  @ForeignKey(() => PurchaseModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  purchase_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  purchase_number: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tax_no: string;

  @ForeignKey(() => StoreModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  store_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  note: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_total_discounts: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_total_vat: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  return_invoice_total: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  item_total: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  qr_code: string;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  branch_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @HasMany(() => PurchaseReturnTransactionItemModel, 'purchase_return_id')
  transactionItems: PurchaseReturnTransactionItemModel[];

  @BelongsTo(() => PurchaseModel, 'purchase_id')
  purchase: PurchaseModel;

  @BelongsTo(() => PartnerModel, 'vendor_id')
  vendor: PartnerModel;

  @BelongsTo(() => StoreModel, 'store_id')
  store: StoreModel;

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;

  @BelongsTo(() => PaymentTypeModel, 'payment_type_id')
  paymentType: PaymentTypeModel;
}
