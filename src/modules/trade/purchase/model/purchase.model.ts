import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';
import { PartnerModel } from '../../partners/model/partner.model';
import { StoreModel } from '../../../inventory/store/model/store.model';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { PaymentTypeModel } from '../../../users/payment-types/model/payment-type.model';
import { PurchaseTransactionItemModel } from './transaction-item.model';
import { PurchaseAdditionalExpensesModel } from './additional-expenses.model';

@Table({
  tableName: 'purchases',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class PurchaseModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  invoice_no: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  invoice_type: string;

  @ForeignKey(() => PaymentTypeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  payment_type_id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  registered_vendor: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  free_tax: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  vendor_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  vendor_name_ar: string;

  @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  vendor_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  vendor_phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tax_no: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  invoice_date: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  due_date: Date;

  @ForeignKey(() => StoreModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  store_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  note: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  reference: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_discount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total_discounts: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total_vat: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_total: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  item_total: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  paid: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  qr_code: string;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  branch_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @HasMany(() => PurchaseTransactionItemModel, 'purchase_id')
  transactionItems: PurchaseTransactionItemModel[];

  @HasMany(() => PurchaseAdditionalExpensesModel, 'purchase_id')
  additionalExpenses: PurchaseAdditionalExpensesModel[];

  @BelongsTo(() => PartnerModel, 'vendor_id')
  vendor: PartnerModel;

  @BelongsTo(() => StoreModel, 'store_id')
  store: StoreModel;

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;

  @BelongsTo(() => PaymentTypeModel, 'payment_type_id')
  paymentType: PaymentTypeModel;
}
