import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ExpenseModel } from '../../expense/model/expense.model';
import { AccountingNodeModel } from '../../../accounting/accounting-node/model/accounting-node.model';
import { PurchaseModel } from './purchase.model';

@Table({
  tableName: 'purchase_additional_expenses',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class PurchaseAdditionalExpensesModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => ExpenseModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  expense_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  amount: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  effect_on_cost: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  on_vendor: boolean;

  @ForeignKey(() => AccountingNodeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  custom_account_id: number;

  @ForeignKey(() => PurchaseModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  purchase_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @BelongsTo(() => PurchaseModel, 'purchase_id')
  purchase: PurchaseModel;

  @BelongsTo(() => AccountingNodeModel, 'custom_account_id')
  accountingNode: AccountingNodeModel;

  @BelongsTo(() => ExpenseModel, 'expense_id')
  expense: ExpenseModel;
}
