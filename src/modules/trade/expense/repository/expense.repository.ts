import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { ExpenseModel } from '../model/expense.model';

@Injectable()
export class ExpenseRepository extends BaseRepository<ExpenseModel> {
  constructor(@InjectModel(ExpenseModel) expenseModel: typeof ExpenseModel) {
    super(expenseModel);
  }

  public async getOneByMongoId(mongoId: string) {
    return await this.findOne(
      { mongo_id: mongoId },
      { attributes: ['id', 'mongo_id'], raw: true },
    );
  }
}
