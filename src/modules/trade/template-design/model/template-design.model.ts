import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({
  tableName: 'template_designs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class TemplateDesignModel extends Model {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  en: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  ar: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  type: string;

  // control options
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  invoice_title: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  tax_number: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  payment_method: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  store_address: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  vat: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  footer: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  footer_text: string;

  // design options
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  logo_in_background: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title_weight: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  text_align: string;

  // logo settings
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  align: string;

  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  logo_size: number;

  // send options
  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  email: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  sms: boolean;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;
}
