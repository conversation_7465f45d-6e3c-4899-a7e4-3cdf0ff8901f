import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { PaymentTypeModel } from '../../../users/payment-types/model/payment-type.model';
import { SaleModel } from './sale.model';

// This table is used to store the payment types used in a sale
@Table({
  tableName: 'sale_payment_types',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class SalePaymentTypesModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => PaymentTypeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  payment_type_id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  amount: number;

  @ForeignKey(() => SaleModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  sale_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @BelongsTo(() => SaleModel, 'sale_id')
  sale: SaleModel;

  @BelongsTo(() => PaymentTypeModel, 'payment_type_id')
  paymentType: PaymentTypeModel;
}
