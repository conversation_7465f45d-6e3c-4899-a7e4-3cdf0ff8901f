import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';
import { PartnerModel } from '../../partners/model/partner.model';
import { StoreModel } from '../../../inventory/store/model/store.model';
import { SalesRepresentativeModel } from '../../sales-representative/model/sales-representative.model';
import { BranchModel } from '../../../users/branch/model/branch.model';
import { SaleOrderModel } from '../../sale-order/model/sale-order.model';
import { SaleTransactionItemModel } from './transaction-item.model';
import { SalePaymentTypesModel } from './payment-type.model';
import { SaleAdditionalExpensesModel } from './additional-expenses.model';
import { SaleReturnModel } from '../../return-sale/model/sale-return.model';

@Table({
  tableName: 'sales',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: true,
  deletedAt: 'deleted_at',
})
export class SaleModel extends Model {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  invoice_no: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  free_tax: boolean;

  @ForeignKey(() => SaleOrderModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  sale_order_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_name: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_name_ar: string;

  @ForeignKey(() => PartnerModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  customer_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  customer_phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  tax_no: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  invoice_date: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  due_date: Date;

  @ForeignKey(() => StoreModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
  })
  store_id: number;

  @ForeignKey(() => SalesRepresentativeModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  sales_representative_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  note: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  reference: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  is_percentage_discount: boolean;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_discount_percentage: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_discount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  tax_amount: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total_discounts: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  total_vat: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  invoice_total: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  item_total: number;

  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    defaultValue: 0,
  })
  paid: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  qr_code: string;

  @ForeignKey(() => BranchModel)
  @Column({
    type: DataType.BIGINT,
    allowNull: true,
  })
  branch_id: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  created_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
  })
  updated_at: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  deleted_at: Date;

  //TODO reomve once stop using mongo
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  mongo_id: string;

  // relations
  @HasMany(() => SaleTransactionItemModel, 'sale_id')
  transactionItems: SaleTransactionItemModel[];

  @HasMany(() => SalePaymentTypesModel, 'sale_id')
  paymentTypes: SalePaymentTypesModel[];

  @HasMany(() => SaleAdditionalExpensesModel, 'sale_id')
  additionalExpenses: SaleAdditionalExpensesModel[];

  @HasMany(() => SaleReturnModel, 'sale_id')
  saleReturn: SaleReturnModel[];

  @BelongsTo(() => PartnerModel, 'customer_id')
  customer: PartnerModel;

  @BelongsTo(() => StoreModel, 'store_id')
  store: StoreModel;

  @BelongsTo(() => SalesRepresentativeModel, 'sales_representative_id')
  salesRepresentative: SalesRepresentativeModel;

  @BelongsTo(() => BranchModel, 'branch_id')
  branch: BranchModel;
}
