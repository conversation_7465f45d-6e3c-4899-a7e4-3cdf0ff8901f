import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../../../../repositories/abstract.repository';
import { InjectModel } from '@nestjs/sequelize';
import { PartnerGroupModel } from '../model/partner-group.model';

@Injectable()
export class PartnerGroupRepository extends BaseRepository<PartnerGroupModel> {
  constructor(
    @InjectModel(PartnerGroupModel) partnerGroupModel: typeof PartnerGroupModel,
  ) {
    super(partnerGroupModel);
  }

  public async getOneByMongoId(mongoId: string) {
    return await this.findOne(
      { mongo_id: mongoId },
      { attributes: ['id', 'mongo_id'], raw: true },
    );
  }
}
