/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { SalesRepresentativeModel } from '../../trade/sales-representative/model/sales-representative.model';

export class CreateSalesRepresentativeTableMigration extends BaseTransactionMigration {
  name = 'CreateSalesRepresentativeTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(
      `Creating sales_representatives table for tenant ${tenantId}...`,
    );

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [
      SalesRepresentativeModel,
    ]);

    // Get attributes from the SalesRepresentativeModel
    const salesRepresentativesAttributes =
      SalesRepresentativeModel.getAttributes();

    // Create the sales_representatives table
    const salesRepresentativesTableDef = getTableDefinition(
      SalesRepresentativeModel,
      tenantId,
    );
    await queryInterface.createTable(
      salesRepresentativesTableDef,
      salesRepresentativesAttributes,
      {
        transaction,
      },
    );

    console.log(
      `Created sales_representatives table for tenant ${tenantId} using model definition`,
    );

    // Add index for sales_representatives
    await queryInterface.addIndex(salesRepresentativesTableDef, ['number'], {
      unique: true,
      name: 'sales_representatives_number_idx',
      transaction,
    });

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(
      `Dropping sales_representatives table for tenant ${tenantId}...`,
    );

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [
      SalesRepresentativeModel,
    ]);

    // Drop table
    try {
      // First try with the table definition from model

      const salesRepresentativesTableDef = getTableDefinition(
        SalesRepresentativeModel,
        tenantId,
      );
      await queryInterface.dropTable(salesRepresentativesTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for sales_representatives');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."sales_representatives" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
