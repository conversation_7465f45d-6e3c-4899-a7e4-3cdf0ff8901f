/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { PartnerModel } from '../../trade/partners/model/partner.model';

export class CreatePartnerTableMigration extends BaseTransactionMigration {
  name = 'CreatePartnerTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating partners table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [PartnerModel]);

    // Get attributes from the SalesRepresentativeModel
    const partnersAttributes = PartnerModel.getAttributes();

    // Create the partners table
    const partnersTableDef = getTableDefinition(PartnerModel, tenantId);
    await queryInterface.createTable(partnersTableDef, partnersAttributes, {
      transaction,
    });

    console.log(
      `Created partners table for tenant ${tenantId} using model definition`,
    );

    // Add index for partners
    await queryInterface.addIndex(partnersTableDef, ['number'], {
      unique: true,
      name: 'partners_number_idx',
      transaction,
    });

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping partners table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [PartnerModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const salesRepresentativesTableDef = getTableDefinition(
        PartnerModel,
        tenantId,
      );
      await queryInterface.dropTable(salesRepresentativesTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for partners');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."partners" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
