/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BundleModel } from '../../inventory/bundle/model/bundle.model';
import { BaseTransactionMigration } from './base-transaction-migration';

export class CreateBundleTableMigration extends BaseTransactionMigration {
  name = 'CreateBundleTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating bundles table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [BundleModel]);

    // Get attributes from the BundleModel
    const bundleAttributes = BundleModel.getAttributes();

    // Create the bundles table
    const bundleTableDef = getTableDefinition(BundleModel, tenantId);
    await queryInterface.createTable(bundleTableDef, bundleAttributes, {
      transaction,
    });

    console.log(
      `Created bundles table for tenant ${tenantId} using model definition`,
    );

    // Add indexes for bundles
    await queryInterface.addIndex(bundleTableDef, ['code'], {
      unique: true,
      name: 'bundle_code_idx',
      transaction,
    });

    console.log(
      `Bundle table created successfully for tenant ${tenantId} with indexes and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping bundles table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [BundleModel]);

    // Drop table
    try {
      // First try with the table definition from model
      const bundleTableDef = getTableDefinition(BundleModel, tenantId);
      await queryInterface.dropTable(bundleTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for bundles');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."bundles" CASCADE`,
        { transaction },
      );
    }

    console.log(`Bundle table dropped successfully for tenant ${tenantId}`);
  }
}
