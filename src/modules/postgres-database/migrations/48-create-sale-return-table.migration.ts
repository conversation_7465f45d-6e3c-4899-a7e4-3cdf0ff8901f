/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { SaleReturnModel } from '../../trade/return-sale/model/sale-return.model';

export class CreateSaleReturnsTableMigration extends BaseTransactionMigration {
  name = 'CreateSaleReturnsTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating sale_returns table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [
      SaleReturnModel,
    ]);

    // Get attributes from the SaleReturnModel
    const saleReturnsAttributes = SaleReturnModel.getAttributes();

    // Create the sale_returns table
    const saleReturnsTableDef = getTableDefinition(SaleReturnModel, tenantId);
    await queryInterface.createTable(
      saleReturnsTableDef,
      saleReturnsAttributes,
      {
        transaction,
      },
    );

    console.log(
      `Created sale_returns table for tenant ${tenantId} using model definition`,
    );

    // Add index for sale_returns
    await queryInterface.addIndex(
      saleReturnsTableDef,
      ['branch_id', 'invoice_no'],
      {
        unique: true,
        name: 'sale_returns_branch_id_invoice_no_idx',
        transaction,
      },
    );

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping sale_returns table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [SaleReturnModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const saleReturnsTableDef = getTableDefinition(SaleReturnModel, tenantId);
      await queryInterface.dropTable(saleReturnsTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for sale_returns');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."sale_returns" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
