/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { SaleOrderModel } from '../../trade/sale-order/model/sale-order.model';

export class CreateSaleOrdersTableMigration extends BaseTransactionMigration {
  name = 'CreateSaleOrdersTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating sale_orders table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [SaleOrderModel]);

    // Get attributes from the SalesRepresentativeModel
    const saleOrdersAttributes = SaleOrderModel.getAttributes();

    // Create the sale_orders table
    const saleOrdersTableDef = getTableDefinition(SaleOrderModel, tenantId);
    await queryInterface.createTable(saleOrdersTableDef, saleOrdersAttributes, {
      transaction,
    });

    console.log(
      `Created sale_orders table for tenant ${tenantId} using model definition`,
    );

    // Add index for sale_orders
    await queryInterface.addIndex(
      saleOrdersTableDef,
      ['branch_id', 'invoice_no'],
      {
        unique: true,
        name: 'sale_orders_branch_id_invoice_no_idx',
        transaction,
      },
    );

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping sale_orders table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [SaleOrderModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const salesOrdersTableDef = getTableDefinition(SaleOrderModel, tenantId);
      await queryInterface.dropTable(salesOrdersTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for sale_orders');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."sale_orders" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
