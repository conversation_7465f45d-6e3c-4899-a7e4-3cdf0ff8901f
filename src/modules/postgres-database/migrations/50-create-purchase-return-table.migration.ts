/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { PurchaseReturnModel } from '../../trade/return-purchase/model/purchase-return.model';

export class CreatePurchaseReturnsTableMigration extends BaseTransactionMigration {
  name = 'CreatePurchaseReturnsTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating purchase_returns table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [
      PurchaseReturnModel,
    ]);

    // Get attributes from the PurchaseReturnModel
    const purchaseReturnsAttributes = PurchaseReturnModel.getAttributes();

    // Create the purchases table
    const purchaseReturnsTableDef = getTableDefinition(
      PurchaseReturnModel,
      tenantId,
    );
    await queryInterface.createTable(
      purchaseReturnsTableDef,
      purchaseReturnsAttributes,
      {
        transaction,
      },
    );

    console.log(
      `Created purchase_returns table for tenant ${tenantId} using model definition`,
    );

    // Add index for purchase_returns
    await queryInterface.addIndex(
      purchaseReturnsTableDef,
      ['branch_id', 'invoice_no'],
      {
        unique: true,
        name: 'purchase_returns_branch_id_invoice_no_idx',
        transaction,
      },
    );

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping purchase_returns table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [PurchaseReturnModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const purchaseReturnsTableDef = getTableDefinition(
        PurchaseReturnModel,
        tenantId,
      );
      await queryInterface.dropTable(purchaseReturnsTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for purchase_returns');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."purchase_returns" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
