/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { PurchaseModel } from '../../trade/purchase/model/purchase.model';

export class CreatePurchasesTableMigration extends BaseTransactionMigration {
  name = 'CreatePurchasesTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating purchases table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [PurchaseModel]);

    // Get attributes from the PurchaseModel
    const purchasesAttributes = PurchaseModel.getAttributes();

    // Create the purchases table
    const purchasesTableDef = getTableDefinition(PurchaseModel, tenantId);
    await queryInterface.createTable(purchasesTableDef, purchasesAttributes, {
      transaction,
    });

    console.log(
      `Created purchases table for tenant ${tenantId} using model definition`,
    );

    // Add index for purchases
    await queryInterface.addIndex(
      purchasesTableDef,
      ['branch_id', 'invoice_no'],
      {
        unique: true,
        name: 'purchases_branch_id_invoice_no_idx',
        transaction,
      },
    );

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping purchases table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [PurchaseModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const purchasesTableDef = getTableDefinition(PurchaseModel, tenantId);
      await queryInterface.dropTable(purchasesTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for purchases');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."purchases" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
