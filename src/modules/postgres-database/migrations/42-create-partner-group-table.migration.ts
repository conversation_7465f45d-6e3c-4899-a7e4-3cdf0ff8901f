/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { PartnerGroupModel } from '../../trade/partner-group/model/partner-group.model';

export class CreatePartnerGroupTableMigration extends BaseTransactionMigration {
  name = 'CreatePartnerGroupTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating partner_groups table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [
      PartnerGroupModel,
    ]);

    // Get attributes from the partnerGroupModel
    const partnerGroupsAttributes = PartnerGroupModel.getAttributes();

    // Create the partner_groups table
    const partnerGroupsTableDef = getTableDefinition(
      PartnerGroupModel,
      tenantId,
    );
    await queryInterface.createTable(
      partnerGroupsTableDef,
      partnerGroupsAttributes,
      {
        transaction,
      },
    );

    console.log(
      `Created partner_groups table for tenant ${tenantId} using model definition`,
    );

    // Add index for partner_groups
    await queryInterface.addIndex(partnerGroupsTableDef, ['number'], {
      unique: true,
      name: 'partner_groups_number_idx',
      transaction,
    });

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping partner_groups table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [PartnerGroupModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const partnerGroupTableDef = getTableDefinition(
        PartnerGroupModel,
        tenantId,
      );
      await queryInterface.dropTable(partnerGroupTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for partner_groups');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."partner_groups" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
