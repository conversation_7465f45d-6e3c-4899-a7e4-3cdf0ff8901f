/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BundleItemModel } from '../../inventory/bundle/model/bundle-item.model';
import { BundleModel } from '../../inventory/bundle/model/bundle.model';
import { ItemModel } from '../../inventory/inventory-item/model/item.model';
import { UnitModel } from '../../inventory/unit/model/unit.model';
import { BaseTransactionMigration } from './base-transaction-migration';

export class CreateBundleItemTableMigration extends BaseTransactionMigration {
  name = 'CreateBundleItemTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating bundle_items table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [
      BundleItemModel,
    ]);

    // Get attributes from the BundleItemModel
    const bundleItemAttributes = BundleItemModel.getAttributes();

    // Create the bundle_items table
    const bundleItemTableDef = getTableDefinition(BundleItemModel, tenantId);
    await queryInterface.createTable(
      bundleItemTableDef,
      bundleItemAttributes,
      { transaction },
    );

    console.log(
      `Created bundle_items table for tenant ${tenantId} using model definition`,
    );

    // Add indexes for bundle_items
    await queryInterface.addIndex(
      bundleItemTableDef,
      ['bundle_id', 'item_id'],
      {
        unique: true,
        name: 'bundle_items_bundle_item_idx',
        transaction,
      },
    );

    await queryInterface.addIndex(bundleItemTableDef, ['bundle_id'], {
      name: 'bundle_items_bundle_idx',
      transaction,
    });

    await queryInterface.addIndex(bundleItemTableDef, ['item_id'], {
      name: 'bundle_items_item_idx',
      transaction,
    });

    await queryInterface.addIndex(bundleItemTableDef, ['unit_id'], {
      name: 'bundle_items_unit_idx',
      transaction,
    });

    console.log(
      `Bundle items table created successfully for tenant ${tenantId} with indexes and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping bundle_items table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [BundleItemModel]);

    // Drop table
    try {
      // First try with the table definition from model
      const bundleItemTableDef = getTableDefinition(BundleItemModel, tenantId);
      await queryInterface.dropTable(bundleItemTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for bundle_items');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."bundle_items" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Bundle items table dropped successfully for tenant ${tenantId}`,
    );
  }
}
