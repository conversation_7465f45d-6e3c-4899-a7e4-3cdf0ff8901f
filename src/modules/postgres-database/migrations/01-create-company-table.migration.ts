/* eslint-disable */
import {
  initializeModelsAndCheckTables,
  initializeModelsForDown,
  getTableDefinition,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { setTenantSchema, resetToPublicSchema } from '../schema.utils';
import {
  CompanyModel,
  companyTableName,
} from '../../../modules/users/company/model/company.model';
import { BaseTransactionMigration } from './base-transaction-migration';
import { Transaction } from 'sequelize';
import {
  ContactPersonModel,
  contactPersonModelTableName,
} from '../../../modules/shared/model/contact-person.model';
import {
  NationalAddressModel,
  nationalAddressModelTableName,
} from '../../../modules/shared/model/national-address.model';
export class CreateCompanyTablesMigration extends BaseTransactionMigration {
  name = 'CreateCompanyTablesMigration';

  async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    try {
      // Set the schema context to the tenant's schema
      await setTenantSchema(sequelize, tenantId);

      console.log(
        `Creating companyModel , ContactPersonModel ,NationalAddressModel tables for tenant ${tenantId}...`,
      );

      // Get the query interface
      const queryInterface = sequelize.getQueryInterface();

      // Initialize models and check if tables exist
      await initializeModelsAndCheckTables(sequelize, tenantId, [
        CompanyModel,
        ContactPersonModel,
        NationalAddressModel,
      ]);
      const companyAddressModelTable = getTableDefinition(
        NationalAddressModel,
        tenantId,
      );
      await queryInterface.createTable(
        // eslint-disable-next-line @typescript-eslint/naming-convention
        companyAddressModelTable,
        NationalAddressModel.getAttributes(),
        { transaction },
      );
      console.log(
        `companyAddressModelTable table created successfully for tenant ${tenantId}`,
      );
      const contactPersonModelTable = getTableDefinition(
        ContactPersonModel,
        tenantId,
      );
      await queryInterface.createTable(
        // eslint-disable-next-line @typescript-eslint/naming-convention
        contactPersonModelTable,
        ContactPersonModel.getAttributes(),
        { transaction },
      );
      console.log(
        `ContactPersonModel table created successfully for tenant ${tenantId}`,
      );
      // Create the invoices table with queryInterface
      const companyTableDef = getTableDefinition(CompanyModel, tenantId);
      await queryInterface.createTable(
        // eslint-disable-next-line @typescript-eslint/naming-convention
        companyTableDef,
        CompanyModel.getAttributes(),
        { transaction },
      );

      console.log(`company table created successfully for tenant ${tenantId}`);

      // Reset to public schema
      await resetToPublicSchema(sequelize);
    } catch (error) {
      console.error(`Error creating tables for tenant ${tenantId}:`, error);

      // Make sure to reset to public schema
      await resetToPublicSchema(sequelize);

      throw error;
    }
  }

  async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    try {
      // Set the schema context to the tenant's schema
      await setTenantSchema(sequelize, tenantId);

      console.log(
        `Dropping companyModel ,NationalAddressModel  and ContactPersonModel tables for tenant ${tenantId}...`,
      );

      // Get the query interface
      const queryInterface = sequelize.getQueryInterface();

      // Initialize models for DOWN migration (handles relation errors gracefully)
      await initializeModelsForDown(sequelize, tenantId, [
        CompanyModel,
        NationalAddressModel,
        ContactPersonModel,
      ]);

      // Drop tables in reverse order due to foreign key constraints
      try {
        // First try with the table definition from model
        const companyTableDef = getTableDefinition(CompanyModel, tenantId);
        await queryInterface.dropTable(companyTableDef, {
          cascade: true,
          transaction,
        });
      } catch (error) {
        console.log('Falling back to old format for company');
        // Fall back to the old format
        await sequelize.query(
          `DROP TABLE IF EXISTS "tenant_${tenantId}"."${companyTableName}" CASCADE`,
        );
      }
      try {
        // First try with the table definition from model
        const companyContactTableDef = getTableDefinition(
          ContactPersonModel,
          tenantId,
        );
        await queryInterface.dropTable(companyContactTableDef, {
          cascade: true,
          transaction,
        });
      } catch (error) {
        console.log('Falling back to old format for company_contact_persons');
        // Fall back to the old format
        await sequelize.query(
          `DROP TABLE IF EXISTS "tenant_${tenantId}"."${contactPersonModelTableName}" CASCADE`,
        );
      }
      try {
        // First try with the table definition from model
        const nationalAddressTableDef = getTableDefinition(
          NationalAddressModel,
          tenantId,
        );
        await queryInterface.dropTable(nationalAddressTableDef, {
          cascade: true,
          transaction,
        });
      } catch (error) {
        console.log('Falling back to old format for company_address');
        // Fall back to the old format
        await sequelize.query(
          `DROP TABLE IF EXISTS "tenant_${tenantId}"."${nationalAddressModelTableName}" CASCADE`,
        );
      }

      console.log(`Tables dropped successfully for tenant ${tenantId}`);

      // Reset to public schema
      await resetToPublicSchema(sequelize);
    } catch (error) {
      console.error(`Error dropping tables for tenant ${tenantId}:`, error);

      // Make sure to reset to public schema
      await resetToPublicSchema(sequelize);

      throw error;
    }
  }
}
