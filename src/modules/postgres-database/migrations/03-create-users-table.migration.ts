/* eslint-disable */
import {
  initializeModelsAndCheckTables,
  getTableDefinition,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { setTenantSchema, resetToPublicSchema } from '../schema.utils';
import { UserModel, usersTableName } from '../../users/users/model/users.model';
import {
  UserOtpModel,
  usersOtpTableName,
} from '../../users/users/model/user-otp.model';
import { BaseTransactionMigration } from './base-transaction-migration';
import { Transaction } from 'sequelize';
export class CreateUsersTablesMigration extends BaseTransactionMigration {
  name = 'CreateUsersTablesMigration';

  async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    try {
      // Set the schema context to the tenant's schema
      await setTenantSchema(sequelize, tenantId);

      console.log(`Creating users , otp tables for tenant ${tenantId}...`);

      // Get the query interface
      const queryInterface = sequelize.getQueryInterface();

      // Initialize models and check if tables exist
      await initializeModelsAndCheckTables(sequelize, tenantId, [
        UserModel,
        UserOtpModel,
      ]);

      // Create the invoices table with queryInterface
      const userTableDef = getTableDefinition(UserModel, tenantId);
      await queryInterface.createTable(
        // eslint-disable-next-line @typescript-eslint/naming-convention
        userTableDef,
        UserModel.getAttributes(),
        { transaction },
      );

      console.log(
        `user table created successfully for tenant ${tenantId} with index`,
      );

      const userOtp = getTableDefinition(UserOtpModel, tenantId);
      await queryInterface.createTable(
        // eslint-disable-next-line @typescript-eslint/naming-convention
        userOtp,
        UserOtpModel.getAttributes(),
        { transaction },
      );

      console.log(`user otp table created successfully for tenant ${tenantId}`);

      // Reset to public schema
      await resetToPublicSchema(sequelize);
    } catch (error) {
      console.error(`Error creating tables for tenant ${tenantId}:`, error);

      // Make sure to reset to public schema
      await resetToPublicSchema(sequelize);

      throw error;
    }
  }

  async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    try {
      // Set the schema context to the tenant's schema
      await setTenantSchema(sequelize, tenantId);

      console.log(
        `Dropping UserModel and invoices UserOtpModel for tenant ${tenantId}...`,
      );

      // Get the query interface
      const queryInterface = sequelize.getQueryInterface();

      // Initialize models to ensure they're registered with sequelize
      await initializeModelsForDown(sequelize, tenantId, [
        UserModel,
        UserOtpModel,
      ]);

      // Drop tables in reverse order due to foreign key constraints
      try {
        // First try with the table definition from model
        const transactionTableDef = getTableDefinition(UserOtpModel, tenantId);
        await queryInterface.dropTable(transactionTableDef, {
          cascade: true,
          transaction,
        });
      } catch (error) {
        console.log(`Falling back to old format for ${usersOtpTableName} `);
        // Fall back to the old format
        await sequelize.query(
          `DROP TABLE IF EXISTS "tenant_${tenantId}"."${usersOtpTableName}" CASCADE`,
        );
      }

      try {
        // First try with the table definition from model
        const invoiceTableDef = getTableDefinition(UserModel, tenantId);
        await queryInterface.dropTable(invoiceTableDef, {
          cascade: true,
          transaction,
        });
      } catch (error) {
        console.log('Falling back to old format for invoices');
        // Fall back to the old format
        await sequelize.query(
          `DROP TABLE IF EXISTS "tenant_${tenantId}"."${usersTableName}" CASCADE`,
        );
      }

      console.log(`Tables dropped successfully for tenant ${tenantId}`);

      // Reset to public schema
      await resetToPublicSchema(sequelize);
    } catch (error) {
      console.error(`Error dropping tables for tenant ${tenantId}:`, error);

      // Make sure to reset to public schema
      await resetToPublicSchema(sequelize);

      throw error;
    }
  }
}
