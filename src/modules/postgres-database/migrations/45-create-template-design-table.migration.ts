/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { TemplateDesignModel } from '../../trade/template-design/model/template-design.model';

export class CreateTemaplateDesignTableMigration extends BaseTransactionMigration {
  name = 'CreateTemaplateDesignTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating template_designs table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [
      TemplateDesignModel,
    ]);

    // Get attributes from the SalesRepresentativeModel
    const templateDesignAttributes = TemplateDesignModel.getAttributes();

    // Create the template_designs table
    const templateDesignTableDef = getTableDefinition(
      TemplateDesignModel,
      tenantId,
    );
    await queryInterface.createTable(
      templateDesignTableDef,
      templateDesignAttributes,
      {
        transaction,
      },
    );

    console.log(
      `Created template_designs table for tenant ${tenantId} using model definition`,
    );

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping template_designs table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [TemplateDesignModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const templateDesignTableDef = getTableDefinition(
        TemplateDesignModel,
        tenantId,
      );
      await queryInterface.dropTable(templateDesignTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for template_designs');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."template_designs" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
