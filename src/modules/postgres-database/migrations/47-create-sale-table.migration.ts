/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { SaleModel } from '../../trade/sale/model/sale.model';

export class CreateSalesTableMigration extends BaseTransactionMigration {
  name = 'CreateSalesTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating sales table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [SaleModel]);

    // Get attributes from the SaleModel
    const salesAttributes = SaleModel.getAttributes();

    // Create the sales table
    const salesTableDef = getTableDefinition(SaleModel, tenantId);
    await queryInterface.createTable(salesTableDef, salesAttributes, {
      transaction,
    });

    console.log(
      `Created sales table for tenant ${tenantId} using model definition`,
    );

    // Add index for sales
    await queryInterface.addIndex(salesTableDef, ['branch_id', 'invoice_no'], {
      unique: true,
      name: 'sales_branch_id_invoice_no_idx',
      transaction,
    });

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping sales table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [SaleModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const salesTableDef = getTableDefinition(SaleModel, tenantId);
      await queryInterface.dropTable(salesTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for sales');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."sales" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
