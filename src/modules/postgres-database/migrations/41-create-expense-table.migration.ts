/* eslint-disable */
import {
  getTableDefinition,
  initializeModelsAndCheckTables,
  initializeModelsForDown,
} from './migration.interface';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { BaseTransactionMigration } from './base-transaction-migration';
import { ExpenseModel } from '../../trade/expense/model/expense.model';

export class CreateExpenseTableMigration extends BaseTransactionMigration {
  name = 'CreateExpenseTableMigration';

  /**
   * Implementation of the migration's up logic with transaction
   */
  protected async upWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Creating expenses table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models and check if tables exist
    await initializeModelsAndCheckTables(sequelize, tenantId, [ExpenseModel]);

    // Get attributes from the ExpenseModel
    const ExpenseAttributes = ExpenseModel.getAttributes();

    // Create the expense table
    const expenseTableDef = getTableDefinition(ExpenseModel, tenantId);
    await queryInterface.createTable(expenseTableDef, ExpenseAttributes, {
      transaction,
    });

    console.log(
      `Created expenses table for tenant ${tenantId} using model definition`,
    );

    // Add index for expenses
    await queryInterface.addIndex(expenseTableDef, ['code'], {
      unique: true,
      name: 'expenses_code_idx',
      transaction,
    });

    console.log(
      `Item groups table created successfully for tenant ${tenantId} with index and constraints`,
    );
  }

  /**
   * Implementation of the migration's down logic with transaction
   */
  protected async downWithTransaction(
    sequelize: Sequelize,
    tenantId: number,
    transaction: Transaction,
  ): Promise<void> {
    console.log(`Dropping expenses table for tenant ${tenantId}...`);

    // Get the query interface
    const queryInterface = sequelize.getQueryInterface();

    // Initialize models for DOWN migration (handles relation errors gracefully)
    await initializeModelsForDown(sequelize, tenantId, [ExpenseModel]);

    // Drop table
    try {
      // First try with the table definition from model

      const expenseTableDef = getTableDefinition(ExpenseModel, tenantId);
      await queryInterface.dropTable(expenseTableDef, {
        cascade: true,
        transaction,
      });
    } catch (error) {
      console.log('Falling back to old format for expenses');
      // Fall back to the old format
      await sequelize.query(
        `DROP TABLE IF EXISTS "tenant_${tenantId}"."expenses" CASCADE`,
        { transaction },
      );
    }

    console.log(
      `Item groups table dropped successfully for tenant ${tenantId}`,
    );
  }
}
