export const branchesStub = () => {
  return [
    {
      _id: '64956ef5f506e9edf21df4e9',
      general_information: {
        code: '2',
        name: {
          en: 'Williamson LLC',
          ar: 'عاشور and Sons',
        },
        parent_branch: '',
        tax_code: '702860954818530',
        registration_number: 0,
        phone: '+966542580727',
        fax: '',
        email: '',
        address: '',
        activation_status: true,
        is_default: true,
        is_main_branch: false,
      },
      national_address: {
        trade_name: {
          en: 'Clothing',
          ar: 'إلكترونيات',
        },
        commercial_activities: 'Home',
        short_address: 'vZm',
        building_no: '2853',
        street: 'Reilly Manors',
        secondary_no: '535',
        district: 'Arizona',
        postal_code: '56318',
        city: 'Palmdale',
        governorate: 'Arkansas',
      },
      settings: {
        general: {
          allow_documents_deletion: true,
          allow_documents_edition: {
            enabled: true,
          },
          use_multi_stores: false,
        },
        global_accounts: {
          sales_accounts: {
            cash_sales_account: '6501b9f946ad8225ab76cbba',
            credit_sales_account: '6501b9f946ad8225ab76cbbb',
            cash_sales_return_account: '6501b9f946ad8225ab76cbbc',
            credit_sales_return_account: '6501b9f946ad8225ab76cbbd',
            sales_discount_account: '6501b9f946ad8225ab76cbbe',
          },
          purchase_accounts: {
            cash_purchase_account: '6501b9f946ad8225ab76cba9',
            credit_purchase_account: '6501b9f946ad8225ab76cbaa',
            cash_purchase_return_account: '6501b9f946ad8225ab76cbab',
            credit_purchase_return_account: '6501b9f946ad8225ab76cbac',
            purchase_discount_account: '6501b9f946ad8225ab76cbad',
            deferred_discount_account: '',
          },
          cash_and_bank: {
            cash_account: '6501b9f946ad8225ab76cb7f',
            bank_account: '6501b9f946ad8225ab76cb83',
            additional_expense_account: '',
            cash_invoices_under_settlement: '',
            profit_account: '6501b9f946ad8225ab76cba3',
          },
          sub_accounts: {
            cash_sales_commission_account: '',
            credit_sales_commission_account: '',
            levy_commission_account: '',
            customer_group_account: '6501b9f946ad8225ab76cb85',
            vendor_group_account: '6501b9f946ad8225ab76cb97',
          },
          tax_accounts: {
            purchase_tax_account: '6501b9f946ad8225ab76cb92',
            sales_tax_account: '6501b9f946ad8225ab76cb9e',
            payment_commission_tax_account: '6501b9f946ad8225ab76cb93',
          },
          other_accounts: {
            transfer_account: '',
            adjustment_account: '6501b9f946ad8225ab76cbb6',
            inventory_account: '6501b9f946ad8225ab76cb8e',
            beginning_balance_account: '',
            ending_balance_account: '',
            cost_of_sales_account: '6501b9f946ad8225ab76cbb5',
            cost_of_purchase_account: '',
          },
        },
        _id: '65f84bc2910e42d3f62dbeba',
      },
      document: {
        sales_invoices: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 100,
          default_unit_as_biggest: false,
        },
        sales_return: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 100,
          default_unit_as_biggest: false,
        },
        purchase_return: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        reservation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        preparation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        journal: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 100,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        payment_voucher_cash: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 6,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        receipt_voucher: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 6,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        debit_credit_memo: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 3,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        service_invoice_customer: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        service_invoice_vendors: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        purchase_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        store_transactions_and_adjustments: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        import_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        prepare_purchase_entry: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_receive: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation_order_and_comp: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        fiscal_inventory_voucher: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        fiscal_inventory_posting: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        sales_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        purchase_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        issue_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        addition_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        work_orders: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        entry_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        release_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        work_order_production: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_preparing_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        production_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation_request_p: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        cargo_manifest: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        cargo_manifest_invoice: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        purchases_invoices: {
          auto_serial: false,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
      },
      contact_person: {
        name: '',
        mobile: '',
        phone: '',
        email: '',
      },
      dates: {
        year_beginning_date: '',
        year_ending_date: '',
      },
      version: 412701,
      createdAt: '2024-03-18T14:12:18.122Z',
      updatedAt: '2024-03-19T23:37:34.215Z',
    },
  ];
};
