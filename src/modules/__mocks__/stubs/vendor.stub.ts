import { Types } from 'mongoose';

export const vendorStub = () => {
  return {
    _id: new Types.ObjectId('65f8425e79a87f3fe42a6f68'),
    name: { en: 'Senger LLC', ar: 'شنيق - النخلي' },
    group: {
      _id: '65c4df694154c4af160e375c',
      number: 1,
      name: { en: 'default vendor group', ar: 'مجموعة الموردين الافتراضية' },
      type: 'vendor',
      createdAt: '2024-03-18T13:32:14.582Z',
      updatedAt: '2024-03-18T13:32:14.582Z',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      __v: 0,
    },
    mobile: '+966527297737',
    tax_code: '269319743562635',
    email: '<EMAIL>',
    website: 'vicious-debris.org',
    national_address: {
      trade_name: { en: 'Music', ar: 'إلكترونيات' },
      commercial_activities: 'Industrial',
      short_address: 'tCd',
      building_no: '6540',
      street: 'Reichert Fields',
      secondary_no: '202',
      district: 'Hampshire',
      postal_code: '95275',
      city: 'Salt Lake City',
      governorate: 'Colorado',
    },
    is_customer_vendor: false,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    isActive: true,
    contact_person: {
      name: 'Sherry Cole IV',
      mobile: '+************',
      phone: '+************',
      email: '<EMAIL>',
    },
    type: 'vendor',
    accounting_info: {
      general_account: {
        _id: '6501b9f946ad8225ab76cb98',
        code: '*********',
        'name.ar': 'مصتع البلاستك العالمي',
        'name.en': 'International plastic factory',
        is_parent: false,
        active: true,
        hidden: false,
        balance_type: 'debit',
        group: [Object],
        type: 'bank_account',
        reporting_type: 'balance_sheet',
        static: true,
        invoice_party_type: 'vendor',
        balance: 60,
        currency: 'SAR',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        nodeQty: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        __v: 0,
        createdAt: '2024-03-15T13:44:34.650Z',
        updatedAt: '2024-03-19T16:18:20.238Z',
      },
      invoice_due_in: { type: 'to_end_of_month' },
      transactions: [[Object]],
    },
    cash_only: true,
    number: 1,
    createdAt: '2024-03-18T13:32:14.490Z',
    updatedAt: '2024-03-18T13:32:14.490Z',
    // eslint-disable-next-line @typescript-eslint/naming-convention
    __v: 0,
  } as any;
};
