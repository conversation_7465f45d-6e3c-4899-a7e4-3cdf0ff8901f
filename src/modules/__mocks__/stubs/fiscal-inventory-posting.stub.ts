import { PhysicalInventoryPostingDocument } from '../../inventory/physical-inventory-posting/schema/physical-inventory-posting.schema';

export const fiscalInventoryPostingStub =
  (): PhysicalInventoryPostingDocument => {
    return {
      _id: '64ad000ab16fbc375c9649de',
      name: {
        ar: 'المخزن الافتراضي',
        en: 'default store',
      },
      createdAt: '2024-02-27T11:58:55.486Z',
      updatedAt: '2024-02-27T11:58:55.486Z',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      __v: 0,
    } as any;
  };
