import { Voucher } from '../../accounting/voucher/schema/voucher.schema';
import { generalAccountingNodeStub } from './accounting-node.stub';
import { vendorStub } from './vendor.stub';
import { customerStub } from './customer.stub';

export const paymentVoucherStub = (): Voucher => {
  return {
    type: 'payment',
    number: 1,
    payment_type: '6501b9f946ad8225ab76cbad',
    debit_account: [
      {
        accounting_node: vendorStub()._id,
        amount: 123,
      },
    ],
    credit_account: [
      {
        accounting_node: generalAccountingNodeStub()._id,
        amount: 123,
      },
    ],
    amount: 123,
    branch: '64956ef5f506e9edf21df4e8',
    note: 'note',
    is_include_tax: false,
    settlement_type: 'on_account',
    reference: 'reference',
    invoice_party_type: 'vendor',
    discount: 2,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    printOptions: {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      templateLang: 'en',
      print: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      sendEmail: false,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      sendSms: false,
    },
  } as any;
};

export const receiptVoucherStub = (): Voucher => {
  return {
    type: 'receipt',
    number: 1,
    payment_type: '6501b9f946ad8225ab76cbad',
    debit_account: [
      {
        accounting_node: generalAccountingNodeStub()._id,
        amount: 123,
      },
    ],
    credit_account: [
      {
        accounting_node: customerStub()._id,
        amount: 123,
      },
    ],
    amount: 123,
    branch: '64956ef5f506e9edf21df4e8',
    note: 'note',
    settlement_type: 'on_account',
    reference: 'reference ',
    invoice_party_type: 'customer',
    sales_representative: '65e7039aa4d7a77ba53cb5aa',
    discount: 2,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    printOptions: {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      templateLang: 'en',
      print: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      sendEmail: false,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      sendSms: false,
    },
  } as any;
};

export const creditMemoVoucherStub = (): Voucher => {
  return {
    type: 'credit_memo',
    number: 1,
    credit_account: [
      {
        accounting_node: '6501b9f946ad8225ab76cb79',
        amount: 123,
        description: 'discription',
      },
    ],
    debit_account: [
      {
        accounting_node: '6501b9f946ad8225ab76cb78',
        amount: 123,
      },
    ],
    amount: 123,
    note: 'note',
    branch: '64956ef5f506e9edf21df4e8',
    reference: 'reference',
  } as any;
};

export const debitMemoVoucherStub = (): Voucher => {
  return {
    type: 'debit_memo',
    number: 1,
    credit_account: [
      {
        accounting_node: '6501b9f946ad8225ab76cb79',
        amount: 123,
        description: 'discription',
      },
    ],
    debit_account: [
      {
        accounting_node: '6501b9f946ad8225ab76cb78',
        amount: 123,
      },
    ],
    amount: 123,
    note: 'note',
    branch: '64956ef5f506e9edf21df4e8',
    reference: 'reference',
  } as any;
};
