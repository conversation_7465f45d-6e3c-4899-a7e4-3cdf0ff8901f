/* eslint-disable @typescript-eslint/naming-convention */
export const generalAccountingNodeStub = () => {
  return {
    _id: '6501b9f946ad8225ab76cb7f',
    code: '1020101',
    name: {
      ar: 'الصندوق الرئيسى',
      en: 'Main Cash Box',
    },
    is_parent: false,
    active: true,
    hidden: false,
    balance_type: 'debit',
    group: {
      _id: '6501b9f946ad8225ab76cb7e',
      code: '10201',
      'name.ar': 'النقديه بالصندوق',
      'name.en': 'Cash in Cash Box',
      is_parent: true,
      active: true,
      hidden: false,
      balance_type: 'debit',
      group: '6501b9f946ad8225ab76cb7d',
      type: 'cashier',
      reporting_type: 'balance_sheet',

      static: true,
      balance: 0,
      currency: 'SAR',
      nodeQty: 3,
      __v: 0,
      createdAt: '2024-03-19T13:53:42.692Z',
      updatedAt: '2024-03-19T13:53:42.692Z',
    },
    type: 'cashier',
    reporting_type: 'balance_sheet',

    static: true,
    balance: 113.2,
    currency: 'SAR',
    nodeQty: 0,
    __v: 0,
    createdAt: '2024-03-19T13:53:42.692Z',
    updatedAt: '2024-03-19T13:56:28.605Z',
  };
};
export const generalAccountingNodeStub2 = () => {
  return {
    _id: '6501b9f946ad8225ab76cb80',
    code: '1020102',
    name: {
      ar: 'صندوق الدولار',
      en: 'USD Cash Box',
    },
    is_parent: false,
    active: true,
    hidden: false,
    balance_type: 'debit',
    group: {
      _id: '6501b9f946ad8225ab76cb7e',
      code: '10201',
      'name.ar': 'النقديه بالصندوق',
      'name.en': 'Cash in Cash Box',
      is_parent: true,
      active: true,
      hidden: false,
      balance_type: 'debit',
      group: '6501b9f946ad8225ab76cb7d',
      type: 'cashier',
      reporting_type: 'balance_sheet',

      static: true,
      balance: 0,
      currency: 'SAR',
      nodeQty: 3,
      __v: 0,
      createdAt: '2024-03-19T13:53:42.692Z',
      updatedAt: '2024-03-19T13:53:42.692Z',
    },
    type: 'cashier',
    reporting_type: 'balance_sheet',

    static: true,
    balance: 0,
    currency: 'SAR',
    nodeQty: 0,
    __v: 0,
    createdAt: '2024-03-19T13:53:42.692Z',
    updatedAt: '2024-03-19T13:53:42.692Z',
  };
};

export const vendorAccountingNodeStub = () => {
  return {
    _id: '6501b9f946ad8225ab76cb98',
    code: '*********',
    'name.ar': 'مصتع البلاستك العالمي',
    'name.en': 'International plastic factory',
    is_parent: false,
    active: true,
    hidden: false,
    balance_type: 'debit',
    group: {
      _id: '6501b9f946ad8225ab76cb97',
      code: '20101',
      'name.ar': 'اجمالى الموردين',
      'name.en': 'Vendors Liabilities',
      is_parent: true,
      active: true,
      hidden: false,
      balance_type: 'debit',
      group: '6501b9f946ad8225ab76cb96',
      type: 'general',
      reporting_type: 'balance_sheet',

      static: true,
      balance: 0,
      currency: 'SAR',
      nodeQty: 0,
      __v: 0,
      createdAt: '2024-03-15T13:44:34.650Z',
      updatedAt: '2024-03-15T13:44:34.650Z',
    },
    type: 'bank_account',
    reporting_type: 'balance_sheet',

    static: true,
    invoice_party_type: 'vendor',
    balance: 90,
    currency: 'SAR',
    nodeQty: 0,
    __v: 0,
    createdAt: '2024-03-15T13:44:34.650Z',
    updatedAt: '2024-03-19T16:19:17.536Z',
  };
};

export const customerAccountingNodeStub = () => {
  return {
    _id: '6501b9f946ad8225ab76cb86',
    code: '*********',
    'name.ar': 'تموينات الخالدية',
    'name.en': 'Alkhaldieh Grocerey store',
    is_parent: false,
    active: true,
    hidden: false,
    balance_type: 'debit',
    group: {
      _id: '6501b9f946ad8225ab76cb85',
      code: '10203',
      'name.ar': 'الذمم المدينه -(العملاء)-',
      'name.en': 'Account Reciveable - Customers',
      is_parent: true,
      active: true,
      hidden: false,
      balance_type: 'debit',
      group: '6501b9f946ad8225ab76cb7d',
      type: 'party',
      reporting_type: 'balance_sheet',

      static: true,
      invoice_party_type: 'customer',
      balance: 0,
      currency: 'SAR',
      nodeQty: 0,
      __v: 0,
      createdAt: '2024-03-15T13:44:34.648Z',
      updatedAt: '2024-03-15T13:44:34.648Z',
    },
    type: 'bank_account',
    reporting_type: 'balance_sheet',

    static: true,
    invoice_party_type: 'customer',
    balance: -133,
    currency: 'SAR',
    nodeQty: 0,
    __v: 0,
    createdAt: '2024-03-15T13:44:34.648Z',
    updatedAt: '2024-03-19T16:14:47.066Z',
  };
};
