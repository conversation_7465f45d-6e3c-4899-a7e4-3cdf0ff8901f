/* eslint-disable @typescript-eslint/naming-convention */
export const receiptVoucherJournalStub = () => {
  return {
    _id: '65fb44c9722f6deb08d94eaf',
    transactions: [
      {
        code: '1020101',
        credit: 0,
        debit: 123,
        accountingNode: '6501b9f946ad8225ab76cb7f',
        by_user: '65128e387d6e377e37b40a6c',
        description: 'note',
        current_balance: 308,
        partner_id: null,
        partner_type: null,
        before_balance: 185,
      },
      {
        code: '*********',
        credit: 123,
        debit: 0,
        accountingNode: '6501b9f946ad8225ab76cb86',
        by_user: '65128e387d6e377e37b40a6c',
        description: 'note',
        current_balance: -398,
        partner_id: '65f8425e79a87f3fe42a6f3a',
        partner_type: 'customer',
        before_balance: -275,
      },
    ],
    type: 'receipt_voucher',
    note: 'note',
    by_user: '65128e387d6e377e37b40a6c',
    transaction_date: '2024-03-20T20:16:40.841Z',
    branch: '64956ef5f506e9edf21df4e8',
    createdAt: '2024-03-20T20:19:21.259Z',
    updatedAt: '2024-03-20T20:19:21.259Z',
    __v: 0,
  };
};
export const paymentVoucherJournalStub = () => {
  return {
    _id: '65fb5badad2c89d50c54c58f',
    transactions: [
      {
        code: '1020101',
        credit: 123,
        debit: 0,
        accountingNode: '6501b9f946ad8225ab76cb7f',
        by_user: '65128e387d6e377e37b40a6c',
        description: 'sdfsd',
        current_balance: 62,
        partner_id: null,
        partner_type: null,
        before_balance: 185,
      },
      {
        code: '*********',
        credit: 0,
        debit: 123,
        accountingNode: '6501b9f946ad8225ab76cb98',
        by_user: '65128e387d6e377e37b40a6c',
        description: 'sdfsd',
        current_balance: 378,
        partner_id: '65f8425e79a87f3fe42a6f68',
        partner_type: 'vendor',
        before_balance: 255,
      },
    ],
    type: 'payment_voucher',
    note: 'sdfsd',
    by_user: '65128e387d6e377e37b40a6c',
    transaction_date: '2024-03-20T20:46:53.125Z',
    branch: '64956ef5f506e9edf21df4e8',
    createdAt: '2024-03-20T21:57:02.020Z',
    updatedAt: '2024-03-20T21:57:02.020Z',
    __v: 0,
  };
};

export const paymentVoucherJournalWithTaxStub = () => {
  return {
    _id: '65fca6088cf5adb57d77c0c6',
    transactions: [
      {
        code: '1020101',
        credit: 123,
        debit: 0,
        accountingNode: '6501b9f946ad8225ab76cb7f',
        by_user: '65128e387d6e377e37b40a6c',
        description: 'sdfsd',
        current_balance: 62,
        partner_id: null,
        partner_type: null,
        before_balance: 185,
      },
      {
        code: '*********',
        credit: 0,
        debit: 123,
        accountingNode: '6501b9f946ad8225ab76cb98',
        by_user: '65128e387d6e377e37b40a6c',
        description: 'sdfsd',
        current_balance: 378,
        partner_id: '65f8425e79a87f3fe42a6f68',
        partner_type: 'vendor',
        before_balance: 255,
      },
      {
        code: '30104',
        credit: 0,
        debit: 16.**************,
        accountingNode: '6501b9f946ad8225ab76cb80',
        by_user: '65128e387d6e377e37b40a6c',
        description: 'note',
        current_balance: 32.**************,
        isTax: true,
        before_balance: 16.**************,
      },
    ],
    type: 'payment_voucher',
    note: 'note',
    by_user: '65128e387d6e377e37b40a6c',
    transaction_date: '2024-03-21T21:25:54.762Z',
    branch: '64956ef5f506e9edf21df4e8',
    createdAt: '2024-03-21T21:26:32.458Z',
    updatedAt: '2024-03-21T21:26:32.458Z',
    __v: 0,
  };
};

export const creditMemoJournalStub = () => {
  return {
    _id: '65fb5badad2c89d50c54c59d',
    transactions: [
      {
        code: '4010105',
        credit: 19,
        debit: 0,
        accountingNode: {
          _id: '6501b9f946ad8225ab76cbbe',
          code: '4010105',
          'name.ar': 'خصم مسموح به',
          'name.en': 'Allowed Discount',
          is_parent: false,
          active: true,
          hidden: false,
          balance_type: 'debit',
          group: '6501b9f946ad8225ab76cbb9',
          type: 'general',
          reporting_type: 'trade_account',

          static: true,
          balance: -43,
          currency: 'SAR',
          nodeQty: 0,
          __v: 0,
          createdAt: '2024-03-15T13:44:34.651Z',
          updatedAt: '2024-03-20T21:57:01.937Z',
        },
        by_user: '65128e387d6e377e37b40a6c',
        description: 'memo',
        current_balance: -43,
        partner_id: null,
        partner_type: null,
        before_balance: -24,
      },
      {
        code: 1,
        credit: 0,
        debit: 19,
        accountingNode: {
          _id: '6501b9f946ad8225ab76cb98',
          code: 1,
          'name.ar': 'شنيق - النخلي',
          'name.en': 'Senger LLC',
          is_parent: false,
          active: true,
          hidden: false,
          balance_type: 'debit',
          group: '6501b9f946ad8225ab76cb97',
          type: 'bank_account',
          reporting_type: 'balance_sheet',

          static: true,
          invoice_party_type: 'vendor',
          balance: 378,
          currency: 'SAR',
          nodeQty: 0,
          __v: 0,
          createdAt: '2024-03-15T13:44:34.650Z',
          updatedAt: '2024-03-20T21:57:02.017Z',
        },
        by_user: '65128e387d6e377e37b40a6c',
        description: 'memo',
        current_balance: 255,
        partner_id: '65f8425e79a87f3fe42a6f68',
        partner_type: 'vendor',
        before_balance: 236,
      },
    ],
    type: 'memo',
    note: 'Discount for Receipt Voucher #2 ',
    by_user: '65128e387d6e377e37b40a6c',
    transaction_date: '2024-03-20T20:46:53.125Z',
    branch: '64956ef5f506e9edf21df4e8',
    createdAt: '2024-03-20T21:57:01.952Z',
    updatedAt: '2024-03-20T21:57:01.952Z',
    __v: 0,
  };
};
