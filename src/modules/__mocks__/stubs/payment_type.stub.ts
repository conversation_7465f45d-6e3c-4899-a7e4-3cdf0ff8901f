import { PaymentTypesEnum } from '../../users/payment-types/types/payment-type.enum';
import { PaymentTypes } from '../../users/payment-types/schema/payment-type.schema';
import { Types } from 'mongoose';

export const paymentTypeStub = (): PaymentTypes => {
  return {
    _id: new Types.ObjectId('6501b9f946ad8225ab76cbad'),
    name: {
      en: 'other',
      ar: 'أخرى',
    },
    code: '4',
    type: PaymentTypesEnum.others,
  } as PaymentTypes;
};

export const paymentTypeDetailsStub = () => {
  return {
    _id: new Types.ObjectId('6501b9f946ad8225ab76cbad'),
    name: { en: 'other', ar: 'أخرى' },
    code: '4',
    type: 'other',
    commission_percentage: 2,
    commission_value: 2,
    createdAt: '2024-02-28T10:45:07.955Z',
    updatedAt: '2024-03-07T16:07:52.709Z',
    // eslint-disable-next-line @typescript-eslint/naming-convention
    __v: 0,
    commission_account: {
      _id: new Types.ObjectId('6501b9f946ad8225ab76cb84'),
      code: '1020202',
      'name.ar': 'مصرف الراجحى',
      'name.en': 'Alrajhi Bank',
      is_parent: false,
      active: true,
      hidden: false,
      balance_type: 'debit',
      group: {
        _id: new Types.ObjectId('6501b9f946ad8225ab76cb82'),
        code: '10202',
        'name.ar': 'النقديه بالبنوك',
        'name.en': 'Cash in Banks',
        is_parent: true,
        active: true,
        hidden: false,
        balance_type: 'debit',
        group: '6501b9f946ad8225ab76cb7d',
        type: 'bank_account',
        reporting_type: 'balance_sheet',

        static: true,
        balance: 0,
        currency: 'SAR',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        nodeQty: 2,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        __v: 0,
        createdAt: '2024-03-06T11:39:31.778Z',
        updatedAt: '2024-03-06T11:39:31.778Z',
      },
      type: 'bank_account',
      reporting_type: 'balance_sheet',

      static: true,
      balance: 0,
      currency: 'SAR',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      nodeQty: 0,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      __v: 0,
      createdAt: '2024-03-06T11:39:31.778Z',
      updatedAt: '2024-03-06T11:39:31.778Z',
    },
    payment_account: {
      _id: new Types.ObjectId('6501b9f946ad8225ab76cb80'),
      code: '1020102',
      'name.ar': 'صندوق الدولار',
      'name.en': 'USD Cash Box',
      is_parent: false,
      active: true,
      hidden: false,
      balance_type: 'debit',
      group: {
        _id: new Types.ObjectId('6501b9f946ad8225ab76cb7e'),
        code: '10201',
        'name.ar': 'النقديه بالصندوق',
        'name.en': 'Cash in Cash Box',
        is_parent: true,
        active: true,
        hidden: false,
        balance_type: 'debit',
        group: '6501b9f946ad8225ab76cb7d',
        type: 'cashier',
        reporting_type: 'balance_sheet',

        static: true,
        balance: 0,
        currency: 'SAR',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        nodeQty: 3,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        __v: 0,
        createdAt: '2024-03-06T11:39:31.778Z',
        updatedAt: '2024-03-06T11:39:31.778Z',
      },
      type: 'cashier',
      reporting_type: 'balance_sheet',

      static: true,
      balance: 0,
      currency: 'SAR',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      nodeQty: 0,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      __v: 0,
      createdAt: '2024-03-06T11:39:31.778Z',
      updatedAt: '2024-03-06T11:39:31.778Z',
    },
  };
};
