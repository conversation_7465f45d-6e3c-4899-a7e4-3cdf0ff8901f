/* eslint-disable @typescript-eslint/naming-convention */
export const singleJournalStub = () => {
  return {
    _id: '65e9bb26c21eb234ee0d94a9',
    transactions: [
      {
        credit: 22,
        debit: 0,
        description: 'test',
        accountingNode: {
          _id: '6501b9f946ad8225ab76cb8e',
          code: '1020401',
          name: {
            en: 'Inventory stock in stores',
            ar: 'مخزون بضاعه بالمستودعات',
          },
          is_parent: false,
          active: true,
          hidden: false,
          balance_type: 'debit',
          group: '6501b9f946ad8225ab76cb8d',
          type: 'general',
          reporting_type: 'balance_sheet',
          static: true,
          balance: -22,
          currency: 'SAR',
          nodeQty: 0,
          __v: 0,
          createdAt: '2024-03-07T06:31:38.556Z',
          updatedAt: '2024-03-07T13:03:33.901Z',
        },
        by_user: '65128e387d6e377e37b40a6c',
        current_balance: -22,
        before_balance: 0,
        code: '1020401',
      },
      {
        credit: 0,
        debit: 22,
        description: 'test',
        accountingNode: {
          _id: '6501b9f946ad8225ab76cbb6',
          code: '30108',
          name: {
            en: 'Settelment and Deficit in inventory',
            ar: 'تسوية اضافه وعجز مخزون',
          },
          is_parent: false,
          active: true,
          hidden: false,
          balance_type: 'debit',
          group: '6501b9f946ad8225ab76cba6',
          type: 'general',
          reporting_type: 'trade_account',
          static: true,
          balance: 22,
          currency: 'SAR',
          nodeQty: 0,
          __v: 0,
          createdAt: '2024-03-07T06:31:38.631Z',
          updatedAt: '2024-03-07T13:03:34.028Z',
        },
        by_user: '65128e387d6e377e37b40a6c',
        current_balance: 22,
        before_balance: 0,
        code: '30108',
      },
    ],
    type: 'store_adjustment',
    note: 'test',
    by_user: '65128e387d6e377e37b40a6c',
    reference: 'store adjustment',
    transaction_date: '2024-03-07T13:03:33.000Z',
    document: '65e9bb25b34a1443bed50fbb',
    document_code: '7',
    branch: '64956ef5f506e9edf21df4e8',
    createdAt: '2024-03-07T13:03:34.082Z',
    updatedAt: '2024-03-07T13:03:34.082Z',
    __v: 0,
    accountingNodePUP: [null, null],
  };
};
