import { User } from '../../users/users/schemas/user.schema';
import { Types } from 'mongoose';

export const userStub = (): User => {
  return {
    email: '<EMAIL>',
    name: 'Test User',
    password: 'password',
    positions: [
      {
        roles: [new Types.ObjectId('65b85d830d96fd024478c719')],
        branches: [new Types.ObjectId('65b85d830d96fd024478c719')],
      },
    ],
    default_payment_type: new Types.ObjectId('6501b9f946ad8225ab76cbad'),
    active: true,
  } as any;
};
