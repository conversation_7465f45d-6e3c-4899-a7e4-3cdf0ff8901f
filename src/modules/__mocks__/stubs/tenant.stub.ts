import { TenantUser } from '../../users/tenants/schemas';

export const tenantStub = (): TenantUser => {
  return {
    user_email: '<EMAIL>',
    tenant: {
      name: 'rozbeh',
      activation_status: true,
      registration_no: '1223',
      tax_code: '673637011662540',
      notes: '<PERSON> vis vorago tabesco sublime odio. Demoror .',
      code: 1,
      user: { name: '<PERSON><PERSON><PERSON>', email: '<PERSON><PERSON><PERSON><PERSON>.<EMAIL>' },
      national_address: {
        commercial_activities: '',
        short_address: '',
        building_no: '',
        street: '',
        secondary_no: '',
        district: '',
        postal_code: '',
        city: '',
        governorate: '',
      },
    },
  } as TenantUser;
};
