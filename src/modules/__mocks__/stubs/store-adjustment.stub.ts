import { Types } from 'mongoose';
import { CreateStoreAdjustmentDto } from '../../inventory/store_adjustments/dto/create-store-adjustment.dto';

export const storeAdjustmentsStub = (): CreateStoreAdjustmentDto => {
  return {
    number: 2,
    store: '64ad000ab16fbc375c9649de',
    type: 'add',
    note: 'test',
    transactions: [
      {
        item: new Types.ObjectId('64acf899b16fbc375c96498e'),
        unit: new Types.ObjectId('64acf899b16fbc375c96498e'),
        quantity: 10,
        avg_cost: 22,
        description: 'test',
      },
    ],
  } as any;
};
