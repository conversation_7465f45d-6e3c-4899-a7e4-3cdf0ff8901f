import { Types } from 'mongoose';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';

export const requestWithUserStub = (): RequestWithUser => {
  return {
    user: {
      _id: new Types.ObjectId('65128e387d6e377e37b40a6c'),
      email: '<EMAIL>',
      token: 'mockToken',
      code: 1,
      user_id: new Types.ObjectId('65128e387d6e377e37b40a6c'),
    },

    settings: [
      {
        _id: '64956ef5f506e9edf21df4e8',
        general_information: {
          code: 1,
          name: {
            en: 'Douglas - <PERSON>',
            ar: 'بنسلامة - العلاني ',
          },
          parent_branch: '',
          tax_code: '073580875947796',
          registration_number: 0,
          phone: '+966549883871',
          fax: '',
          email: '',
          address: '',
          activation_status: true,
          is_default: false,
          is_main_branch: false,
        },
        national_address: {
          trade_name: {
            en: 'Jewelery',
            ar: 'بيت',
          },
          commercial_activities: 'Shoes',
          short_address: 'lKZ',
          building_no: 3607,
          street: 'Jay Bridge',
          secondary_no: '083',
          postal_code: 28466,
          city: ' Lake Lanceville',
          governorate: 'Hawaii',
        },
        settings: {
          general: {
            current_stock_transaction: false,
            use_cost_centers: false,
            allow_modification_after_closing_date: false,
            allow_documents_deletion: true,
            allow_documents_edition: {
              enabled: true,
              type: 'allow-custom-date-document',
            },
            direct_cost_transaction: false,
            use_multi_stores: false,
            store_use_different_items: false,
            return_without_invoice: false,
            edit_invoice_item_total: false,
            post_discount: false,
            apply_change_cost_over_old_documents: false,
            apply_change_cost_over_old_transfers: false,
            print_journal_from_designer: false,
            show_proposal_conditions: false,
            allow_multi_salesman_in_receipts: false,
            branches_price_ratio_from_cost: false,
            show_sales_ordered_qty: false,
          },
          global_accounts: {
            sales_accounts: {
              cash_sales_account: '6501b9f946ad8225ab76cbba',
              credit_sales_account: '6501b9f946ad8225ab76cbbb',
              cash_sales_return_account: '6501b9f946ad8225ab76cbbc',
              credit_sales_return_account: '6501b9f946ad8225ab76cbbd',
              sales_discount_account: '6501b9f946ad8225ab76cbbe',
            },
            purchase_accounts: {
              cash_purchase_account: '6501b9f946ad8225ab76cba9',
              credit_purchase_account: '6501b9f946ad8225ab76cbaa',
              cash_purchase_return_account: '6501b9f946ad8225ab76cbab',
              credit_purchase_return_account: '6501b9f946ad8225ab76cbac',
              purchase_discount_account: '6501b9f946ad8225ab76cbad',
              deferred_discount_account: '',
            },
            cash_and_bank: {
              cash_account: '6501b9f946ad8225ab76cb7f',
              bank_account: '6501b9f946ad8225ab76cb83',
              additional_expense_account: '6501b9f946ad8225ab76cbae',
              cash_invoices_under_settlement: '',
              profit_account: '6501b9f946ad8225ab76cba3',
            },
            sub_accounts: {
              cash_sales_commission_account: '',
              credit_sales_commission_account: '',
              levy_commission_account: '',
              customer_group_account: '6501b9f946ad8225ab76cb85',
              vendor_group_account: '6501b9f946ad8225ab76cb97',
            },
            tax_accounts: {
              purchase_tax_account: '6501b9f946ad8225ab76cb92',
              sales_tax_account: '6501b9f946ad8225ab76cb9e',
              payment_commission_tax_account: '6501b9f946ad8225ab76cb93',
            },
            other_accounts: {
              transfer_account: '',
              adjustment_account: '6501b9f946ad8225ab76cbb6 ',
              inventory_account: '6501b9f946ad8225ab76cb8e',
              beginning_balance_account: '',
              ending_balance_account: '',
              cost_of_sales_account: '6501b9f946ad8225ab76cbb5',
              cost_of_purchase_account: '',
            },
          },
          _id: '65e58af2033d8812d53f8e32',
        },
        document: {
          sales_invoices: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 3,
            credit_serial: 3,
            default_unit_as_biggest: false,
          },
          purchases_invoices: {
            auto_serial: false,
            same_serial: true,
            cash_serial: 2,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          sales_return: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 13,
            credit_serial: 100,
            default_unit_as_biggest: false,
          },
          purchase_return: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 2,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          transfer_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          quotation: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          reservation: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          preparation: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          journal: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 100,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          payment_voucher_cash: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          receipt_voucher: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          debit_credit_memo: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 5,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          service_invoice_customer: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          service_invoice_vendors: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          purchase_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          store_transactions_and_adjustments: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          import_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          prepare_purchase_entry: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          transfer_receive: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          quotation_order_and_comp: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          fiscal_inventory_voucher: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 1,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          fiscal_inventory_posting: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 6,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          sales_receipts_consignment: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          purchase_receipts_consignment: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          issue_permission: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          addition_permission: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          work_orders: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          entry_permission: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          release_permission: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          work_order_production: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          transfer_preparing_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          production_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          quotation_request_p: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          cargo_manifest: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          cargo_manifest_invoice: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
        },
        contact_person: {
          name: '',
          mobile: '',
          phone: '',
          email: '',
        },
        dates: {
          year_beginning_date: '2024-03-04T08:47:30.145Z',
          year_ending_date: '2024-03-04T08:47:30.145Z',
        },
        version: 665360,
        createdAt: '2024-02-28T10:45:08.263Z',
        updatedAt: '2024-03-07T13:43:07.134Z',
      },
      {
        _id: '64956ef5f506e9edf21df4e9',
        general_information: {
          code: 2,
          name: {
            en: 'Gottlieb - Moen',
            ar: 'النخلي Inc',
          },
          parent_branch: '',
          tax_code: 575622815280212,
          registration_number: 0,
          phone: +966599232489,
          fax: '',
          email: '',
          address: '',
          activation_status: true,
          is_default: true,
          is_main_branch: false,
        },
        national_address: {
          trade_name: {
            en: 'Clothing',
            ar: 'أدوات',
          },
          commercial_activities: 'Health',
          short_address: 'vkK',
          building_no: '3163',
          street: 'Kingsway',
          secondary_no: 647,
          postal_code: 12890,
          city: 'Dickensborough',
          governorate: 'Louisiana',
        },
        settings: {
          general: {
            current_stock_transaction: false,
            use_cost_centers: false,
            allow_modification_after_closing_date: false,
            allow_documents_deletion: true,
            allow_documents_edition: {
              enabled: true,
            },
            direct_cost_transaction: false,
            use_multi_stores: false,
            store_use_different_items: false,
            return_without_invoice: false,
            edit_invoice_item_total: false,
            post_discount: false,
            apply_change_cost_over_old_documents: false,
            apply_change_cost_over_old_transfers: false,
            print_journal_from_designer: false,
            show_proposal_conditions: false,
            allow_multi_salesman_in_receipts: false,
            branches_price_ratio_from_cost: false,
            show_sales_ordered_qty: false,
          },
          global_accounts: {
            sales_accounts: {
              cash_sales_account: '6501b9f946ad8225ab76cbba',
              credit_sales_account: '6501b9f946ad8225ab76cbbb',
              cash_sales_return_account: '6501b9f946ad8225ab76cbbc',
              credit_sales_return_account: '6501b9f946ad8225ab76cbbd',
              sales_discount_account: '6501b9f946ad8225ab76cbbe',
            },
            purchase_accounts: {
              cash_purchase_account: '6501b9f946ad8225ab76cba9',
              credit_purchase_account: '6501b9f946ad8225ab76cbaa',
              cash_purchase_return_account: '6501b9f946ad8225ab76cbab',
              credit_purchase_return_account: '6501b9f946ad8225ab76cbac',
              purchase_discount_account: '6501b9f946ad8225ab76cbad',
              deferred_discount_account: '',
            },
            cash_and_bank: {
              cash_account: '6501b9f946ad8225ab76cb7f',
              bank_account: '6501b9f946ad8225ab76cb83',
              additional_expense_account: '',
              cash_invoices_under_settlement: '',
              profit_account: '6501b9f946ad8225ab76cba3',
            },
            sub_accounts: {
              cash_sales_commission_account: '',
              credit_sales_commission_account: '',
              levy_commission_account: '',
              customer_group_account: '6501b9f946ad8225ab76cb85',
              vendor_group_account: '6501b9f946ad8225ab76cb97',
            },
            tax_accounts: {
              purchase_tax_account: '6501b9f946ad8225ab76cb92',
              sales_tax_account: '6501b9f946ad8225ab76cb9e',
              payment_commission_tax_account: '6501b9f946ad8225ab76cb93',
            },
            other_accounts: {
              transfer_account: '',
              adjustment_account: '6501b9f946ad8225ab76cbb6',
              inventory_account: '6501b9f946ad8225ab76cb8e',
              beginning_balance_account: '',
              ending_balance_account: '',
              cost_of_sales_account: '6501b9f946ad8225ab76cbb5',
              cost_of_purchase_account: '',
            },
          },
          _id: '65df0eb4fe9da20f4c5881ff',
        },
        document: {
          sales_invoices: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 100,
            default_unit_as_biggest: false,
          },
          sales_return: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 100,
            default_unit_as_biggest: false,
          },
          purchase_return: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          transfer_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          quotation: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          reservation: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          preparation: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          journal: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 100,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          payment_voucher_cash: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          receipt_voucher: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          debit_credit_memo: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          service_invoice_customer: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          service_invoice_vendors: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          purchase_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          store_transactions_and_adjustments: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          import_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          prepare_purchase_entry: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          transfer_receive: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          quotation_order_and_comp: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          fiscal_inventory_voucher: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          fiscal_inventory_posting: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          sales_receipts_consignment: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          purchase_receipts_consignment: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          issue_permission: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          addition_permission: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          work_orders: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          entry_permission: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          release_permission: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          work_order_production: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          transfer_preparing_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          production_order: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          quotation_request_p: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          cargo_manifest: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          cargo_manifest_invoice: {
            auto_serial: true,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
          purchases_invoices: {
            auto_serial: false,
            same_serial: true,
            cash_serial: 0,
            credit_serial: 0,
            default_unit_as_biggest: false,
          },
        },
        contact_person: {
          name: '',
          mobile: '',
          phone: '',
          email: '',
        },
        dates: {
          year_beginning_date: '',
          year_ending_date: '',
        },
        version: 293178,
        createdAt: '2024-02-28T10:45:08.263Z',
        updatedAt: '2024-02-28T10:45:08.263Z',
      },
    ],
    headers: {
      branch: '64956ef5f506e9edf21df4e8',
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    policyData: {
      receipt_voucher: {
        auto_serial: true,
        same_serial: true,
        cash_serial: 4,
        credit_serial: 0,
        default_unit_as_biggest: false,
      },
      payment_voucher_cash: {
        auto_serial: true,
        same_serial: true,
        cash_serial: 4,
        credit_serial: 0,
        default_unit_as_biggest: false,
      },
      debit_credit_memo: {
        auto_serial: true,
        same_serial: true,
        cash_serial: 4,
        credit_serial: 0,
        default_unit_as_biggest: false,
      },

      version: 1,
      sales_discount_account: '6501b9f946ad8225ab76cb7f',
      purchase_tax_account: '6501b9f946ad8225ab76cb7f',
      journal: {
        auto_serial: false,
        same_serial: true,
        cash_serial: 4,
        credit_serial: 0,
        default_unit_as_biggest: false,
      },
      inventory_account: '64956ef5f506e9edf21df4e9',
      adjustment_account: '64956ef5f506e9edf21df4e9',

      physical_inventory_posting: {
        auto_serial: false,
        same_serial: false,
        cash_serial: 4,
        credit_serial: 0,
        default_unit_as_biggest: false,
      },
      store_transactions_and_adjustments: {
        auto_serial: false,
        same_serial: false,
        cash_serial: 4,
        credit_serial: 0,
        default_unit_as_biggest: false,
      },
    },
  } as unknown as RequestWithUser;
};

export const tenantUser = (): any => requestWithUserStub().user;
