import { Voucher } from '../../accounting/voucher/schema/voucher.schema';
import { settlementType } from '../../accounting/voucher/dto/settlment-type.enum';
import { voucherPartyType } from '../../accounting/voucher/dto/voucher-party.enum';
import { Types } from 'mongoose';
import { voucherType } from '../../accounting/voucher/dto/voucher-type.enum';

export const findOneReceiptVoucherResponseStub = (): Voucher => {
  return {
    _id: '65f9b9f79c098e298c147498',
    number: 4,
    type: voucherType.receipt,
    debit_account: [
      {
        accounting_node: '6501b9f946ad8225ab76cb7f',
        amount: 123,
      },
    ],
    credit_account: [
      {
        accounting_node: '6501b9f946ad8225ab76cb86',
        amount: 123,
        partner_id: '65f8425e79a87f3fe42a6f3a',
        partner_type: 'customer',
      },
    ],
    amount: 123,
    tax_amount: 0,
    journal: {
      _id: new Types.ObjectId('65f9b9f69c098e298c147470'),
      transactions: [
        {
          code: '1020101',
          credit: 0,
          debit: 123,
          accountingNode: {
            _id: '6501b9f946ad8225ab76cb7f',
            code: '1020101',
            'name.ar': 'الصندوق الرئيسى',
            'name.en': 'Main Cash Box',
            is_parent: false,
            active: true,
            hidden: false,
            balance_type: 'debit',
            group: '6501b9f946ad8225ab76cb7e',
            type: 'cashier',
            reporting_type: 'balance_sheet',

            static: true,
            balance: 43,
            currency: 'SAR',
            // eslint-disable-next-line @typescript-eslint/naming-convention
            nodeQty: 0,
            createdAt: '2024-03-15T13:44:34.648Z',
            updatedAt: '2024-03-19T16:19:17.513Z',
          },
          by_user: '65128e387d6e377e37b40a6c',
          description: 'dsf',
          current_balance: 103,
          partner_id: null,
          partner_type: null,
          before_balance: -20,
        } as any,
        {
          code: 1,
          credit: 123,
          debit: 0,
          accountingNode: {
            _id: '6501b9f946ad8225ab76cb86',
            code: 1,
            'name.ar': 'الرصاع - العواني',
            'name.en': 'Cassin, Wintheiser and Mayert',
            is_parent: false,
            active: true,
            hidden: false,
            balance_type: 'debit',
            group: '6501b9f946ad8225ab76cb85',
            type: 'bank_account',
            reporting_type: 'balance_sheet',

            static: true,
            invoice_party_type: 'customer',
            balance: -133,
            currency: 'SAR',
            // eslint-disable-next-line @typescript-eslint/naming-convention
            nodeQty: 0,
            createdAt: '2024-03-15T13:44:34.648Z',
            updatedAt: '2024-03-19T16:14:47.066Z',
          },
          by_user: '65128e387d6e377e37b40a6c',
          description: 'dsf',
          current_balance: -133,
          partner_id: '65f8425e79a87f3fe42a6f3a',
          partner_type: 'customer',
          before_balance: -10,
        } as any,
      ] as any,
      type: 'receipt_voucher',
      note: 'dsf',
      by_user: '65128e387d6e377e37b40a6c',
      transaction_date: '2024-03-19T16:10:14.805Z',
      branch: '64956ef5f506e9edf21df4e8',
      createdAt: '2024-03-19T16:14:47.074Z',
      updatedAt: '2024-03-19T16:14:47.100Z',
      document: '65f9b9f79c098e298c147498',
      document_code: '4',
    },
    branch: new Types.ObjectId('64956ef5f506e9edf21df4e9'),
    discount: 10,
    invoice_party_type: voucherPartyType.customer,
    memo: [
      {
        _id: '65f9b9f79c098e298c14748a',
        number: 1,
        type: 'credit_memo',
        debit_account: [],
        credit_account: [],
        amount: 10,
        tax_amount: 0,
        journal: '65f9b9f79c098e298c147484',
        branch: '64956ef5f506e9edf21df4e9',
        description: 'Discount for Receipt Voucher #4',
        transaction_date: '2024-03-19T16:10:15.082Z',
        invoice: [],
        is_include_tax: false,
        createdAt: '2024-03-19T16:14:47.031Z',
        updatedAt: '2024-03-19T16:14:47.031Z',
        note: 'note',
      },
    ] as any,
    note: 'dsf',
    transaction_date: new Date(),
    invoice: [],
    settlement_type: settlementType.on_account,
    payment_type: {
      _id: '6501b9f946ad8225ab76cbad',
      name: {
        en: 'other',
        ar: 'أخرى',
      },
      code: '4',
      type: 'other',
      commission_percentage: 0,
      commission_value: 0,
      createdAt: '2024-03-18T14:12:17.608Z',
      updatedAt: '2024-03-18T14:12:17.608Z',
    } as any,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    printOptions: {
      print: true,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      sendEmail: false,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      sendSms: false,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      templateLang: 'en',
    },

    reference: 'reference ',
    sales_representative: '65e7039aa4d7a77ba53cb5aa',
    is_include_tax: false,
    createdAt: '2024-03-19T16:14:47.087Z',
    updatedAt: '2024-03-19T16:14:47.087Z',
    accountingNode: [
      {
        _id: '6501b9f946ad8225ab76cb7f',
        code: '1020101',
        'name.ar': 'الصندوق الرئيسى',
        'name.en': 'Main Cash Box',
        is_parent: false,
        active: true,
        hidden: false,
        balance_type: 'debit',
        group: '6501b9f946ad8225ab76cb7e',
        type: 'cashier',
        reporting_type: 'balance_sheet',

        static: true,
        balance: 43,
        currency: 'SAR',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        nodeQty: 0,
        createdAt: '2024-03-15T13:44:34.648Z',
        updatedAt: '2024-03-19T16:19:17.513Z',
      },
      {
        _id: '6501b9f946ad8225ab76cb86',
        code: 1,
        'name.ar': 'الرصاع - العواني',
        'name.en': 'Cassin, Wintheiser and Mayert',
        is_parent: false,
        active: true,
        hidden: false,
        balance_type: 'debit',
        group: '6501b9f946ad8225ab76cb85',
        type: 'bank_account',
        reporting_type: 'balance_sheet',

        static: true,
        invoice_party_type: 'customer',
        balance: -133,
        currency: 'SAR',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        nodeQty: 0,
        createdAt: '2024-03-15T13:44:34.648Z',
        updatedAt: '2024-03-19T16:14:47.066Z',
      },
    ],
    partner: {
      _id: '65f8425e79a87f3fe42a6f3a',
      name: {
        en: 'Cassin, Wintheiser and Mayert',
        ar: 'الرصاع - العواني',
      },
      group: {
        _id: '65c3e1d942e04a2dede8d4f2',
        number: 1,
        name: {
          en: 'default customer group',
          ar: 'مجموعة العملاء الافتراضية',
        },
        type: 'customer',
        createdAt: '2024-03-18T13:32:14.515Z',
        updatedAt: '2024-03-18T13:32:14.515Z',
      },
      mobile: '+************',
      tax_code: '***************',
      email: '<EMAIL>',
      website: 'careless-mozzarella.net',
      national_address: {
        trade_name: {
          en: 'Jewelery',
          ar: 'جمال',
        },
        commercial_activities: 'Computers',
        short_address: 'wHZ',
        building_no: '1241',
        street: 'Woodside',
        secondary_no: '785',
        district: 'Cheshire',
        postal_code: '77619',
        city: 'North Ulicesburgh',
        governorate: 'Massachusetts',
      },
      is_customer_vendor: false,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      isActive: true,
      contact_person: {
        name: 'Mrs. Dorothy Thompson',
        mobile: '+************',
        phone: '+************',
        email: '<EMAIL>',
      },
      type: 'customer',
      accounting_info: {
        general_account: {
          _id: '6501b9f946ad8225ab76cb86',
          code: '*********',
          'name.ar': 'تموينات الخالدية',
          'name.en': 'Alkhaldieh Grocerey store',
          is_parent: false,
          active: true,
          hidden: false,
          balance_type: 'debit',
          group: {
            _id: '6501b9f946ad8225ab76cb85',
            code: '10203',
            'name.ar': 'الذمم المدينه -(العملاء)-',
            'name.en': 'Account Reciveable - Customers',
            is_parent: true,
            active: true,
            hidden: false,
            balance_type: 'debit',
            group: '6501b9f946ad8225ab76cb7d',
            type: 'party',
            reporting_type: 'balance_sheet',

            static: true,
            invoice_party_type: 'customer',
            balance: 0,
            currency: 'SAR',
            // eslint-disable-next-line @typescript-eslint/naming-convention
            nodeQty: 0,
            createdAt: '2024-03-15T13:44:34.648Z',
            updatedAt: '2024-03-15T13:44:34.648Z',
          },
          type: 'bank_account',
          reporting_type: 'balance_sheet',

          static: true,
          invoice_party_type: 'customer',
          balance: -133,
          currency: 'SAR',
          // eslint-disable-next-line @typescript-eslint/naming-convention
          nodeQty: 0,
          createdAt: '2024-03-15T13:44:34.648Z',
          updatedAt: '2024-03-19T16:14:47.066Z',
        },
        invoice_due_in: {
          type: 'to_end_of_month',
        },
        transactions: [
          {
            _id: 3,
            credit: 147,
            debit: 144,
            balance: null,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            lastTransactionDate: '2024-03-19T16:14:47.074Z',
          },
        ],
      },
      cash_only: false,
      number: 1,
      createdAt: '2024-03-18T13:32:14.250Z',
      updatedAt: '2024-03-18T13:32:14.250Z',
    },
  } as any;
};
