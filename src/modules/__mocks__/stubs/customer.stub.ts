/* eslint-disable @typescript-eslint/naming-convention */
import { Types } from 'mongoose';

export const customerStub = () => {
  return {
    _id: new Types.ObjectId('65f8425e79a87f3fe42a6f3a'),
    name: { en: '<PERSON><PERSON>, <PERSON>the<PERSON> and <PERSON><PERSON>', ar: 'الرصاع - العواني' },
    group: {
      _id: '65c3e1d942e04a2dede8d4f2',
      number: 1,
      name: { en: 'default customer group', ar: 'مجموعة العملاء الافتراضية' },
      type: 'customer',
      createdAt: '2024-03-18T13:32:14.515Z',
      updatedAt: '2024-03-18T13:32:14.515Z',
      __v: 0,
    },
    mobile: '+966569922424',
    tax_code: '380002980558672',
    email: '<EMAIL>',
    website: 'careless-mozzarella.net',
    national_address: {
      trade_name: { en: 'Jewelery', ar: 'جمال' },
      commercial_activities: 'Computers',
      short_address: 'wHZ',
      building_no: '1241',
      street: 'Woodside',
      secondary_no: '785',
      district: 'Cheshire',
      postal_code: '77619',
      city: 'North Ulicesburgh',
      governorate: 'Massachusetts',
    },
    is_customer_vendor: false,
    isActive: true,
    contact_person: {
      name: 'Mrs. Dorothy Thompson',
      mobile: '+************',
      phone: '+************',
      email: '<EMAIL>',
    },
    type: 'customer',
    accounting_info: {
      general_account: {
        _id: '6501b9f946ad8225ab76cb86',
        code: '*********',
        'name.ar': 'تموينات الخالدية',
        'name.en': 'Alkhaldieh Grocerey store',
        is_parent: false,
        active: true,
        hidden: false,
        balance_type: 'debit',
        group: [Object],
        type: 'bank_account',
        reporting_type: 'balance_sheet',
        static: true,
        invoice_party_type: 'customer',
        balance: 0,
        currency: 'SAR',
        nodeQty: 0,
        __v: 0,
        createdAt: '2024-03-15T13:44:34.648Z',
        updatedAt: '2024-03-15T13:44:34.648Z',
      },
      invoice_due_in: { type: 'to_end_of_month' },
      transactions: [[Object]],
    },
    cash_only: false,
    number: 1,
    createdAt: '2024-03-18T13:32:14.250Z',
    updatedAt: '2024-03-18T13:32:14.250Z',
    __v: 0,
  } as any;
};
