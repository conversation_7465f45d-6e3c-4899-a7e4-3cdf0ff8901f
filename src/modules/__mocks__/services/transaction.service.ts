export const transactionServiceMock = jest.fn().mockReturnValue({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  executeERPDocumentSaga: jest.fn().mockImplementation(async () => {}),

  // eslint-disable-next-line @typescript-eslint/naming-convention
  handleFailure: jest.fn().mockImplementation(async () => {}),

  // eslint-disable-next-line @typescript-eslint/naming-convention
  transactionCompleted: jest.fn().mockImplementation(async () => {}),

  // eslint-disable-next-line @typescript-eslint/naming-convention
  createdERPDocument: jest.fn().mockImplementation(async () => {}),

  // eslint-disable-next-line @typescript-eslint/naming-convention
  journalInventory: jest.fn().mockImplementation(async () => {
    return Promise.resolve([{ _id: '65e88ae0b34a1443bed507a1' }]);
  }),

  // eslint-disable-next-line @typescript-eslint/naming-convention
  journalAccounting: jest.fn().mockImplementation(async () => {
    return Promise.resolve({ _id: '65e88ae0b34a1443bed507a1' });
  }),
});
