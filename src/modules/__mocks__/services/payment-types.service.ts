import { userStub } from '../stubs/user.stub';
import { paymentTypeStub } from '../stubs/payment_type.stub';

export const paymentTypesServiceMock = jest.fn().mockReturnValue({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  findOnePayment: jest.fn().mockImplementation(async (_id?: string) => {
    if (String(_id) === String(userStub().default_payment_type)) {
      return Promise.resolve(paymentTypeStub());
    } else {
      return;
    }
  }),
});
