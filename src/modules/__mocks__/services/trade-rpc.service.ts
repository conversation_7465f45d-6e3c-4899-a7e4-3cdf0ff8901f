import { customerStub } from '../stubs/customer.stub';
import { vendorStub } from '../stubs/vendor.stub';

export const tradeClientMock = jest.fn().mockReturnValue({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  upsertPolicies: jest.fn().mockImplementation(async () => {
    return;
  }),

  // eslint-disable-next-line @typescript-eslint/naming-convention
  getPolicies: jest.fn().mockImplementation(async () => {
    return;
  }),

  // eslint-disable-next-line @typescript-eslint/naming-convention
  getPartner: jest.fn().mockImplementation(async (code, data) => {
    if (
      String(data._id) === String(customerStub()._id) &&
      data.type === 'customer'
    ) {
      return customerStub();
    } else if (
      String(data._id) === String(vendorStub()._id) &&
      data.type === 'vendor'
    ) {
      return vendorStub();
    } else {
      return null;
    }
  }),
});
