import { CreateTenantUserRequestDto } from '../../users/tenants/dtos';
import { tenantStub } from '../stubs/tenant.stub';
import { userStub } from '../stubs/user.stub';

export const tenantServicesMock = jest.fn().mockReturnValue({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  getTenantUser2: jest.fn().mockImplementation(async (filters: any) => {
    if (filters.email === userStub().email) {
      return tenantStub();
    } else {
      return;
    }
  }),
  // eslint-disable-next-line @typescript-eslint/naming-convention
  createTenantUser: jest
    .fn()
    .mockImplementation(async (users: CreateTenantUserRequestDto[]) => {
      return Promise.resolve([
        {
          ...userStub(),
          email: users[0].email,
        },
      ]);
    }),
});
