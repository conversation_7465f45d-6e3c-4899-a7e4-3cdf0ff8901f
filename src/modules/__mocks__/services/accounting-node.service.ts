import {
  customerAccountingNodeStub,
  generalAccountingNodeStub,
  generalAccountingNodeStub2,
  vendorAccountingNodeStub,
} from '../stubs/accounting-node.stub';
import { FindOneAccountingNodeDto } from '../../accounting/accounting-node/dto/find-one-accounting-node.dto';

export const accountingNodeServiceMock = jest.fn().mockReturnValue({
  findOne: jest
    .fn()
    .mockImplementation(async (filters: FindOneAccountingNodeDto) => {
      if (String(filters._id) === String(generalAccountingNodeStub()._id)) {
        return Promise.resolve(generalAccountingNodeStub());
      } else {
        return;
      }
    }),
  // eslint-disable-next-line @typescript-eslint/naming-convention
  findAllRaw: jest.fn().mockImplementation(async (ids: any[]) => {
    const result = [];
    const customerAccount = ids.find(
      (id) => String(customerAccountingNodeStub()._id) === String(id),
    );
    const vendorAccount = ids.find(
      (id) => String(vendorAccountingNodeStub()._id) === String(id),
    );
    const generalAccount1 = ids.find(
      (id) => String(generalAccountingNodeStub()._id) === String(id),
    );
    const generalAccount2 = ids.find(
      (id) => String(generalAccountingNodeStub2()._id) === String(id),
    );

    if (customerAccount) {
      result.push(customerAccountingNodeStub());
    }
    if (vendorAccount) {
      result.push(vendorAccountingNodeStub());
    }
    if (generalAccount1) {
      result.push(generalAccountingNodeStub());
    }
    if (generalAccount2) {
      result.push(generalAccountingNodeStub2());
    }

    return result;
  }),
  // eslint-disable-next-line @typescript-eslint/naming-convention
  findAllRawId: jest.fn().mockImplementation(async (ids: any[]) => {
    const result = [];
    const customerAccount = ids.find(
      (id) => String(customerAccountingNodeStub()._id) === String(id),
    );
    const vendorAccount = ids.find(
      (id) => String(vendorAccountingNodeStub()._id) === String(id),
    );
    const generalAccount1 = ids.find(
      (id) => String(generalAccountingNodeStub()._id) === String(id),
    );
    const generalAccount2 = ids.find(
      (id) => String(generalAccountingNodeStub2()._id) === String(id),
    );

    if (customerAccount) {
      result.push(customerAccountingNodeStub());
    }
    if (vendorAccount) {
      result.push(vendorAccountingNodeStub());
    }
    if (generalAccount1) {
      result.push(generalAccountingNodeStub());
    }
    if (generalAccount2) {
      result.push(generalAccountingNodeStub2());
    }

    return result;
  }),
});
