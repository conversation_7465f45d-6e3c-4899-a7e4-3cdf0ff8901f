/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/naming-convention */
import { documentType, steps } from '../../accounting/transactions/dto';
import { Types } from 'mongoose';
import {
  paymentVoucherJournalStub,
  paymentVoucherJournalWithTaxStub,
  receiptVoucherJournalStub,
} from '../stubs/general-ledger.stub';
import { creditMemoVoucherStub } from '../stubs/voucher.stub';
import { findOneReceiptVoucherResponseStub } from '../stubs/find-one-voucher.respose.stub';
import { CreateGeneralLedgerDto } from '../../accounting/general-ledger/dto/create-general-ledger.dto';

export const transactionServiceMock = jest.fn().mockReturnValue({
  executeERPDocumentSaga: jest
    .fn()
    .mockImplementation(
      async (
        branch: string,
        type: documentType,
        requiredSteps: Array<steps>,
        notRequiredSteps?: Array<steps>,
        store?: string,
      ) => {
        return new Types.ObjectId();
      },
    ),
  createdERPDocument: jest.fn().mockImplementation(async () => {}),
  handleFailure: jest.fn().mockImplementation(async () => {}),
  purchaseInvoice: jest.fn().mockImplementation(async () => {}),
  salesInvoice: jest.fn().mockImplementation(async () => {}),
  creditMemoVoucher: jest.fn().mockImplementation(async () => {
    return findOneReceiptVoucherResponseStub();
  }),
  voucherJournalAccounting: jest
    .fn()
    .mockImplementation(
      async (
        createJournalDto: CreateGeneralLedgerDto,
        prepare: boolean = true,
      ) => {
        if (createJournalDto.type === 'receipt_voucher') {
          return {
            ...receiptVoucherJournalStub(),
            save: jest.fn().mockResolvedValue(receiptVoucherJournalStub()),
          };
        }
        if (createJournalDto.type === 'payment_voucher') {
          if (createJournalDto.transactions.length === 3) {
            return {
              ...paymentVoucherJournalWithTaxStub(),
              save: jest
                .fn()
                .mockResolvedValue(paymentVoucherJournalWithTaxStub()),
            };
          }
          return {
            ...paymentVoucherJournalStub(),
            save: jest.fn().mockResolvedValue(paymentVoucherJournalStub()),
          };
        }
        return {
          ...creditMemoVoucherStub(),
          save: jest.fn().mockResolvedValue(creditMemoVoucherStub()),
        };
      },
    ),
  journalAccounting: jest
    .fn()
    .mockImplementation(
      async (
        createJournalDto: CreateGeneralLedgerDto,
        prepare: boolean = true,
      ) => {
        if (createJournalDto.type === 'receipt_voucher') {
          return {
            ...receiptVoucherJournalStub(),
            save: jest.fn().mockResolvedValue(receiptVoucherJournalStub()),
          };
        }
        if (createJournalDto.type === 'payment_voucher') {
          if (createJournalDto.transactions.length === 3) {
            return {
              ...paymentVoucherJournalWithTaxStub(),
              save: jest
                .fn()
                .mockResolvedValue(paymentVoucherJournalWithTaxStub()),
            };
          }
          return {
            ...paymentVoucherJournalStub(),
            save: jest.fn().mockResolvedValue(paymentVoucherJournalStub()),
          };
        }
        return {
          ...creditMemoVoucherStub(),
          save: jest.fn().mockResolvedValue(creditMemoVoucherStub()),
        };
      },
    ),
});
