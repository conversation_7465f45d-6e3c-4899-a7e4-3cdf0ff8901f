import {
  paymentVoucherJournalStub,
  paymentVoucherJournalWithTaxStub,
  receiptVoucherJournalStub,
} from '../stubs/general-ledger.stub';
import { creditMemoVoucherStub } from '../stubs/voucher.stub';
import { CreateGeneralLedgerDto } from '../../accounting/general-ledger/dto/create-general-ledger.dto';

export const journalServiceMock = jest.fn().mockReturnValue({
  create: jest.fn().mockImplementation(
    async (
      createJournalDto: CreateGeneralLedgerDto,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      prepare: boolean = true,
    ) => {
      if (createJournalDto.type === 'receipt_voucher') {
        return {
          ...receiptVoucherJournalStub(),
          save: jest.fn().mockResolvedValue(receiptVoucherJournalStub()),
        };
      }
      if (createJournalDto.type === 'payment_voucher') {
        if (createJournalDto.transactions.length === 3) {
          return {
            ...paymentVoucherJournalWithTaxStub(),
            save: jest
              .fn()
              .mockResolvedValue(paymentVoucherJournalWithTaxStub()),
          };
        }
        return {
          ...paymentVoucherJournalStub(),
          save: jest.fn().mockResolvedValue(paymentVoucherJournalStub()),
        };
      }
      return {
        ...creditMemoVoucherStub(),
        save: jest.fn().mockResolvedValue(creditMemoVoucherStub()),
      };
    },
  ),
});
