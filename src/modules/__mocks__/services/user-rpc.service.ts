import { paymentTypeDetailsStub } from '../stubs/payment_type.stub';

export const userClientMock = jest.fn().mockReturnValue({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  getpaymentType: jest.fn().mockImplementation(async (code, data) => {
    if (String(data._id) === String(paymentTypeDetailsStub()._id)) {
      return paymentTypeDetailsStub();
    } else {
      return null;
    }
  }),
});
