import { CreateVoucherDto } from '../../accounting/voucher/dto/create-voucher.dto';
import { RequestWithUser } from '../../auth/interfaces/authorize.interface';
import {
  creditMemoVoucherStub,
  debitMemoVoucherStub,
  paymentVoucherStub,
  receiptVoucherStub,
} from '../stubs/voucher.stub';
import { voucherType } from '../../accounting/voucher/dto/voucher-type.enum';

// eslint-disable-next-line @typescript-eslint/naming-convention
export const VoucherService = jest.fn().mockReturnValue({
  create: jest.fn().mockImplementation(
    async (
      dto: CreateVoucherDto,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      settings: any = undefined,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      request: RequestWithUser,
    ) => {
      if (dto.type === voucherType.payment) {
        return paymentVoucherStub();
      }
      if (dto.type === voucherType.receipt) {
        return receiptVoucherStub();
      }
      if (dto.type === voucherType.credit_memo) {
        return creditMemoVoucherStub();
      }
      if (dto.type === voucherType.debit_memo) {
        return debitMemoVoucherStub();
      }
    },
  ),
});
