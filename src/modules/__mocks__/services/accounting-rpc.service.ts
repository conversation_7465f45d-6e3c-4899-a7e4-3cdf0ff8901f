import { accountingMessagePattern } from '../../../utils/queues.enum';
import { singleJournalStub } from '../stubs/single-journal.stub';
import { of } from 'rxjs';
import { generalAccountingNodeStub } from '../stubs/accounting-node.stub';

export const accountingClientMock = jest.fn().mockReturnValue({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  getSingleAccountingNode: jest
    .fn()
    .mockImplementation(async (request, generalAccount) => {
      if (String(generalAccount) === String(generalAccountingNodeStub()._id)) {
        return generalAccountingNodeStub();
      } else {
        return;
      }
    }),

  send: jest.fn().mockImplementation((pattern, data) => {
    switch (pattern.cmd) {
      case accountingMessagePattern.get_single_journal:
        if (String(data._id) === String(singleJournalStub()._id)) {
          return of(singleJournalStub());
        } else {
          return of(null);
        }
      default:
        return of(null);
    }
  }),
});
