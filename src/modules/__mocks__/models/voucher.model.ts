import {
  creditMemoJournalStub,
  paymentVoucherJournalStub,
  receiptVoucherJournalStub,
} from '../stubs/general-ledger.stub';
import { CreateVoucherDto } from '../../accounting/voucher/dto/create-voucher.dto';
import { voucherType } from '../../accounting/voucher/dto/voucher-type.enum';

class VoucherModel {
  // Simulate any instance properties if necessary
  public _id = 'someMockId';
  public data: Partial<CreateVoucherDto>;

  constructor(createVoucherDto: Partial<CreateVoucherDto>) {
    this.data = createVoucherDto;
  }

  save = jest.fn().mockImplementation(() => {
    if (this.data.type === voucherType.receipt) {
      return Promise.resolve({
        ...this.data,
        _id: receiptVoucherJournalStub()._id,
        populate: jest.fn().mockResolvedValue({
          ...this.data,
          _id: receiptVoucherJournalStub()._id,
          journal: receiptVoucherJournalStub(),
        }),
      });
    }
    if (this.data.type === voucherType.payment) {
      return Promise.resolve({
        ...this.data,
        _id: paymentVoucherJournalStub()._id,
        populate: jest.fn().mockResolvedValue({
          ...this.data,
          _id: paymentVoucherJournalStub()._id,
          journal: paymentVoucherJournalStub(),
        }),
      });
    }
    return Promise.resolve({
      ...this.data,
      _id: creditMemoJournalStub()._id,
      populate: jest.fn().mockResolvedValue({
        ...this.data,
        _id: creditMemoJournalStub()._id,
        journal: creditMemoJournalStub(),
      }),
    });
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  static findOne = jest.fn().mockImplementation(async (query: any) => {});

  static create = jest
    .fn()
    .mockImplementation((createVoucherDto: CreateVoucherDto) => {
      return new VoucherModel(createVoucherDto);
    });

  static save = jest.fn().mockImplementation(async () => {
    return this;
  });
}

export const voucherModelMock = VoucherModel;
