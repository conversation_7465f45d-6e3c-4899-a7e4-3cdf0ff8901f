import { policyStub } from '../stubs/policy.stub';

export const policyModelMock = jest.fn().mockReturnValue({
  find: jest.fn().mockImplementation(({ service_name }) => {
    const mockExec = jest.fn().mockImplementation(async () => {
      if (service_name === 'wrong') {
        return Promise.resolve([]);
      } else {
        return Promise.resolve([policyStub()]);
      }
    });
    return { exec: mockExec };
  }),
});
