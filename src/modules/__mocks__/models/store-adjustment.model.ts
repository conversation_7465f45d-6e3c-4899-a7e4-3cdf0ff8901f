import { storeAdjustmentsStub } from '../stubs/store-adjustment.stub';

export const storeAdjustmentModelMock = jest.fn().mockReturnValue({
  findOne: jest.fn().mockImplementation(function ({ _id, number }) {
    if (number === storeAdjustmentsStub().number) {
      return Promise.resolve(storeAdjustmentsStub());
    } else if (_id === '65e88ae0b34a1443bed507a1') {
      return this;
    } else {
      return Promise.resolve(null);
    }
  }),
  create: jest.fn().mockImplementation(async (storeAdjustment) => {
    const created = {
      ...storeAdjustment,
      _id: '65e88ae0b34a1443bed507a1',
      save: jest.fn().mockImplementation(async function () {
        // Here, you might update the mock data or simply return 'this' to simulate a successful save.
        // You could also make this function resolve with updated data to simulate the document being saved.
        return this;
      }),
    };
    return Promise.resolve(created);
  }),
  populate: jest.fn().mockImplementation(function () {
    return this; // Allows chaining
  }),
  exec: jest.fn().mockImplementation(async () => {
    return Promise.resolve({
      ...storeAdjustmentsStub(),
      _id: '65e88ae0b34a1443bed507a1',
    });
  }),
});
