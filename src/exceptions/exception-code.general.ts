/* eslint-disable @typescript-eslint/naming-convention */
//need frontend change
import { HttpStatusCode } from 'axios';
import { GeneralExceptionCode } from './interface/general-exception-code.interface';

export const generalExceptionCode: GeneralExceptionCode = {
  unauthorized: HttpStatusCode.Unauthorized,
  internalServerError: HttpStatusCode.InternalServerError,
  badRequest: HttpStatusCode.BadRequest,
  notFound: HttpStatusCode.NotFound,
  conflict: HttpStatusCode.Conflict,
  tooManyRequest: HttpStatusCode.TooManyRequests,
  settingsNotFound: HttpStatusCode.NotFound,
  branchNotFound: HttpStatusCode.NotFound,
  unableToDelete: HttpStatusCode.Forbidden,
  unableToEdit: HttpStatusCode.Forbidden,
  unableToEditDueEOM: HttpStatusCode.Forbidden,
  unableToEditDueDLD: HttpStatusCode.Forbidden,
  maxAttemptsReached: HttpStatusCode.Forbidden,
  forbidden: HttpStatusCode.Forbidden,
  incorrectCredentials: HttpStatusCode.Unauthorized,
  noDirectApiAccess: HttpStatusCode.Forbidden,
};
