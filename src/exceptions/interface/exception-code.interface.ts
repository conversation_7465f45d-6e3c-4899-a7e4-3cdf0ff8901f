import { HttpStatusCode } from 'axios';

export interface ExceptionCode {
  unauthorizedToUpdate: HttpStatusCode;
  unauthorizedToDelete: HttpStatusCode;
  journalSnapshotDoesNotExist: HttpStatusCode;
  branchDoesNotExist: HttpStatusCode;
  invoiceJournalIsManualAndEmpty: HttpStatusCode;
  customerIdRequiredIfRegisterCustomerTrue: HttpStatusCode;
  vendorIdRequiredIfRegisterVendorTrue: HttpStatusCode;
  transactionDoesNotHaveItems: HttpStatusCode;
  itemDoesNotHaveUnit: HttpStatusCode;
  journalSnapshotForItemAndStoreDoesNotExist: HttpStatusCode;
  itemQuantityNotValid: HttpStatusCode;
  itemPriceNotValid: HttpStatusCode;
  totalDiscountNotValid: HttpStatusCode;
  taxAmountNotValid: HttpStatusCode;
  partnerNotFound: HttpStatusCode;
  totalAfterDiscountIsLessThanMinimumAvailable: HttpStatusCode;
  finalPriceIsNotValid: HttpStatusCode;
  costOfSalesAccountNotValid: HttpStatusCode;
  salesAccountsInvoiceExpensesNotValid: HttpStatusCode;
  vatAccountNotValid: HttpStatusCode;
  storeInventoryAccountNotValid: HttpStatusCode;
  cashierBoxAccountNotValid: HttpStatusCode;
  cashSalesAccountNotValid: HttpStatusCode;
  creditSalesAccountNotValid: HttpStatusCode;
  customerAccountNotValid: HttpStatusCode;
  accountAlreadyUsedAsGeneral: HttpStatusCode;
  customerAccountNotChangeable: HttpStatusCode;
  vendorAccountNotValid: HttpStatusCode;
  vendorAccountNotChangeable: HttpStatusCode;
  invoiceDoesNotExist: HttpStatusCode;
  documentCantDelete: HttpStatusCode;
  customerDoesNotExist: HttpStatusCode;
  vendorDoesNotExist: HttpStatusCode;
  templateDoesNotExist: HttpStatusCode;
  expenseNotFound: HttpStatusCode;
  actionNotFound: HttpStatusCode;
  numberDuplicated: HttpStatusCode;
  numberRequired: HttpStatusCode;
  salesInvoiceNotFound: HttpStatusCode;
  purchaseInvoiceNotFound: HttpStatusCode;
  invoiceTypeNotValid: HttpStatusCode;
  customerGroupNotFound: HttpStatusCode;
  vendorGroupNotFound: HttpStatusCode;
  customerIsCashOnly: HttpStatusCode;
  customerCreditLimitExceed: HttpStatusCode;
  onCustomerExpensesConflictNonRegisteredCustomer: HttpStatusCode;
  onVendorExpensesConflictNonRegisteredVendor: HttpStatusCode;
  vendorIsCashOnly: HttpStatusCode;
  salesRepresentativeNotFound: HttpStatusCode;
  accountingNodeTypeCashierOrBank: HttpStatusCode;
  expenseAccountCannotBeSame: HttpStatusCode;
  paymentTypeNotAllowed: HttpStatusCode;
  paymentTypeSelected: HttpStatusCode;
  paymentTypeNotFound: HttpStatusCode;
  invalidPrice: HttpStatusCode;
  invalidSubtotal: HttpStatusCode;
  invalidDiscount: HttpStatusCode;
  invalidVat: HttpStatusCode;
  freeTaxVat: HttpStatusCode;
  invalidTotalWithVat: HttpStatusCode;
  rowInvoiceDiscountNotValid: HttpStatusCode;
  rowInvoiceDiscountVatNotValid: HttpStatusCode;
  invoiceDiscountVatNotValid: HttpStatusCode;
  totalVatNotValid: HttpStatusCode;
  duplicatedNumber: HttpStatusCode;
  unknowingInvoiceType: HttpStatusCode;
  unknowingInvoiceStatisticsFilter: HttpStatusCode;
  cannotUseThisNumber: HttpStatusCode;
  numberMustBeSame: HttpStatusCode;
  problemInSendInvoice: HttpStatusCode;
  itemTypeNotAllowed: HttpStatusCode;
  referenceTypeNotSupported: HttpStatusCode;

  incorrectCredentials: HttpStatusCode;
  emailExist: HttpStatusCode;
  phoneExist: HttpStatusCode;
  invalidOtp: HttpStatusCode;
  branchNotFound: HttpStatusCode;
  cannotDeleteBranch: HttpStatusCode;
  roleAlreadyExist: HttpStatusCode;
  roleNotFound: HttpStatusCode;
  permNotFound: HttpStatusCode;
  tenantNotFound: HttpStatusCode;
  accountNotFound: HttpStatusCode;
  userNotFound: HttpStatusCode;
  tenantDisabled: HttpStatusCode;
  companyNotFound: HttpStatusCode;
  otpSend: HttpStatusCode;
  otpInvalid: HttpStatusCode;
  otpExpired: HttpStatusCode;
  internalServerError: HttpStatusCode;
  accountingNodeNotFound: HttpStatusCode;
  zatcaError: HttpStatusCode;
  csrAlreadyOnboarded: HttpStatusCode;

  branchRequired: HttpStatusCode;
  onlyCreditOrDebit: HttpStatusCode;
  journalNotBalanced: HttpStatusCode;
  notEmptyNode: HttpStatusCode;
  createFile: HttpStatusCode;
  notValidAccount: HttpStatusCode;
  customerNotFound: HttpStatusCode;
  addNodeToLeaf: HttpStatusCode;
  staticNodes: HttpStatusCode;
  emptyFile: HttpStatusCode;
  accountingTemplateNotFound: HttpStatusCode;
  journalNotFound: HttpStatusCode;
  voucherNotFound: HttpStatusCode;
  salesDiscountAccountNotFound: HttpStatusCode;
  voucherNotBalanced: HttpStatusCode;
  accountingCodeExist: HttpStatusCode;
  accountingCodeMissing: HttpStatusCode;
  tooManyDigits: HttpStatusCode;
  numberNotExist: HttpStatusCode;
  unknownAdjustmentType: HttpStatusCode;
  canNotDeleteUnitIsUsedInSystem: HttpStatusCode;
  snapshotNotFound: HttpStatusCode;
  noPhysicalInventoryJournal: HttpStatusCode;
  physicalInventoryPostingNotFound: HttpStatusCode;
  physicalInventoryPostingUnPosted: HttpStatusCode;
  itemUnitQuantityIsNegative: HttpStatusCode;
  negativeQuantityNotAllowed: HttpStatusCode;
  quantityNotAllowed: HttpStatusCode;
  vendorNotFound: HttpStatusCode;
  voucherItemsHadBeginningJournal: HttpStatusCode;
  duplicatedStoreItemsAndUnitInPosting: HttpStatusCode;
  duplicatedStoreItemsAndNewAvgCostInPosting: HttpStatusCode;
  branchItemDoesNotExists: HttpStatusCode;
  itemGroupNotFound: HttpStatusCode;
  itemNotFound: HttpStatusCode;
  unitNotFound: HttpStatusCode;
  storeNotFound: HttpStatusCode;
  unableToCreateMultipleStores: HttpStatusCode;
  physicalNotfound: HttpStatusCode;
  duplicatedCode: HttpStatusCode;
  duplicatePolicy: HttpStatusCode;
  invalidServiceItemGroup: HttpStatusCode;
  invalidItemGroup: HttpStatusCode;
  storeAdjustmentNotFound: HttpStatusCode;
  documentPolicyNotFound: HttpStatusCode;

  noteAndDescriptionNotfound: HttpStatusCode;
  memoIsNotDeletable: HttpStatusCode;
  memoIsNotEditable: HttpStatusCode;
  accountingNodeCashier: HttpStatusCode;
  wrongPaymentTypeNotEqualDebitAccount: HttpStatusCode;
  wrongPaymentTypeNotEqualCreditAccount: HttpStatusCode;
  voucherIsNotEditable: HttpStatusCode;
  nodeHasChild: HttpStatusCode;
  leafHasTransactions: HttpStatusCode;
  accountingNodeIsLeaf: HttpStatusCode;
  beginningBalanceIsNotBalanced: HttpStatusCode;
  accountAlreadyUsedAsPartner: HttpStatusCode;
  ledgerNotfound: HttpStatusCode;
  editDeletePolicyNotFound: HttpStatusCode;

  bundleDoesNotExist: HttpStatusCode;
  bundleItemTotalWrong: HttpStatusCode;
  bundleOriginalPriceWrong: HttpStatusCode;
  bundleDiscountPercentageWrong: HttpStatusCode;
}
