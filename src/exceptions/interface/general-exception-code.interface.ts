import { HttpStatusCode } from 'axios';

export interface GeneralExceptionCode {
  unauthorized: HttpStatusCode;
  internalServerError: HttpStatusCode;
  badRequest: HttpStatusCode;
  notFound: HttpStatusCode;
  conflict: HttpStatusCode;
  tooManyRequest: HttpStatusCode;
  settingsNotFound: HttpStatusCode;
  branchNotFound: HttpStatusCode;
  unableToDelete: HttpStatusCode;
  unableToEdit: HttpStatusCode;
  unableToEditDueEOM: HttpStatusCode;
  unableToEditDueDLD: HttpStatusCode;
  maxAttemptsReached: HttpStatusCode;
  forbidden: HttpStatusCode;
  incorrectCredentials: HttpStatusCode;
  noDirectApiAccess: HttpStatusCode;
}
