/* eslint-disable @typescript-eslint/naming-convention */
import { HttpStatusCode } from 'axios';
import { ExceptionCode } from './interface/exception-code.interface';

export const erpExceptionCode: ExceptionCode = {
  //trade
  unauthorizedToUpdate: HttpStatusCode.Forbidden,
  unauthorizedToDelete: HttpStatusCode.Forbidden,
  journalSnapshotDoesNotExist: HttpStatusCode.BadRequest,
  branchDoesNotExist: HttpStatusCode.BadRequest,
  invoiceJournalIsManualAndEmpty: HttpStatusCode.BadRequest,
  customerIdRequiredIfRegisterCustomerTrue: HttpStatusCode.BadRequest,
  vendorIdRequiredIfRegisterVendorTrue: HttpStatusCode.BadRequest,
  transactionDoesNotHaveItems: HttpStatusCode.BadRequest,
  itemDoesNotHaveUnit: HttpStatusCode.BadRequest,
  journalSnapshotForItemAndStoreDoesNotExist: HttpStatusCode.BadRequest,
  itemQuantityNotValid: HttpStatusCode.BadRequest,
  itemPriceNotValid: HttpStatusCode.BadRequest,
  totalDiscountNotValid: HttpStatusCode.BadRequest,
  taxAmountNotValid: HttpStatusCode.BadRequest,
  totalAfterDiscountIsLessThanMinimumAvailable: HttpStatusCode.BadRequest,
  finalPriceIsNotValid: HttpStatusCode.BadRequest,
  costOfSalesAccountNotValid: HttpStatusCode.BadRequest,
  salesAccountsInvoiceExpensesNotValid: HttpStatusCode.BadRequest,
  vatAccountNotValid: HttpStatusCode.BadRequest,
  storeInventoryAccountNotValid: HttpStatusCode.BadRequest,
  cashierBoxAccountNotValid: HttpStatusCode.BadRequest,
  cashSalesAccountNotValid: HttpStatusCode.BadRequest,
  creditSalesAccountNotValid: HttpStatusCode.BadRequest,
  customerAccountNotValid: HttpStatusCode.BadRequest,
  accountAlreadyUsedAsGeneral: HttpStatusCode.BadRequest,
  customerAccountNotChangeable: HttpStatusCode.BadRequest,
  invoiceDoesNotExist: HttpStatusCode.NotFound,
  documentCantDelete: HttpStatusCode.BadRequest,
  customerDoesNotExist: HttpStatusCode.NotFound,
  vendorAccountNotValid: HttpStatusCode.NotFound,
  vendorAccountNotChangeable: HttpStatusCode.BadRequest,
  templateDoesNotExist: HttpStatusCode.NotFound,
  expenseNotFound: HttpStatusCode.NotFound,
  actionNotFound: HttpStatusCode.NotFound,
  numberDuplicated: HttpStatusCode.BadRequest,
  numberRequired: HttpStatusCode.BadRequest,
  salesInvoiceNotFound: HttpStatusCode.NotFound,
  purchaseInvoiceNotFound: HttpStatusCode.NotFound,
  invoiceTypeNotValid: HttpStatusCode.BadRequest,
  customerGroupNotFound: HttpStatusCode.NotFound,
  vendorGroupNotFound: HttpStatusCode.NotFound,
  customerIsCashOnly: HttpStatusCode.Forbidden,
  customerCreditLimitExceed: HttpStatusCode.UnprocessableEntity,
  salesRepresentativeNotFound: HttpStatusCode.NotFound,
  vendorDoesNotExist: HttpStatusCode.NotFound,
  vendorIsCashOnly: HttpStatusCode.Forbidden,
  accountingNodeTypeCashierOrBank: HttpStatusCode.Forbidden,
  onCustomerExpensesConflictNonRegisteredCustomer: HttpStatusCode.Forbidden,
  onVendorExpensesConflictNonRegisteredVendor: HttpStatusCode.Forbidden,
  expenseAccountCannotBeSame: HttpStatusCode.Forbidden,
  paymentTypeNotAllowed: HttpStatusCode.BadRequest,
  paymentTypeSelected: HttpStatusCode.BadRequest,
  paymentTypeNotFound: HttpStatusCode.BadRequest,
  invalidPrice: HttpStatusCode.BadRequest,
  invalidSubtotal: HttpStatusCode.BadRequest,
  invalidDiscount: HttpStatusCode.BadRequest,
  invalidVat: HttpStatusCode.BadRequest,
  freeTaxVat: HttpStatusCode.BadRequest,
  invalidTotalWithVat: HttpStatusCode.BadRequest,
  rowInvoiceDiscountNotValid: HttpStatusCode.BadRequest,
  rowInvoiceDiscountVatNotValid: HttpStatusCode.BadRequest,
  invoiceDiscountVatNotValid: HttpStatusCode.BadRequest,
  totalVatNotValid: HttpStatusCode.BadRequest,
  duplicatedNumber: HttpStatusCode.BadRequest,
  unknowingInvoiceType: HttpStatusCode.BadRequest,
  unknowingInvoiceStatisticsFilter: HttpStatusCode.BadRequest,
  cannotUseThisNumber: HttpStatusCode.Forbidden,
  numberMustBeSame: HttpStatusCode.Forbidden,
  problemInSendInvoice: HttpStatusCode.BadRequest,
  itemTypeNotAllowed: HttpStatusCode.BadRequest,

  //users
  incorrectCredentials: HttpStatusCode.Unauthorized,
  emailExist: HttpStatusCode.Conflict,
  phoneExist: HttpStatusCode.Conflict,
  invalidOtp: HttpStatusCode.Forbidden,
  branchNotFound: HttpStatusCode.NotFound,
  cannotDeleteBranch: HttpStatusCode.Forbidden,
  roleAlreadyExist: HttpStatusCode.Conflict,
  roleNotFound: HttpStatusCode.NotFound,
  permNotFound: HttpStatusCode.NotFound,
  tenantNotFound: HttpStatusCode.NotFound,
  accountNotFound: HttpStatusCode.NotFound,
  userNotFound: HttpStatusCode.NotFound,
  tenantDisabled: HttpStatusCode.NotFound,
  companyNotFound: HttpStatusCode.NotFound,
  otpSend: HttpStatusCode.Forbidden,
  otpInvalid: HttpStatusCode.Forbidden,
  otpExpired: HttpStatusCode.Forbidden,
  internalServerError: HttpStatusCode.InternalServerError,
  accountingNodeNotFound: HttpStatusCode.NotFound,
  zatcaError: HttpStatusCode.Forbidden,
  csrAlreadyOnboarded: HttpStatusCode.Forbidden,

  //inventory
  branchRequired: HttpStatusCode.Forbidden,
  onlyCreditOrDebit: HttpStatusCode.Conflict,
  journalNotBalanced: HttpStatusCode.Conflict,
  voucherNotBalanced: HttpStatusCode.Conflict,
  notEmptyNode: HttpStatusCode.Forbidden,
  createFile: HttpStatusCode.InternalServerError,
  notValidAccount: HttpStatusCode.NotFound,
  customerNotFound: HttpStatusCode.NotFound,
  addNodeToLeaf: HttpStatusCode.Forbidden,
  staticNodes: HttpStatusCode.Forbidden,
  emptyFile: HttpStatusCode.Forbidden,
  accountingTemplateNotFound: HttpStatusCode.NotFound,
  journalNotFound: HttpStatusCode.NotFound,
  voucherNotFound: HttpStatusCode.NotFound,
  salesDiscountAccountNotFound: HttpStatusCode.NotFound,
  accountingCodeExist: HttpStatusCode.Forbidden,
  tooManyDigits: HttpStatusCode.Forbidden,
  numberNotExist: HttpStatusCode.Forbidden,
  accountingCodeMissing: HttpStatusCode.Forbidden,
  unknownAdjustmentType: HttpStatusCode.BadRequest,
  canNotDeleteUnitIsUsedInSystem: HttpStatusCode.Forbidden,
  snapshotNotFound: HttpStatusCode.NotFound,
  noPhysicalInventoryJournal: HttpStatusCode.BadRequest,
  physicalInventoryPostingNotFound: HttpStatusCode.NotFound,
  physicalInventoryPostingUnPosted: HttpStatusCode.BadRequest,
  itemUnitQuantityIsNegative: HttpStatusCode.BadRequest,
  negativeQuantityNotAllowed: HttpStatusCode.BadRequest,
  quantityNotAllowed: HttpStatusCode.BadRequest,
  vendorNotFound: HttpStatusCode.NotFound,
  voucherItemsHadBeginningJournal: HttpStatusCode.BadRequest,
  duplicatedStoreItemsAndUnitInPosting: HttpStatusCode.BadRequest,
  duplicatedStoreItemsAndNewAvgCostInPosting: HttpStatusCode.BadRequest,
  branchItemDoesNotExists: HttpStatusCode.BadRequest,
  itemGroupNotFound: HttpStatusCode.NotFound,
  itemNotFound: HttpStatusCode.NotFound,
  unitNotFound: HttpStatusCode.NotFound,
  storeNotFound: HttpStatusCode.NotFound,
  unableToCreateMultipleStores: HttpStatusCode.Forbidden,
  physicalNotfound: HttpStatusCode.NotFound,
  duplicatedCode: HttpStatusCode.Forbidden,
  duplicatePolicy: HttpStatusCode.BadRequest,
  invalidServiceItemGroup: HttpStatusCode.BadRequest,
  invalidItemGroup: HttpStatusCode.BadRequest,
  storeAdjustmentNotFound: HttpStatusCode.NotFound,
  documentPolicyNotFound: HttpStatusCode.NotFound,
  referenceTypeNotSupported: HttpStatusCode.NotFound,

  //accounting

  partnerNotFound: HttpStatusCode.BadRequest,
  noteAndDescriptionNotfound: HttpStatusCode.NotFound,
  accountingNodeCashier: HttpStatusCode.Forbidden,
  wrongPaymentTypeNotEqualDebitAccount: HttpStatusCode.Conflict,
  wrongPaymentTypeNotEqualCreditAccount: HttpStatusCode.Conflict,
  memoIsNotEditable: HttpStatusCode.Forbidden,
  voucherIsNotEditable: HttpStatusCode.Forbidden,
  memoIsNotDeletable: HttpStatusCode.Forbidden,
  nodeHasChild: HttpStatusCode.Forbidden,
  leafHasTransactions: HttpStatusCode.Forbidden,
  accountingNodeIsLeaf: HttpStatusCode.Forbidden,
  beginningBalanceIsNotBalanced: HttpStatusCode.Forbidden,
  accountAlreadyUsedAsPartner: HttpStatusCode.Forbidden,

  ledgerNotfound: HttpStatusCode.NotFound,
  editDeletePolicyNotFound: HttpStatusCode.NotFound,

  bundleDoesNotExist: HttpStatusCode.NotFound,
  bundleItemTotalWrong: HttpStatusCode.UnprocessableEntity,
  bundleOriginalPriceWrong: HttpStatusCode.UnprocessableEntity,
  bundleDiscountPercentageWrong: HttpStatusCode.UnprocessableEntity,
};
