import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON>ler,
  HttpException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { nameOf } from '../utils/object-key-name';
import { generalExceptionCode } from '../exceptions/exception-code.general';

@Injectable()
export class RouteRules implements NestInterceptor {
  constructor() {}
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    if (context.getType() == 'http') {
      const request = context.switchToHttp().getRequest();
      const config = {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        allowEdit: request.policyData?.allow_documents_edition?.enabled,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        allowDelete: request.policyData?.allow_documents_deletion,
      };

      if (config.allowEdit === false && request.method === 'PATCH') {
        throw new HttpException(
          nameOf(generalExceptionCode, (x) => x.unableToEdit),
          generalExceptionCode.unableToEdit,
        );
      }
      if (config.allowDelete === false && request.method === 'DELETE') {
        console.log('hi');

        throw new HttpException(
          nameOf(generalExceptionCode, (x) => x.unableToDelete),
          generalExceptionCode.unableToDelete,
        );
      }
    }
    return next.handle();
  }
}
