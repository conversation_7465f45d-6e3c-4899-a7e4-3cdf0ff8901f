import * as winston from 'winston';

export const winstonConfigAsync = {
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.cli(),
        winston.format.splat(),
        winston.format.timestamp(),
        winston.format.printf((info) => {
          return `Winston ${info.timestamp} ${info.level}: ${info.message}`;
        }),
      ),
      level: 'info',
    }),
  ],
};
