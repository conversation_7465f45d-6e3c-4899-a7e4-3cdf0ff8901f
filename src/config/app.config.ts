import {
  HttpException,
  Injectable,
  ValidationPipeOptions,
  ValidationError,
  LogLevel,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { generalExceptionCode } from '../exceptions/exception-code.general';
import { nameOf } from '../utils/object-key-name';

@Injectable()
export default class AppConfig {
  constructor(private configService: ConfigService) {}

  get name(): string {
    return this.configService.get<string>('APP_NAME') || 'Nest App';
  }

  get logLevel(): LogLevel[] {
    return this.configService.get<LogLevel[]>('LOG_LEVEL');
  }

  get port(): number {
    return this.configService.get<number>('PORT') || 3000;
  }

  get env(): string {
    return this.configService.get<string>('NODE_ENV') || 'dev';
  }

  get rmqUrl(): string {
    return this.configService.get<string>('RABBIT_MQ');
  }

  get dbUrl(): string {
    return this.configService.get<string>('DB_URL');
  }

  get prefixDB(): string {
    return this.configService.get<string>('PREFIX_DB');
  }

  get baseUrl(): string[] {
    return this.configService.get<string[]>('BASE_URL');
  }

  get accessTokenExpire(): number {
    return this.configService.get<number>('ACCESS_TOKEN_EXPIRE');
  }

  get appUrlPrefix(): string {
    return this.configService.get<string>('APP_URL_PREFIX');
  }

  get redis(): string {
    return this.configService.get<string>('REDIS');
  }
  get IsDebugMode(): boolean {
    return this.configService.get<boolean>('APP_DEBUG') || true;
  }

  get rabbitmq_host(): string {
    return this.configService.get<string>('RABBIT_MQ');
  }

  get refreshTokenExpire(): number {
    return this.configService.get<number>('REFRESH_TOKEN_EXPIRE');
  }

  get zatcaBaseUrl(): string {
    return this.configService.get<string>('ZATCA_URL');
  }

  get mailHost(): string {
    return this.configService.get<string>('MAIL_HOST');
  }

  get mailPort(): number {
    return this.configService.get<number>('MAIL_PORT');
  }

  get mailUser(): string {
    return this.configService.get<string>('MAIL_USER');
  }

  get mailPass(): string {
    return this.configService.get<string>('MAIL_PASSWORD');
  }
}

export const validationConfig: ValidationPipeOptions = {
  whitelist: true,
  transform: true,
  skipNullProperties: false,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  validateCustomDecorators: true,
  skipUndefinedProperties: false,
  validationError: { target: false, value: false },
  stopAtFirstError: false,
  forbidUnknownValues: false,
  exceptionFactory(validationErrors: ValidationError[] = []) {
    throw new HttpException(
      {
        exceptionCode: nameOf(
          generalExceptionCode,
          (exception) => exception.badRequest,
        ),
        response: validationErrors, //JSON.stringify(validationErrors, null, 2),
      },
      generalExceptionCode.badRequest,
    );
  },
};
