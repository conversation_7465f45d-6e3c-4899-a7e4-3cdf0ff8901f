import { PermissionsModel } from '../../../modules/users/permissions/model/permissons.model';
import { PermissionPrivilegesTypeModel } from '../../../modules/users/permissions/model/permission-privileges.model';
import { PermissionsPermListModel } from '../../../modules/users/permissions/model/permission-privileages-list.model';

export async function seedPermSql(
  data,
  mongoId: Array<any>,
  schemaName: string,
) {
  const cache = new Map<string, PermissionPrivilegesTypeModel>();

  for (const [index, value] of data.entries()) {
    // 1. Create the permission entry

    const permission = await PermissionsModel.schema(schemaName).create({
      name: value.name,
      mongo_id: String(mongoId[index]),
    });

    for (const priv of value.privileges) {
      const key = `${priv.action}:${priv.subject}`;

      // 2. Reuse existing privilege if already created
      let privilege = cache.get(key);

      if (!privilege) {
        privilege = await PermissionPrivilegesTypeModel.schema(
          schemaName,
        ).findOne({
          where: { action: priv.action, subject: priv.subject },
        });

        if (!privilege) {
          privilege = await PermissionPrivilegesTypeModel.schema(
            schemaName,
          ).create({
            action: priv.action,
            subject: priv.subject,
          });
        }

        cache.set(key, privilege);
      }

      // 3. Link permission to privilege
      await PermissionsPermListModel.schema(schemaName).create({
        permission_id: permission.id,
        privileges_id: privilege.id,
      });
    }
  }
}
