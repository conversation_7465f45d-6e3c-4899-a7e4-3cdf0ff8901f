import { faker, fakerAR } from '@faker-js/faker';
import mongoose from 'mongoose';
import { bundleSchema } from '../../modules/inventory/bundle/schema/bundle.schema';
import { bundleType } from '../../modules/inventory/bundle/types/bundle-types.enum';
import { ItemSeeder } from './item-seeder';
import { ItemModel } from '../../modules/inventory/inventory-item/model/item.model';
import { UnitModel } from '../../modules/inventory/unit/model/unit.model';
import { BundleModel } from '../../modules/inventory/bundle/model/bundle.model';
import { BundleItemModel } from '../../modules/inventory/bundle/model/bundle-item.model';

export class BundleSeeder {
  bundleModel: any;

  constructor(private db: any) {
    this.db = db;
    this.bundleModel = this.db.model('bundles', bundleSchema);
  }

  static bundleId = {
    office_combo: new mongoose.Types.ObjectId('64acf899b16fbc375c964998'),
    office_combo_sql: 11,
    drinks_combo: new mongoose.Types.ObjectId('64acf899b16fbc375c964999'),
    drinks_combo_sql: 12,
    spices_combo: new mongoose.Types.ObjectId('64acf899b16fbc375c96499a'),
    spices_combo_sql: 13,
    service_combo: new mongoose.Types.ObjectId('64acf899b16fbc375c96499b'),
    service_combo_sql: 14,
  };

  static mergedBundleListSet: Set<string> = new Set([
    ...String(this.bundleId.office_combo),
  ]);

  static generateUniqueObjectId(): mongoose.Types.ObjectId {
    let newId: mongoose.Types.ObjectId;

    do {
      newId = new mongoose.Types.ObjectId();
    } while (this.mergedBundleListSet.has(newId.toHexString()));

    return newId;
  }

  async stressSeeding(unitIds: any, itemIds: any, schemaName?: string) {
    const bundlesData = [];
    for (let i = 100; i < 200; i++) {
      const bundleItems = [
        {
          item: ItemSeeder.itemId.chairs,
          unit: unitIds.piece,
          qty: faker.number.int({ min: 1, max: 5 }),
          price: faker.number.float({ min: 10, max: 50, fractionDigits: 2 }),
          total: 0, // Will be calculated
        },
        {
          item: ItemSeeder.itemId.tables,
          unit: unitIds.piece,
          qty: faker.number.int({ min: 1, max: 3 }),
          price: faker.number.float({ min: 80, max: 120, fractionDigits: 2 }),
          total: 0, // Will be calculated
        },
      ];

      // Calculate totals and original price
      let originalPrice = 0;
      bundleItems.forEach((item) => {
        item.total = item.qty * item.price;
        originalPrice += item.total; // original_price is sum of (qty * price) for each item
      });

      const discountPercentage = faker.number.int({ min: 5, max: 25 });
      const bundlePrice = originalPrice * (1 - discountPercentage / 100);

      const bundle = {
        code: `BND${i}`,
        name: {
          en: faker.commerce.productName() + ' Bundle',
          ar: fakerAR.commerce.productName() + ' حزمة',
        },
        nested_bundles: false,
        status: true,
        type: bundleType.simple,
        bundle_items: bundleItems,
        number_of_items: bundleItems.length,
        description: faker.commerce.productDescription(),
        bundle_price: Math.round(bundlePrice * 100) / 100,
        original_price: Math.round(originalPrice * 100) / 100,
        discount_percentage: discountPercentage,
        _id: BundleSeeder.generateUniqueObjectId(),
      };

      bundlesData.push(bundle);
    }

    const bundles = await this.bundleModel.create(bundlesData);

    // Create SQL bundle records for stress testing
    if (schemaName) {
      // Create SQL bundle records
      const bundleSqlList = await Promise.all(
        bundles.map(async (bundle: any) => {
          return {
            mongo_id: String(bundle._id),
            code: bundle.code,
            name_en: bundle.name.en,
            name_ar: bundle.name.ar,
            nested_bundles: bundle.nested_bundles,
            status: bundle.status,
            type: bundle.type,
            number_of_items: bundle.number_of_items,
            description: bundle.description,
            bundle_price: bundle.bundle_price,
            original_price: bundle.original_price,
            discount_percentage: bundle.discount_percentage,
          };
        }),
      );

      await BundleModel.schema(schemaName).bulkCreate(bundleSqlList);
      console.log(`✅ Created ${bundleSqlList.length} SQL bundle records (stress test)`);

      // Create SQL bundle item records
      const bundleItemsSql = [];
      for (let index = 0; index < bundles.length; index++) {
        const bundle = bundles[index];
        for (
          let itemIndex = 0;
          itemIndex < bundle.bundle_items.length;
          itemIndex++
        ) {
          const bundleItem = bundle.bundle_items[itemIndex];

          const bundleId = (
            await BundleModel.schema(schemaName).findOne({
              where: { mongo_id: String(bundle._id) },
              attributes: ['id'],
            })
          ).id;

          const itemId = (
            await ItemModel.schema(schemaName).findOne({
              where: { mongo_id: String(bundleItem.item) },
              attributes: ['id'],
            })
          ).id;

          const unitId = (
            await UnitModel.schema(schemaName).findOne({
              where: { mongo_id: String(bundleItem.unit) },
              attributes: ['id'],
            })
          ).id;

          bundleItemsSql.push({
            bundle_id: bundleId,
            item_id: itemId,
            unit_id: unitId,
            qty: bundleItem.qty,
            price: bundleItem.price,
            total: bundleItem.total,
            mongo_id: String(bundle._id) + '_' + String(bundleItem.item),
          });
        }
      }

      await BundleItemModel.schema(schemaName).bulkCreate(bundleItemsSql);
      console.log(`✅ Created ${bundleItemsSql.length} SQL bundle item records (stress test)`);
    }

    return bundles;
  }

  async seed(unitIds: any, schemaName?: string) {
    const bundlesData = [
      {
        // Office Combo Bundle
        code: 'BND001',
        name: { en: 'Office Combo Bundle', ar: 'حزمة مكتب متكاملة' },
        nested_bundles: false,
        status: true,
        type: bundleType.simple,
        bundle_items: [
          {
            item: ItemSeeder.itemId.chairs,
            unit: unitIds.piece,
            qty: 2,
            price: 22,
            total: 44, // 2 * 22
          },
          {
            item: ItemSeeder.itemId.tables,
            unit: unitIds.piece,
            qty: 1,
            price: 100,
            total: 100, // 1 * 100
          },
        ],
        number_of_items: 2,
        description: 'Complete office setup with chairs and table',
        bundle_price: 130,
        original_price: 144, // 44 + 100 = 144 (sum of totals)
        discount_percentage: 9.72, // ((144-130)/144)*100 = 9.72%
        _id: BundleSeeder.bundleId.office_combo,
      },
      {
        // Drinks Combo Bundle
        code: 'BND002',
        name: { en: 'Drinks Combo Bundle', ar: 'حزمة المشروبات' },
        nested_bundles: false,
        status: true,
        type: bundleType.simple,
        bundle_items: [
          {
            item: ItemSeeder.itemId.cocacola,
            unit: unitIds.dozen,
            qty: 1,
            price: 14,
            total: 14, // 1 * 14
          },
          {
            item: ItemSeeder.itemId.pepsi,
            unit: unitIds.dozen,
            qty: 1,
            price: 14,
            total: 14, // 1 * 14
          },
        ],
        number_of_items: 2,
        description: 'Mixed drinks bundle for parties',
        bundle_price: 25,
        original_price: 28, // 14 + 14 = 28 (sum of totals)
        discount_percentage: 10.71, // ((28-25)/28)*100 = 10.71%
        _id: BundleSeeder.bundleId.drinks_combo,
      },
      {
        // Spices Combo Bundle
        code: 'BND003',
        name: { en: 'Premium Spices Bundle', ar: 'حزمة البهارات المميزة' },
        nested_bundles: false,
        status: true,
        type: bundleType.simple,
        bundle_items: [
          {
            item: ItemSeeder.itemId.cepper,
            unit: unitIds.bag_three_hundred_gram,
            qty: 1,
            price: 10.5,
            total: 10.5, // 1 * 10.5
          },
          {
            item: ItemSeeder.itemId.cardamom,
            unit: unitIds.bag_three_hundred_gram,
            qty: 1,
            price: 12,
            total: 12, // 1 * 12
          },
          {
            item: ItemSeeder.itemId.tumeric,
            unit: unitIds.bag_three_hundred_gram,
            qty: 1,
            price: 7.5,
            total: 7.5, // 1 * 7.5
          },
        ],
        number_of_items: 3,
        description: 'Essential spices collection for cooking',
        bundle_price: 27,
        original_price: 30, // 10.5 + 12 + 7.5 = 30 (sum of totals)
        discount_percentage: 10, // ((30-27)/30)*100 = 10%
        _id: BundleSeeder.bundleId.spices_combo,
      },
      {
        // Service Combo Bundle
        code: 'BND004',
        name: {
          en: 'Car Care Service Bundle',
          ar: 'حزمة خدمات العناية بالسيارة',
        },
        nested_bundles: false,
        status: true,
        type: bundleType.simple,
        bundle_items: [
          {
            item: ItemSeeder.itemId.car_wash,
            unit: unitIds.basic,
            qty: 1,
            price: 3.5,
            total: 3.5, // 1 * 3.5
          },
          {
            item: ItemSeeder.itemId.car_repair,
            unit: unitIds.one_person,
            qty: 1,
            price: 5,
            total: 5, // 1 * 5
          },
        ],
        number_of_items: 2,
        description: 'Complete car care package',
        bundle_price: 7.5,
        original_price: 8.5, // 3.5 + 5 = 8.5 (sum of totals)
        discount_percentage: 11.76, // ((8.5-7.5)/8.5)*100 = 11.76%
        _id: BundleSeeder.bundleId.service_combo,
      },
    ];

    const bundles = await this.bundleModel.create(bundlesData);

    // Create SQL bundle records
    if (schemaName) {
      // Create SQL bundle records
      const bundleSqlList = await Promise.all(
        bundles.map(async (bundle: any) => {
          return {
            mongo_id: String(bundle._id),
            code: bundle.code,
            name_en: bundle.name.en,
            name_ar: bundle.name.ar,
            nested_bundles: bundle.nested_bundles,
            status: bundle.status,
            type: bundle.type,
            number_of_items: bundle.number_of_items,
            description: bundle.description,
            bundle_price: bundle.bundle_price,
            original_price: bundle.original_price,
            discount_percentage: bundle.discount_percentage,
          };
        }),
      );

      await BundleModel.schema(schemaName).bulkCreate(bundleSqlList);
      console.log(`✅ Created ${bundleSqlList.length} SQL bundle records`);

      // Create SQL bundle item records
      const bundleItemsSql = [];
      for (let index = 0; index < bundles.length; index++) {
        const bundle = bundles[index];
        for (
          let itemIndex = 0;
          itemIndex < bundle.bundle_items.length;
          itemIndex++
        ) {
          const bundleItem = bundle.bundle_items[itemIndex];

          const bundleId = (
            await BundleModel.schema(schemaName).findOne({
              where: { mongo_id: String(bundle._id) },
              attributes: ['id'],
            })
          ).id;

          const itemId = (
            await ItemModel.schema(schemaName).findOne({
              where: { mongo_id: String(bundleItem.item) },
              attributes: ['id'],
            })
          ).id;

          const unitId = (
            await UnitModel.schema(schemaName).findOne({
              where: { mongo_id: String(bundleItem.unit) },
              attributes: ['id'],
            })
          ).id;

          bundleItemsSql.push({
            bundle_id: bundleId,
            item_id: itemId,
            unit_id: unitId,
            qty: bundleItem.qty,
            price: bundleItem.price,
            total: bundleItem.total,
            mongo_id: String(bundle._id) + '_' + String(bundleItem.item),
          });
        }
      }

      await BundleItemModel.schema(schemaName).bulkCreate(bundleItemsSql);
      console.log(`✅ Created ${bundleItemsSql.length} SQL bundle item records`);
    }

    return bundles;
  }
}
