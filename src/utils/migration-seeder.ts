import { TenantMigrationsManager } from '../modules/postgres-database';
import { migrationList } from './migration-assets/migration-list';

export async function runSeederMigrations(tenantId, sequelize) {
  const tenantMigrationsManager = new TenantMigrationsManager(sequelize);
  await tenantMigrationsManager.registerMigrations(migrationList);
  await tenantMigrationsManager.runMigrationsForTenant(tenantId);
}
