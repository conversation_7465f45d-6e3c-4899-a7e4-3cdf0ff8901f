import mongoose, { Types } from 'mongoose';
import { faker, fakerAR } from '@faker-js/faker';
import * as dotenv from 'dotenv';
import { User_Schema } from '../modules/users/users/schemas/user.schema';
import { Role_Schema } from '../modules/users/roles/schemas/role.schema';
import { branchSchema } from '../modules/users/branch/schemas/branch.schema';
import { companySchema } from '../modules/users/company/schemas/company.schema';
import { Permission_Schema } from '../modules/users/permissions/schema/permisstions.schema';
import { userBranchRoleSchema } from '../modules/users/user-branches-roles/schema/user-branches-roles.schema';
import {
  documentPolicy,
  documentPolicyStatus,
  level,
  pricingLevel,
} from '../modules/users/company/schemas/company-settings.schema';
import { PaymentTypeSchema } from '../modules/users/payment-types/schema/payment-type.schema';
import { PaymentTypesEnum } from '../modules/users/payment-types/types/payment-type.enum';
import { permList } from './assets/perm';
import { ServiceVersionSeeder } from './seeders/service-version-seeder';
import { PartnerSeeder } from './seeders/partner-seeder';
import { PurchaseSeeder } from './seeders/purchase-seeder';
import { SalesSeeder } from './seeders/sales-seeder';
import { SalesReturnSeeder } from './seeders/sales-return-seeder';
import { TemplateSeeder } from './seeders/template-seeder';
import { PolicySeeder } from './seeders/policy-seeder';
import { PartnerGroupSeeder } from './seeders/partner-group-seeder';
import { SalesRepresentativesSeeder } from './seeders/sales-representatives-seeder';
import { ExpensesSeeder } from './seeders/expenses-seeder';
import { ItemSeeder } from './seeders/item-seeder';
import { BundleSeeder } from './seeders/bundle-seeder';
import { ItemSnapshotSeeder } from './seeders/item-snapshot-seeder';
import { itemTypeEnum } from '../modules/inventory/inventory-item/enum/item-types.enum';
import seedItemExplorerFunc from './seeders/item-explorer-seeder';
import { PurchaseInventoryJournalSeeder } from './seeders/purchase-inventory-journal-seeder';
import { SaleInventoryJournalSeeder } from './seeders/sale-inventory-journal-seeder';
import { SaleReturnInventoryJournalSeeder } from './seeders/sale-return-inventory-journal-seeder';
import { storeSchema } from '../modules/inventory/store/schema/store.schema';
import { unitSchema } from '../modules/inventory/unit/schema/unit.schema';
import { itemGroupSchema } from '../modules/inventory/group-item/schema/item-group.schema';
import { GeneralJournalSeeder } from '../utils/seeders/general-journal-seeder';
import { ReceiptVoucherSeeder } from '../utils/seeders/receipt-voucher-seeder';
import { PartnerAccountingNodeSeeder } from '../utils/seeders/partner-accounting-node-seeder';
import { PurchaseGeneralLedgerSeeder } from '../utils/seeders/purchase-general-ledger-seeder';
import { SalesGeneralLedgerSeeder } from '../utils/seeders/sales-general-ledger-seeder';
import { SalesReturnGeneralLedgerSeeder } from '../utils/seeders/sales-return-general-ledger-seeder';
import {
  AccountingNode,
  accountingNodeSchema,
} from '../modules/accounting/accounting-node/schema/accounting-node.schema';
import { nodeSeeder, nodeSqlSeeder } from './seeders/accounting-node-seeder';
import { Sequelize } from 'sequelize-typescript';
import { PaymentTypeModel } from '../modules/users/payment-types/model/payment-type.model';
import { PermissionsModel } from '../modules/users/permissions/model/permissons.model';
import { PermissionPrivilegesTypeModel } from '../modules/users/permissions/model/permission-privileges.model';
import { seedPermSql } from './seeders/utils/seed-perm-sql';
import { PermissionsPermListModel } from '../modules/users/permissions/model/permission-privileages-list.model';
import { RolesModel } from '../modules/users/roles/model/roles.model';
import { RolesPermissionModel } from '../modules/users/roles/model/role-perm.model';
import { CompanyModel } from '../modules/users/company/model/company.model';
import { ContactPersonModel } from '../modules/shared/model/contact-person.model';
import { StoreModel } from '../modules/inventory/store/model/store.model';
import { BranchModel } from '../modules/users/branch/model/branch.model';
import { ItemExplorerModel } from '../modules/inventory/item-explorer/model/item-explorer.model';
import { BranchAccountModel } from '../modules/users/branch/model/branch-account.model';
import { ItemGroupModel } from '../modules/inventory/group-item/model/item-group.model';
import { ItemModel } from '../modules/inventory/inventory-item/model/item.model';
import { ItemExplorerUnitPricesModel } from '../modules/inventory/item-explorer/model/item-explorer-unit-prices.model';
import { ItemExplorerUnitCostsModel } from '../modules/inventory/item-explorer/model/item-explorer-unit-costs.model';
import { UnitModel } from '../modules/inventory/unit/model/unit.model';
import { BranchDocumentModel } from '../modules/users/branch/model/branch-document.model';
import { AccountingNodeModel } from '../modules/accounting/accounting-node/model/accounting-node.model';
import { PartnerModel } from '../modules/trade/partners/model/partner.model';
import { BeginningBalanceModel } from '../modules/accounting/temp-beginning-balance/model/beginning-balance.model';
import { GeneralLedgerTransactionModel } from '../modules/accounting/general-ledger/model/general-ledger-transaction.model';
import { GeneralLedgerModel } from '../modules/accounting/general-ledger/model/general-ledger.model';
import { UserModel } from '../modules/users/users/model/users.model';
import { UserOtpModel } from '../modules/users/users/model/user-otp.model';
import { UsersBranchesRolesModel } from '../modules/users/user-branches-roles/model/user-branch-roles.model';
import { RolesListModel } from '../modules/users/user-branches-roles/model/role-list.model';
import { ItemUnitPriceModel } from '../modules/inventory/inventory-item/model/item-unit-price.model';
import { CompanySnapshotUnitCostModel } from '../modules/inventory/snapshot/model/company-snapshot-unit-cost.model';
import { BranchSnapshotUnitCostModel } from '../modules/inventory/snapshot/model/branch-snapshot-unit-cost.model';
import { StoreSnapshotUnitCostModel } from '../modules/inventory/snapshot/model/store-snapshot-unit-cost.model';
import { CompanySnapshotModel } from '../modules/inventory/snapshot/model/company-snapshot.model';
import { BranchSnapshotModel } from '../modules/inventory/snapshot/model/branch-snapshot.model';
import { StoreSnapshotModel } from '../modules/inventory/snapshot/model/store-snapshot.model';
import { ItemBranchModel } from '../modules/inventory/inventory-item/model/item-branch.model';
import { ItemBranchUnitPriceModel } from '../modules/inventory/inventory-item/model/item-branch-unit-price.model';
import { InventoryJournalModel } from '../modules/inventory/inventory-journal/model/inventory-journal.model';
import { runSeederMigrations } from './migration-seeder';
import { PartnerGroupModel } from '../modules/trade/partner-group/model/partner-group.model';
import { NationalAddressModel } from '../modules/shared/model/national-address.model';
import { GeneralJournalModel } from '../modules/accounting/general-journal/model/general-journal.model';
import { PartnerLedgerTransactionModel } from '../modules/accounting/general-ledger/model/partner-ledger-transaction.model';
import { GeneralJournalTransactionModel } from '../modules/accounting/general-journal/model/general-journal-transaction.model';
import { TemplateDesignModel } from '../modules/trade/template-design/model/template-design.model';
import { SalesRepresentativeModel } from '../modules/trade/sales-representative/model/sales-representative.model';
import { ExpenseModel } from '../modules/trade/expense/model/expense.model';
import { BundleModel } from '../modules/inventory/bundle/model/bundle.model';
import { BundleItemModel } from '../modules/inventory/bundle/model/bundle-item.model';

dotenv.config({});
// write the following string to console in yollow font use --drop to drop the database --tenant 1 seed tenant_1 database
console.log(
  '\x1b[33m%s\x1b[0m',
  'you can use the following arguments "npm run seed -- --tenant 1 --drop"',
);
console.log('\x1b[33m%s\x1b[0m', '--drop          drops tenant_id database ');
console.log('\x1b[33m%s\x1b[0m', '--tenant id     seeds tenant_id database');
console.log('\x1b[33m%s\x1b[0m', '--multi     seeds tenants 0 to 3');
console.log(process.env.POSTGRES_SSL);

mongoose.set('strictQuery', false);

async function tenantSeed(tenantId: number) {
  const chairsGroupId = new mongoose.Types.ObjectId();
  const tablesGroupId = new mongoose.Types.ObjectId();
  const drinksGroupId = new mongoose.Types.ObjectId();
  const spicesGroupId = new mongoose.Types.ObjectId();
  const plasticGroupId = new mongoose.Types.ObjectId();
  const washGroupId = new mongoose.Types.ObjectId();
  const repairGroupId = new mongoose.Types.ObjectId();

  const branches = [
    new mongoose.Types.ObjectId('64956ef5f506e9edf21df4e8'),
    new mongoose.Types.ObjectId('64956ef5f506e9edf21df4e9'),
  ];

  const storeId = [
    new mongoose.Types.ObjectId('64ad000ab16fbc375c9649de'),
    new mongoose.Types.ObjectId('64956ef5f506e9edf21df4e9'),
    new mongoose.Types.ObjectId('64ad000ab16fbc375c9649df'),
    new mongoose.Types.ObjectId('64956ef5f506e9edf21df4ea'),
  ];

  const unitIds = {
    kilogram: new mongoose.Types.ObjectId(),
    gram: new mongoose.Types.ObjectId(),
    piece: new mongoose.Types.ObjectId('64acf899b16fbc375c96498e'),
    ten_pic: new mongoose.Types.ObjectId(),
    dozen: new mongoose.Types.ObjectId(),
    box_twenty_pic: new mongoose.Types.ObjectId(),
    box_hundred_pic: new mongoose.Types.ObjectId(),
    roll: new mongoose.Types.ObjectId(),
    bag_three_hundred_gram: new mongoose.Types.ObjectId(),
    basic: new mongoose.Types.ObjectId(),
    medium: new mongoose.Types.ObjectId(),
    premium: new mongoose.Types.ObjectId(),
    one_person: new mongoose.Types.ObjectId(),
    more_than_one_person: new mongoose.Types.ObjectId(),
  };

  const storesList = [
    {
      _id: storeId[0],
      number: 1,
      'name.ar': 'المخزن الافتراضي',
      'name.en': 'default store',
      branch: '64956ef5f506e9edf21df4e8',
      phone: '+201011111111',
      is_default: true,
    },
    {
      _id: storeId[1],
      number: 2,
      'name.ar': 'المخزن الخلفي',
      'name.en': 'back store',
      branch: '64956ef5f506e9edf21df4e8',
      phone: '+201011111111',
      is_default: false,
    },
    {
      _id: storeId[2],
      number: 1,
      'name.ar': ' الفرع الثاني - المخزن الافتراضي',
      'name.en': 'default store - Second branch',
      branch: '64956ef5f506e9edf21df4e9',
      phone: '+201011111111',
      is_default: true,
    },
    {
      _id: storeId[3],
      number: 2,
      'name.ar': ' الفرع الثاني - المخزن الخلفي',
      'name.en': 'back store - Second branch',
      branch: '64956ef5f506e9edf21df4e9',
      phone: '+201011111111',
      is_default: false,
    },
  ];
  const tenantUrl = new URL(
    (process.env.PREFIX_DB || '') + 'tenant_' + tenantId.toString(),
    process.env.DB_URL,
  );
  tenantUrl.searchParams.set('authSource', 'admin');
  const tenantDb = mongoose.createConnection(tenantUrl.href);
  const sequelize = new Sequelize({
    dialect: 'postgres',
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
    username: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DB || 'invoice_portal',
    logging: process.env.POSTGRES_LOGGING === 'true',
    models: [
      PermissionsPermListModel,
      PermissionPrivilegesTypeModel,
      PermissionsModel,
      PaymentTypeModel,
      RolesModel,
      RolesPermissionModel,
      CompanyModel,
      ContactPersonModel,
      StoreModel,
      BranchModel,
      BranchDocumentModel,
      ItemExplorerModel,
      BranchAccountModel,
      ItemGroupModel,
      ItemModel,
      ItemExplorerUnitPricesModel,
      ItemExplorerUnitCostsModel,
      UnitModel,
      PartnerModel,
      AccountingNodeModel,
      BeginningBalanceModel,
      GeneralLedgerTransactionModel,
      GeneralLedgerModel,
      UserModel,
      UserOtpModel,
      UsersBranchesRolesModel,
      RolesListModel,
      ItemUnitPriceModel,
      CompanySnapshotUnitCostModel,
      BranchSnapshotUnitCostModel,
      StoreSnapshotUnitCostModel,
      CompanySnapshotModel,
      BranchSnapshotModel,
      StoreSnapshotModel,
      ItemBranchModel,
      ItemBranchUnitPriceModel,
      InventoryJournalModel,
      PartnerGroupModel,
      NationalAddressModel,
      GeneralJournalModel,
      PartnerLedgerTransactionModel,
      GeneralJournalTransactionModel,
      TemplateDesignModel,
      SalesRepresentativeModel,
      ExpenseModel,
      BundleModel,
      BundleItemModel,
    ],
    // eslint-disable-next-line @typescript-eslint/naming-convention
    dialectOptions:
      process.env.POSTGRES_SSL === 'true'
        ? {
            ssl: {
              require: true,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              rejectUnauthorized: false,
            },
          }
        : {},
  });

  // await sequelize.sync({ force: true }); // force: true drops and recreates

  await sequelize.authenticate();
  console.log('✅ PostgreSQL connection established successfully.');

  console.log('📦 Initializing tenant seeding...');

  const schemaName = `tenant_${tenantId}`;

  await sequelize.dropSchema(schemaName, { logging: false });

  await sequelize.createSchema(schemaName, { logging: false });
  await runSeederMigrations(tenantId, sequelize);
  // dont touch the order for sake of fk
  /*   await UserModel.schema(schemaName).sync({ force: true });
  await UserOtpModel.schema(schemaName).sync({ force: true });
  await PermissionsModel.schema(schemaName).sync({ force: true });
  await PermissionPrivilegesTypeModel.schema(schemaName).sync({ force: true });
  await PermissionsPermListModel.schema(schemaName).sync({ force: true });
  await RolesModel.schema(schemaName).sync({ force: true });
  await RolesPermissionModel.schema(schemaName).sync({ force: true });
  await UsersBranchesRolesModel.schema(schemaName).sync({ force: true });
  await RolesListModel.schema(schemaName).sync({ force: true });
  await ContactPersonModel.schema(schemaName).sync({ force: true });
  await CompanyModel.schema(schemaName).sync({ force: true });
  await BranchModel.schema(schemaName).sync({ force: true });
  await BranchDocumentModel.schema(schemaName).sync({ force: true });
  await BranchAccountModel.schema(schemaName).sync({ force: true });
  await ItemModel.schema(schemaName).sync({ force: true });
  await ItemGroupModel.schema(schemaName).sync({ force: true });
  await UnitModel.schema(schemaName).sync({ force: true });
  await ItemUnitPriceModel.schema(schemaName).sync({ force: true });
  await StoreModel.schema(schemaName).sync({ force: true });
  await ItemExplorerModel.schema(schemaName).sync({ force: true });
  await ItemExplorerUnitPricesModel.schema(schemaName).sync({ force: true });
  await ItemExplorerUnitCostsModel.schema(schemaName).sync({ force: true });
  await AccountingNodeModel.schema(schemaName).sync({ force: true });
  await PaymentTypeModel.schema(schemaName).sync({ force: true });
  await PartnerModel.schema(schemaName).sync({ force: true });
  await InventoryJournalModel.schema(schemaName).sync({ force: true });
  await CompanySnapshotModel.schema(schemaName).sync({ force: true });
  await CompanySnapshotUnitCostModel.schema(schemaName).sync({ force: true });
  await BranchSnapshotModel.schema(schemaName).sync({ force: true });
  await BranchSnapshotUnitCostModel.schema(schemaName).sync({ force: true });
  await StoreSnapshotModel.schema(schemaName).sync({ force: true });
  await StoreSnapshotUnitCostModel.schema(schemaName).sync({ force: true });
  await GeneralLedgerModel.schema(schemaName).sync({ force: true });
  await GeneralLedgerTransactionModel.schema(schemaName).sync({ force: true });
  await BeginningBalanceModel.schema(schemaName).sync({ force: true });
  await ItemBranchModel.schema(schemaName).sync({ force: true });
  await ItemBranchUnitPriceModel.schema(schemaName).sync({ force: true }); */
  // Show the current search path

  // -----------------------------------temprory until migration fixed--------------------------------------------
  await NationalAddressModel.schema(schemaName).sync({ force: true });
  await TemplateDesignModel.schema(schemaName).sync({ force: true });
  await PartnerGroupModel.schema(schemaName).sync({ force: true });
  await PartnerModel.schema(schemaName).sync({ force: true });
  await SalesRepresentativeModel.schema(schemaName).sync({ force: true });
  await ExpenseModel.schema(schemaName).sync({ force: true });
  // -----------------------------------temprory until migration fixed--------------------------------------------

  await sequelize.query('SHOW search_path');

  //check all arguments the argument if it --drop exist drop the table. if the database is droped write database droped in red color
  if (process.argv.includes('--drop')) {
    await tenantDb.dropDatabase();
    console.log(
      '\x1b[31m%s\x1b[0m',
      `Tenant ${tenantId} database has been droped`,
    );
    //commended cuz we use sync
    // await sequelize.dropAllSchemas({});
  }
  const i = tenantId;
  const roleModel = tenantDb.model('role', Role_Schema);
  const permissionModel = tenantDb.model('permission', Permission_Schema);
  const paymentTypesModel = tenantDb.model('paymenttypes', PaymentTypeSchema);
  const branchModel = tenantDb.model('branch', branchSchema);
  const userModel = tenantDb.model('user', User_Schema);
  const userBeanchRolesModel = tenantDb.model(
    'userbranchroles',
    userBranchRoleSchema,
  );
  const companyModel = tenantDb.model('company', companySchema);

  const permissions = await permissionModel.insertMany(permList);

  const permissionIds = permissions.map((permission) => permission._id);
  await seedPermSql(permList, permissionIds, schemaName);
  const permsIdSqlList = await PermissionsModel.schema(schemaName).findAll({
    attributes: ['id'],
    raw: true,
  });

  const roleModelData = await roleModel.insertMany([
    {
      name: 'admin' + i,
      permissions: permissionIds,
    },
    {
      name: 'salesman' + i,
      permissions: permissionIds,
    },
    {
      name: 'cashier' + i,
      permissions: permissionIds,
    },
  ]);

  await RolesModel.schema(schemaName).bulkCreate([
    {
      mongo_id: String(roleModelData[0]._id),
      name: 'admin' + i,
    },
    {
      mongo_id: String(roleModelData[1]._id),
      name: 'salesman' + i,
    },
    {
      mongo_id: String(roleModelData[2]._id),
      name: 'cashier' + i,
    },
  ]);

  for (let index = 0; index < permsIdSqlList.length; index++) {
    const element = permsIdSqlList[index];
    await RolesPermissionModel.schema(schemaName).bulkCreate([
      {
        permission_id: element.id,
        role_id: 1,
      },
      {
        permission_id: element.id,
        role_id: 2,
      },
      {
        permission_id: element.id,
        role_id: 3,
      },
    ]);
  }

  const serviceVersionSeeder = new ServiceVersionSeeder(tenantDb);
  await serviceVersionSeeder.seed();

  const companyDoc = await companyModel.create({
    name: {
      en: 'Osus For Computer Systems',
      ar: 'شركة اسس النظم والحاسب الالى لتقنية المعلومات',
    },
    short_name: 'OSUS',
    settings: {
      pricing_level: pricingLevel.company,
      average_cost_level: level.store,
      document_policy: documentPolicy.adjustment,
      document_policy_status: documentPolicyStatus.enable,
    },
  });
  // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars
  const { name, contact_person, national_address, settings, _id } = companyDoc;
  console.log(national_address, contact_person);

  await CompanyModel.schema(schemaName).create(
    {
      name_en: name.en,
      name_ar: name.ar,
      short_name: 'OSUS',
      pricing_level: pricingLevel.company,
      average_cost_level: level.store,
      document_policy: documentPolicy.adjustment,
      document_policy_status: documentPolicyStatus.enable,
      //national_address: national_address,
      //contact_person: contact_person,
      mongo_id: String(_id),
    },
    {
      include: [NationalAddressModel, ContactPersonModel],
    },
  );

  //accounting
  tenantDb.model(AccountingNode.name, accountingNodeSchema);
  await tenantDb.models.AccountingNode.insertMany(nodeSeeder(undefined));
  const sqlNode = nodeSqlSeeder(undefined) as any;

  await AccountingNodeModel.schema(schemaName).bulkCreate(sqlNode);
  //// end accounting (needed for branch)

  await PaymentTypeModel.schema(schemaName).bulkCreate([
    {
      mongo_id: '6501b9f946ad8225ab76cb92',
      name_en: 'cash',
      name_ar: 'نقدي',
      code: '1',
      payment_account_id: 16,
      type: PaymentTypesEnum.cash,
    },
    {
      mongo_id: '6501b9f946ad8225ab76cba3',
      name_ar: 'بطاقة ائتمان',
      name_en: 'credit card',
      payment_account_id: 16,
      code: '2',
      type: PaymentTypesEnum.bank_card,
    },
    {
      mongo_id: '6501b9f946ad8225ab76cbba',
      name_en: 'bank transfer',
      name_ar: 'تحويل بنكي',
      payment_account_id: 16,
      code: '3',
      type: PaymentTypesEnum.bank_accounts,
    },
    {
      mongo_id: '6501b9f946ad8225ab76cbad',
      name_en: 'other',
      name_ar: 'أخرى',
      payment_account_id: 16,
      code: '4',
      type: PaymentTypesEnum.others,
    },
  ]);

  await paymentTypesModel.create([
    {
      _id: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cb92'),
      name: {
        en: 'cash',
        ar: 'نقدي',
      },
      code: '1',
      payment_account: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cb84'),
      type: PaymentTypesEnum.cash,
    },
    {
      _id: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cba3'),
      name: {
        en: 'credit card',
        ar: ' بطاقة ائتمان',
      },
      payment_account: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cb84'),
      code: '2',
      type: PaymentTypesEnum.bank_card,
    },
    {
      _id: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cbba'),
      name: {
        en: 'bank transfer',
        ar: 'تحويل بنكي',
      },
      payment_account: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cb84'),
      code: '3',
      type: PaymentTypesEnum.bank_accounts,
    },
    {
      _id: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cbad'),
      name: {
        en: 'other',
        ar: 'أخرى',
      },
      payment_account: new mongoose.Types.ObjectId('6501b9f946ad8225ab76cb84'),
      code: '4',
      type: PaymentTypesEnum.others,
    },
  ]);

  const branchlist = [
    {
      _id: new mongoose.Types.ObjectId('64956ef5f506e9edf21df4e8'),
      general_information: {
        code: '1',
        fax: '',
        email: '',
        phone: faker.helpers.replaceSymbolWithNumber('+9665########'),
        activation_status: true,
        name: {
          en: faker.company.name(),
          ar: fakerAR.company.name(),
        },
        tax_code: faker.string.numeric(15),
        registration_number: faker.string.numeric(10),
        is_default: true,
        is_main_branch: false,
      },
      national_address: {
        commercial_activities: faker.commerce.department(),
        trade_name: {
          en: faker.commerce.department(),
          ar: fakerAR.commerce.department(),
        },
        short_address: faker.string.alpha(3),
        building_no: faker.string.numeric(4),
        street: faker.location.street(),
        secondary_no: faker.string.numeric(3),
        district: faker.location.state(),
        postal_code: faker.string.numeric(5),
        city: faker.location.city(),
        governorate: faker.location.state(),
      },
      settings: {
        general: {
          allow_documents_deletion: true,
          allow_documents_edition: {
            enabled: true,
            type: 'unrestricted-editing',
          },
          use_multi_stores: false,
        },
        global_accounts: {
          tax_accounts: {
            purchase_tax_account: '6501b9f946ad8225ab76cb92',
            sales_tax_account: '6501b9f946ad8225ab76cb9e',
            payment_commission_tax_account: '6501b9f946ad8225ab76cb93',
          },
          sales_accounts: {
            cash_sales_account: '6501b9f946ad8225ab76cbba',
            credit_sales_account: '6501b9f946ad8225ab76cbbb',
            cash_sales_return_account: '6501b9f946ad8225ab76cbbc',
            credit_sales_return_account: '6501b9f946ad8225ab76cbbd',
            sales_discount_account: '6501b9f946ad8225ab76cbbe',
            customers_deposits_account: '6501b9f946ad8225ab76cbd9',
          },
          cash_and_bank: {
            cash_account: '6501b9f946ad8225ab76cb7f',
            bank_account: '6501b9f946ad8225ab76cb83',
            additional_expense_account: '',
            cash_invoices_under_settlement: '',
            profit_account: '6501b9f946ad8225ab76cba3',
          },
          sub_accounts: {
            cash_sales_commission_account: '',
            credit_sales_commission_account: '',
            levy_commission_account: '',
            customer_group_account: '6501b9f946ad8225ab76cb85',
            vendor_group_account: '6501b9f946ad8225ab76cb97',
          },
          other_accounts: {
            transfer_account: '',
            adjustment_account: '6501b9f946ad8225ab76cbb6',
            inventory_account: '6501b9f946ad8225ab76cb8e',
            beginning_balance_account: '6501b9f946ad8225ab76cba7',
            ending_balance_account: '',
            cost_of_sales_account: '6501b9f946ad8225ab76cbb5',
            cost_of_purchase_account: '',
          },
          purchase_accounts: {
            cash_purchase_account: '6501b9f946ad8225ab76cba9',
            credit_purchase_account: '6501b9f946ad8225ab76cbaa',
            cash_purchase_return_account: '6501b9f946ad8225ab76cbab',
            credit_purchase_return_account: '6501b9f946ad8225ab76cbac',
            purchase_discount_account: '6501b9f946ad8225ab76cbad',
            deferred_discount_account: '',
          },
        },
      },
      document: {
        sales_invoices: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },

        sales_return: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        purchases_invoices: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        purchase_return: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        transfer_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        quotation: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        reservation: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        preparation: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        journal: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        payment_voucher_cash: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        receipt_voucher: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        debit_memo: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        credit_memo: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        service_invoice_customer: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        service_invoice_vendors: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        purchase_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        store_transactions_and_adjustments: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        import_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        prepare_purchase_entry: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        transfer_receive: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        quotation_order_and_comp: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        physical_inventory_voucher: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        physical_inventory_posting: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        sales_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        purchase_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        issue_permission: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        addition_permission: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        work_orders: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        entry_permission: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        release_permission: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        work_order_production: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        transfer_preparing_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        production_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        quotation_request_p: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        cargo_manifest: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        cargo_manifest_invoice: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
        sales_order: {
          auto_serial: true,
          same_serial: true,
          default_unit_as_biggest: false,
        },
      },
      dates: {
        year_beginning_date: new Date('2024-01-01').toISOString(),
        year_ending_date: new Date('2024-12-31').toISOString(),
      },
    },

    {
      _id: new mongoose.Types.ObjectId('64956ef5f506e9edf21df4e9'),
      general_information: {
        code: '2',
        fax: '',
        email: '',
        phone: faker.helpers.replaceSymbolWithNumber('+9665########'),
        activation_status: true,
        name: {
          en: faker.company.name(),
          ar: fakerAR.company.name(),
        },
        tax_code: faker.string.numeric(15),
        registration_number: faker.string.numeric(10),
        is_default: true,
        is_main_branch: false,
      },
      national_address: {
        commercial_activities: faker.commerce.department(),
        trade_name: {
          en: faker.commerce.department(),
          ar: fakerAR.commerce.department(),
        },
        short_address: faker.string.alpha(3),
        building_no: faker.string.numeric(4),
        street: faker.location.street(),
        secondary_no: faker.string.numeric(3),
        district: faker.location.state(),
        postal_code: faker.string.numeric(5),
        city: faker.location.city(),
        governorate: faker.location.state(),
      },
      settings: {
        general: {
          allow_documents_deletion: true,
          allow_documents_edition: {
            enabled: true,
            type: 'unrestricted-editing',
          },
          use_multi_stores: false,
        },
        global_accounts: {
          tax_accounts: {
            purchase_tax_account: '6501b9f946ad8225ab76cb92',
            sales_tax_account: '6501b9f946ad8225ab76cb9e',
            payment_commission_tax_account: '6501b9f946ad8225ab76cb93',
          },
          sales_accounts: {
            cash_sales_account: '6501b9f946ad8225ab76cbba',
            credit_sales_account: '6501b9f946ad8225ab76cbbb',
            cash_sales_return_account: '6501b9f946ad8225ab76cbbc',
            credit_sales_return_account: '6501b9f946ad8225ab76cbbd',
            sales_discount_account: '6501b9f946ad8225ab76cbbe',
            customers_deposits_account: '6501b9f946ad8225ab76cbd9',
          },
          cash_and_bank: {
            cash_account: '6501b9f946ad8225ab76cb7f',
            bank_account: '6501b9f946ad8225ab76cb83',
            additional_expense_account: '',
            cash_invoices_under_settlement: '',
            profit_account: '6501b9f946ad8225ab76cba3',
          },
          sub_accounts: {
            cash_sales_commission_account: '',
            credit_sales_commission_account: '',
            levy_commission_account: '',
            customer_group_account: '6501b9f946ad8225ab76cb85',
            vendor_group_account: '6501b9f946ad8225ab76cb97',
          },
          other_accounts: {
            transfer_account: '',
            adjustment_account: '6501b9f946ad8225ab76cbb6',
            inventory_account: '6501b9f946ad8225ab76cb8e',
            beginning_balance_account: '',
            ending_balance_account: '',
            cost_of_sales_account: '6501b9f946ad8225ab76cbb5',
            cost_of_purchase_account: '',
          },
          purchase_accounts: {
            cash_purchase_account: '6501b9f946ad8225ab76cba9',
            credit_purchase_account: '6501b9f946ad8225ab76cbaa',
            cash_purchase_return_account: '6501b9f946ad8225ab76cbab',
            credit_purchase_return_account: '6501b9f946ad8225ab76cbac',
            purchase_discount_account: '6501b9f946ad8225ab76cbad',
            deferred_discount_account: '',
          },
        },
      },
      document: {
        sales_invoices: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 100,
          default_unit_as_biggest: false,
        },
        sales_return: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 100,
          default_unit_as_biggest: false,
        },
        purchases_invoices: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 100,
          default_unit_as_biggest: false,
        },
        purchase_return: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        reservation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        preparation: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        journal: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 100,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        payment_voucher_cash: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        receipt_voucher: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        debit_memo: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        credit_memo: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        service_invoice_customer: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        service_invoice_vendors: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        purchase_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        store_transactions_and_adjustments: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        import_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        prepare_purchase_entry: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_receive: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation_order_and_comp: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        physical_inventory_voucher: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        physical_inventory_posting: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        sales_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        purchase_receipts_consignment: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        issue_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        addition_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        work_orders: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        entry_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        release_permission: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        work_order_production: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        transfer_preparing_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        production_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        quotation_request_p: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        cargo_manifest: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        cargo_manifest_invoice: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
        sales_order: {
          auto_serial: true,
          same_serial: true,
          cash_serial: 0,
          credit_serial: 0,
          default_unit_as_biggest: false,
        },
      },
      dates: {
        year_beginning_date: new Date('2024-01-01').toISOString(),
        year_ending_date: new Date('2024-12-31').toISOString(),
      },
    },
  ];
  const branchesData = await branchModel.create(branchlist);
  await BranchModel.schema(schemaName).bulkCreate([
    {
      name_ar: branchlist[0].general_information.name.ar,
      name_en: branchlist[0].general_information.name.en,
      tax_code: branchlist[0].general_information.tax_code,
      registration_number:
        branchlist[0].general_information.registration_number,
      phone: branchlist[0].general_information.phone,
      fax: branchlist[0].general_information.fax,
      email: branchlist[0].general_information.email,
      //address:
      activation_status: branchlist[0].general_information.activation_status,
      year_beginning_date: branchlist[0].dates.year_beginning_date,
      year_ending_date: branchlist[0].dates.year_ending_date,
      is_main_branch: branchlist[0].general_information.is_main_branch,
      override_profit_margin: false,
      allow_price_under_cost: branchlist[0].general_information.is_main_branch,
      allow_custom_price: false,
      allow_documents_deletion:
        branchlist[0].settings.general.allow_documents_deletion,
      allow_documents_edition_enabled:
        branchlist[0].settings.general.allow_documents_edition.enabled,
      allow_documents_edition_type:
        branchlist[0].settings.general.allow_documents_edition.type,

      //  allow_documents_edition_parameter:
      use_multi_stores: branchlist[0].settings.general.use_multi_stores,
      mongo_id: String(branchesData[0]._id),
    },
    {
      name_ar: branchlist[1].general_information.name.ar,
      name_en: branchlist[1].general_information.name.en,
      tax_code: branchlist[1].general_information.tax_code,
      registration_number:
        branchlist[1].general_information.registration_number,
      phone: branchlist[1].general_information.phone,
      fax: branchlist[1].general_information.fax,
      email: branchlist[1].general_information.email,
      //address:
      activation_status: branchlist[1].general_information.activation_status,
      year_beginning_date: branchlist[1].dates.year_beginning_date,
      year_ending_date: branchlist[1].dates.year_ending_date,
      is_main_branch: branchlist[1].general_information.is_main_branch,
      override_profit_margin: false,
      allow_price_under_cost: branchlist[1].general_information.is_main_branch,
      allow_custom_price: false,
      allow_documents_deletion:
        branchlist[1].settings.general.allow_documents_deletion,
      allow_documents_edition_enabled:
        branchlist[1].settings.general.allow_documents_edition.enabled,
      allow_documents_edition_type:
        branchlist[1].settings.general.allow_documents_edition.type,

      use_multi_stores: branchlist[1].settings.general.use_multi_stores,
      mongo_id: String(branchesData[1]._id),
    },
  ]);

  await BranchAccountModel.schema(schemaName).bulkCreate([
    {
      branch_id: 1,
      cash_sales_account: 61,
      credit_sales_account: 62,

      cash_sales_return_account: 63,

      credit_sales_return_account: 64,

      sales_discount_account: 65,

      customers_deposits_account: 32,

      cash_purchase_account: 44,

      credit_purchase_account: 45,

      cash_purchase_return_account: 46,

      credit_purchase_return_account: 47,

      purchase_discount_account: 48,

      //  deferred_discount_account:

      cash_account: 11,

      bank_account: 15,

      //  additional_expense_account:

      // cash_invoices_under_settlement:

      profit_account: 38,

      // cash_sales_commission_account:

      //  credit_sales_commission_account:

      //   levy_commission_account:

      customer_group_account: 17,

      vendor_group_account: 29,

      purchase_tax_account: 24,

      sales_tax_account: 33,

      payment_commission_tax_account: 25,

      //transfer_account:

      adjustment_account: 57,

      inventory_account: 20,

      beginning_balance_account: 42,

      //    ending_balance_account:

      cost_of_sales_account: 56,

      //cost_of_purchase_account:
    },
    {
      branch_id: 2,
      cash_sales_account: 61,
      credit_sales_account: 62,

      cash_sales_return_account: 63,

      credit_sales_return_account: 64,

      sales_discount_account: 65,

      customers_deposits_account: 32,

      cash_purchase_account: 44,

      credit_purchase_account: 45,

      cash_purchase_return_account: 46,

      credit_purchase_return_account: 47,

      purchase_discount_account: 48,

      //  deferred_discount_account:

      cash_account: 11,

      bank_account: 15,

      //  additional_expense_account:

      // cash_invoices_under_settlement:

      profit_account: 38,

      // cash_sales_commission_account:

      //  credit_sales_commission_account:

      //   levy_commission_account:

      customer_group_account: 17,

      vendor_group_account: 29,

      purchase_tax_account: 24,

      sales_tax_account: 33,

      payment_commission_tax_account: 25,

      //transfer_account:

      adjustment_account: 57,

      inventory_account: 20,

      beginning_balance_account: 42,

      //    ending_balance_account:

      cost_of_sales_account: 56,

      //cost_of_purchase_account:
    },
  ]);
  const parsedDocumentsB1 = Object.entries(branchlist[0].document).map(
    ([feature_name, settings]) => ({
      branch_id: 1,
      feature_name,
      auto_serial: settings.auto_serial,
      same_serial: settings.same_serial,
      cash_serial: settings.cash_serial,
      credit_serial: settings.credit_serial,
      default_unit_as_biggest: settings.default_unit_as_biggest,
      created_at: new Date(),
      updated_at: new Date(),
    }),
  );
  const parsedDocumentsB2 = Object.entries(branchlist[1].document).map(
    ([feature_name, settings]) => ({
      branch_id: 2,
      feature_name,
      auto_serial: settings.auto_serial,
      same_serial: settings.same_serial,
      cash_serial: settings.cash_serial,
      credit_serial: settings.credit_serial,
      default_unit_as_biggest: settings.default_unit_as_biggest,
      created_at: new Date(),
      updated_at: new Date(),
    }),
  );
  await BranchDocumentModel.schema(schemaName).bulkCreate([
    ...parsedDocumentsB1,
    ...parsedDocumentsB2,
  ]);

  const roles = roleModelData.map((role) => role._id);
  const branchesIds = branchesData.map((branch) => branch._id);

  const userModelData = await userModel.create({
    _id: new Types.ObjectId('65128e387d6e377e37b40a6c'),
    name: 'مسؤول النظام',
    password: 'a123456789',
    email: 'test' + i + '@test.com',
    notes: faker.lorem.paragraph(),
    email_verified: true,
    positions: [{ roles: roles, branches: branchesIds }],
    active: true,
  });
  await UserModel.schema(schemaName).create({
    name: 'مسؤول النظام',

    password: 'a123456789',

    email: 'test' + i + '@test.com',

    email_verified: true,

    active: true,
    mongo_id: String(userModelData._id),
  });

  const userBranchRoleData = [];
  branchesData.forEach((branch) => {
    userBranchRoleData.push({
      user: userModelData._id,
      branch: branch._id,
      roles: roles,
    });
  });
  const userBeanchRolesModelRs =
    await userBeanchRolesModel.insertMany(userBranchRoleData);

  await UsersBranchesRolesModel.schema(schemaName).bulkCreate([
    {
      user_id: 1,
      branch_id: 1,
      mongo_id: String(userBeanchRolesModelRs[0]._id),
    },
    {
      user_id: 1,
      branch_id: 2,
      mongo_id: String(userBeanchRolesModelRs[1]._id),
    },
  ]);
  await RolesListModel.schema(schemaName).bulkCreate([
    {
      user_branch_role_id: 1,
      role_id: 1,
    },
    {
      user_branch_role_id: 1,
      role_id: 2,
    },
    {
      user_branch_role_id: 1,
      role_id: 3,
    },
    {
      user_branch_role_id: 2,
      role_id: 1,
    },
    {
      user_branch_role_id: 2,
      role_id: 2,
    },
    {
      user_branch_role_id: 2,
      role_id: 3,
    },
  ]);
  //trade seeder

  if (
    process.argv.includes('--stress-seeding') &&
    !process.argv.includes('--drop')
  ) {
    console.log('stress seeding');

    //trade
    const partnerSeeder = new PartnerSeeder(tenantDb);
    await partnerSeeder.stressSeeding();

    const serviceVersionSeeder = new ServiceVersionSeeder(tenantDb);
    await serviceVersionSeeder.seed();

    const purchaseSeeder = new PurchaseSeeder(tenantDb);
    await purchaseSeeder.stressSeeding();

    const salesSeeder = new SalesSeeder(tenantDb);
    const sales = await salesSeeder.stressSeeding();

    const salesReturnSeeder = new SalesReturnSeeder(tenantDb, sales);
    await salesReturnSeeder.stressSeeding();

    //inventory
    const itemSeeder = new ItemSeeder(tenantDb);
    const items = await itemSeeder.stressSeeding(
      chairsGroupId,
      unitIds,
      branches,
    );

    const itemSnapshotSeeder = new ItemSnapshotSeeder(tenantDb);
    const itemData = items.filter((item) => item.type === itemTypeEnum.goods);
    await itemSnapshotSeeder.seed(itemData, branches, storesList);
    await seedItemExplorerFunc(tenantDb);
    const purchaseInventoryJournalSeeder = new PurchaseInventoryJournalSeeder(
      tenantDb,
      tenantId,
    );
    await purchaseInventoryJournalSeeder.stressSeeding();

    const saleInventoryJournalSeeder = new SaleInventoryJournalSeeder(tenantDb);
    await saleInventoryJournalSeeder.stressSeeding();

    const saleReturnInventoryJournalSeeder =
      new SaleReturnInventoryJournalSeeder(tenantDb);
    await saleReturnInventoryJournalSeeder.stressSeeding();

    //accounting
    const generalJournalSeeder = new GeneralJournalSeeder(tenantDb);
    await generalJournalSeeder.stressSeeding();

    const receiptVoucherSeeder = new ReceiptVoucherSeeder(tenantDb);
    await receiptVoucherSeeder.stressSeeding();

    const partnerAccountingNodeSeeder = new PartnerAccountingNodeSeeder(
      tenantDb,
    );
    await partnerAccountingNodeSeeder.stressSeeding();

    const purchaseAccountingNodeSeeder = new PurchaseGeneralLedgerSeeder(
      tenantDb,
    );
    await purchaseAccountingNodeSeeder.stressSeeding();

    const salesAccountingNodeSeeder = new SalesGeneralLedgerSeeder(tenantDb);
    await salesAccountingNodeSeeder.stressSeeding();

    const salesReturnAccountingNodeSeeder = new SalesReturnGeneralLedgerSeeder(
      tenantDb,
    );
    await salesReturnAccountingNodeSeeder.stressSeeding();

    await tenantDb.close();
    return;
  }

  if (
    process.argv.includes('--only-templates') &&
    !process.argv.includes('--drop')
  ) {
    console.log('reseeding templates');
    await tenantDb.dropCollection('templatedesigns');
    const templateSeeder = new TemplateSeeder(tenantDb);
    await templateSeeder.seed(schemaName);

    await tenantDb.close();
    return;
  }

  if (
    process.argv.includes('--only-policies') &&
    !process.argv.includes('--drop')
  ) {
    console.log('reseeding policies');
    await tenantDb.dropCollection('policies');
    const policySeeder = new PolicySeeder(tenantDb);
    await policySeeder.seed();
    await tenantDb.close();
    return;
  }

  const partnerGroupSeeder = new PartnerGroupSeeder(tenantDb);
  await partnerGroupSeeder.seed(schemaName);
  const partnerSeeder = new PartnerSeeder(tenantDb);
  await partnerSeeder.seed(schemaName);
  const salesRepresentativesSeeder = new SalesRepresentativesSeeder(tenantDb);
  await salesRepresentativesSeeder.seed(schemaName);
  const expensesSeeder = new ExpensesSeeder(tenantDb);
  await expensesSeeder.seed(schemaName);
  const policySeeder = new PolicySeeder(tenantDb);
  await policySeeder.seed();
  const templateSeeder = new TemplateSeeder(tenantDb);
  await templateSeeder.seed(schemaName);

  //inventory

  const storeMongoModel = tenantDb.model('stores', storeSchema);

  const unitMongoModel = tenantDb.model('units', unitSchema);
  const itemGroupModel = tenantDb.model('itemgroups', itemGroupSchema);

  // store creation
  const storeModelRs = await storeMongoModel.insertMany(storesList);
  await StoreModel.schema(schemaName).bulkCreate([
    {
      mongo_id: String(storeId[0]),
      number: 1,
      name_ar: storeModelRs[0].name.ar,
      name_en: storeModelRs[0].name.en,
      phone: storeModelRs[0].phone,
      is_default: storeModelRs[0].is_default,
      branch_id: 1,
    },
    {
      mongo_id: String(storeId[1]),
      name_ar: storeModelRs[1].name.ar,
      name_en: storeModelRs[1].name.en,
      phone: storeModelRs[1].phone,
      is_default: storeModelRs[1].is_default,
      number: 2,
      branch_id: 1,
    },
    {
      mongo_id: String(storeId[2]),
      number: 3,
      name_ar: storeModelRs[2].name.ar,
      name_en: storeModelRs[2].name.en,
      phone: storeModelRs[2].phone,
      is_default: storeModelRs[2].is_default,
      branch_id: 2,
    },
    {
      mongo_id: String(storeId[3]),
      name_ar: storeModelRs[3].name.ar,
      name_en: storeModelRs[3].name.en,
      is_default: storeModelRs[3].is_default,
      phone: storeModelRs[3].phone,
      number: 4,
      branch_id: 2,
    },
  ]);
  // item group creation
  const itemGroupData = [
    //items
    {
      _id: chairsGroupId,
      number: 1,
      name: { en: 'chairs', ar: 'كراسي' },
      type: itemTypeEnum.goods,
    },
    {
      _id: tablesGroupId,
      number: 2,
      name: { en: 'tables', ar: 'طاولات' },
      type: itemTypeEnum.goods,
    },
    {
      _id: drinksGroupId,
      number: 3,
      name: { en: 'drinks', ar: 'مشروبات' },
      type: itemTypeEnum.goods,
    },
    {
      _id: spicesGroupId,
      number: 4,
      name: { en: 'spices', ar: 'توابل' },
      type: itemTypeEnum.goods,
    },
    {
      _id: plasticGroupId,
      number: 5,
      name: { en: 'plastic', ar: 'بلاستيك' },
      type: itemTypeEnum.goods,
    },

    //service items
    {
      _id: washGroupId,
      number: 6,
      name: { en: 'wash', ar: 'غسيل' },
      type: itemTypeEnum.service,
    },
    {
      _id: repairGroupId,
      number: 7,
      name: { en: 'repair', ar: 'تصليح' },
      type: itemTypeEnum.service,
    },
  ];
  await itemGroupModel.create(itemGroupData);
  const sqlItemGroup = itemGroupData.map((gi, index: number) => {
    return {
      number: index + 1,
      name_en: gi.name.en,
      name_ar: gi.name.ar,
      type: gi.type,
      mongo_id: String(gi._id),
    };
  });
  await ItemGroupModel.schema(schemaName).bulkCreate(sqlItemGroup);
  // unit creationg
  const unitData = [
    //items
    {
      _id: unitIds.gram,
      number: 1,
      name: { ar: 'جرام', en: 'gram' },
      type: itemTypeEnum.goods,
    },
    {
      _id: unitIds.kilogram,
      number: 2,
      name: { ar: 'كيلوجرام', en: 'kilogram' },
      type: itemTypeEnum.goods,
    },
    {
      _id: unitIds.piece,
      number: 3,
      name: { ar: 'قطعة', en: 'piece' },
      type: itemTypeEnum.goods,
    },
    {
      _id: unitIds.ten_pic,
      number: 4,
      name: { ar: 'عشر قطع', en: 'ten pic' },
      type: itemTypeEnum.goods,
    },
    {
      _id: unitIds.dozen,
      number: 5,
      name: { ar: 'دستة', en: 'dozen' },
      type: itemTypeEnum.goods,
    },
    {
      _id: unitIds.box_twenty_pic,
      number: 6,
      name: { ar: 'كرتون 20 قطعة', en: 'box 20 pic' },
      type: itemTypeEnum.goods,
    },
    {
      _id: unitIds.box_hundred_pic,
      number: 7,
      name: { ar: 'كرتون 100 قطعة', en: 'box 100 pic' },
      type: itemTypeEnum.goods,
    },
    {
      _id: unitIds.roll,
      number: 8,
      name: { ar: 'رول', en: 'roll' },
      type: itemTypeEnum.goods,
    },
    {
      _id: unitIds.bag_three_hundred_gram,
      number: 9,
      name: { ar: 'كيس 300 جرام', en: 'bag 300 gram' },
      type: itemTypeEnum.goods,
    },

    //service items
    {
      _id: unitIds.basic,
      number: 10,
      name: { ar: 'خطة أساسية', en: 'basic plan' },
      type: itemTypeEnum.service,
    },
    {
      _id: unitIds.medium,
      number: 11,
      name: { ar: 'خطة متوسطة', en: 'medium plan' },
      type: itemTypeEnum.service,
    },
    {
      _id: unitIds.premium,
      number: 12,
      name: { ar: 'خطة متقدمة', en: 'premium plan' },
      type: itemTypeEnum.service,
    },
    {
      _id: unitIds.one_person,
      number: 13,
      name: { ar: 'شخص واحد', en: 'one person' },
      type: itemTypeEnum.service,
    },
    {
      _id: unitIds.more_than_one_person,
      number: 14,
      name: { ar: 'أكثر من شخص', en: 'more than one person' },
      type: itemTypeEnum.service,
    },
  ];
  await unitMongoModel.create(unitData);
  const sqlUnitGroup = unitData.map((ui, index: number) => {
    return {
      number: index + 1,
      name_en: ui.name.en,
      name_ar: ui.name.ar,
      type: ui.type,
      mongo_id: String(ui._id),
    };
  });
  await UnitModel.schema(schemaName).bulkCreate(sqlUnitGroup);
  // item creation
  const itemSeeder = new ItemSeeder(tenantDb);
  const items = await itemSeeder.seed(
    chairsGroupId,
    tablesGroupId,
    drinksGroupId,
    spicesGroupId,
    plasticGroupId,
    washGroupId,
    repairGroupId,
    unitIds,
    branches,
    schemaName,
  );

  // bundle creation
  const bundleSeeder = new BundleSeeder(tenantDb);
  await bundleSeeder.seed(unitIds, schemaName);

  const itemSnapshotSeeder = new ItemSnapshotSeeder(tenantDb);
  const itemData = items.filter((item) => item.type === itemTypeEnum.goods);

  await itemSnapshotSeeder.seed(itemData, branches, storesList, schemaName);

  await seedItemExplorerFunc(tenantDb, schemaName);
  await tenantDb.close();
  console.log(`seeding Tenant ${tenantId} done`);
}

async function seedInit() {
  // Get if multi tenant seeding is required
  if (process.argv.includes('--multi')) {
    for (let i = 0; i < 4; i++) {
      await tenantSeed(i);
    }
    process.exit(0);
  } else {
    let tenantId: number;
    // Get tenant id from command line
    process.argv.forEach((val, index) => {
      if (val === '--tenant') {
        tenantId = Number(process.argv[index + 1]);
      }
    });
    const i = tenantId || 0;
    await tenantSeed(i);
    process.exit(0);
  }
}

seedInit();
