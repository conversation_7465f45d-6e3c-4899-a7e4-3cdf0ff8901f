import { CreateCompanyTablesMigration } from '../../modules/postgres-database/migrations/01-create-company-table.migration';
import { CreateBranchTablesMigration } from '../../modules/postgres-database/migrations/02-create-branch-table.migration';
import { CreateUsersTablesMigration } from '../../modules/postgres-database/migrations/03-create-users-table.migration';
import { CreatePaymentsTablesMigration } from '../../modules/postgres-database/migrations/04-create-payments-table.migration';
import { CreatePermsTablesMigration } from '../../modules/postgres-database/migrations/05-create-perms-table.migration';
import { CreateRolesTablesMigration } from '../../modules/postgres-database/migrations/06-create-roles-table.migration';
import { CreateUserBranchRolesTablesMigration } from '../../modules/postgres-database/migrations/07-create-user-branch-roles-table.migration';
import { CreateUnitTableMigration } from '../../modules/postgres-database/migrations/08-create-unit-table.migration';
import { CreateItemGroupTableMigration } from '../../modules/postgres-database/migrations/09-create-item-group-table.migration';
import { CreateItemTableMigration } from '../../modules/postgres-database/migrations/10-create-item-table.migration';
import { CreateItemUnitPriceTableMigration } from '../../modules/postgres-database/migrations/11-create-item-unit-price-table.migration';
import { CreateStoreTableMigration } from '../../modules/postgres-database/migrations/12-create-store-table.migration';
import { CreateItemBranchTableMigration } from '../../modules/postgres-database/migrations/13-create-item-branch-table.migration';
import { CreateItemBranchUnitPriceTableMigration } from '../../modules/postgres-database/migrations/14-create-item-branch-unit-price-table.migration';
import { CreateCompanySnapshotTableMigration } from '../../modules/postgres-database/migrations/15-create-company-snapshot-table.migration';
import { CreateStoreSnapshotTableMigration } from '../../modules/postgres-database/migrations/16-create-store-snapshot-table.migration';
import { CreatePhysicalInventoryTableMigration } from '../../modules/postgres-database/migrations/17-create-physical-inventory-table.migration';
import { CreatePhysicalInventoryTransactionsTableMigration } from '../../modules/postgres-database/migrations/18-create-physical-inventory-transactions-table.migration';
import { CreateStoreAdjustmentTableMigration } from '../../modules/postgres-database/migrations/19-create-store-adjustment-table.migration';
import { CreateStoreAdjustmentTransactionsTableMigration } from '../../modules/postgres-database/migrations/20-create-store-adjustment-transactions-table.migration';
import { CreateInventoryJournalTableMigration } from '../../modules/postgres-database/migrations/21-create-inventory-journal-table.migration';
import { CreateItemExplorerTableMigration } from '../../modules/postgres-database/migrations/22-create-item-explorer-table.migration';
import { CreateItemExplorerUnitPricesTableMigration } from '../../modules/postgres-database/migrations/23-create-item-explorer-unit-prices-table.migration';
import { CreateItemExplorerUnitCostsTableMigration } from '../../modules/postgres-database/migrations/24-create-item-explorer-unit-costs-table.migration';
import { CreateBranchSnapshotTableMigration } from '../../modules/postgres-database/migrations/25-create-branch-snapshot-table.migration';
import { CreateBranchSnapshotUnitCostTableMigration } from '../../modules/postgres-database/migrations/26-create-branch-snapshot-unit-cost-table.migration';
import { CreateCompanySnapshotUnitCostTableMigration } from '../../modules/postgres-database/migrations/27-create-company-snapshot-unit-cost-table.migration';
import { CreateStoreSnapshotUnitCostTableMigration } from '../../modules/postgres-database/migrations/28-create-store-snapshot-unit-cost-table.migration';
import { CreatePhysicalInventoryPostingTableMigration } from '../../modules/postgres-database/migrations/29-create-physical-inventory-posting-table.migration';
import { CreatePhysicalInventoryPostingVouchersTableMigration } from '../../modules/postgres-database/migrations/30-create-physical-inventory-posting-vouchers-table.migration';
import { CreateAccountingNodeTableMigration } from '../../modules/postgres-database/migrations/31-create-accounting-nodes-table.migration';
import { CreateFiscalYearTableMigration } from '../../modules/postgres-database/migrations/32-create-fiscal-year-table.model';
import { CreateGeneralLedgerTableMigration } from '../../modules/postgres-database/migrations/33-create-general-ledger-table.model';
import { CreateGeneralLedgerTransactionsTableMigration } from '../../modules/postgres-database/migrations/34-create-general-ledger-transaction-table.migration';
import { CreatePartnerLedgerTransactionsTableMigration } from '../../modules/postgres-database/migrations/35-create-partner-ledger-transaction-table.migration';
import { CreateTempBeginningBalanceTableMigration } from '../../modules/postgres-database/migrations/36-create-beginning-balance-table.migration';
import { CreateGeneralJournalTableMigration } from '../../modules/postgres-database/migrations/37-create-general-journal-table.migration';
import { CreateGeneralJournalTransactionsTableMigration } from '../../modules/postgres-database/migrations/38-create-general-journal-transaction-table.model';
import { CreateVoucherTableMigration } from '../../modules/postgres-database/migrations/39-create-voucher-table.migration';
import { CreateVoucherTransactionsTableMigration } from '../../modules/postgres-database/migrations/40-create-voucher-transaction-table.migration';
import { CreateBundleTableMigration } from '../../modules/postgres-database/migrations/51-create-bundle-table.migration';
import { CreateBundleItemTableMigration } from '../../modules/postgres-database/migrations/52-create-bundle-item-table.migration';

export const migrationList = [
  new CreateCompanyTablesMigration(),
  new CreateBranchTablesMigration(),
  new CreateAccountingNodeTableMigration(),
  new CreatePaymentsTablesMigration(),
  new CreateUsersTablesMigration(),
  new CreatePermsTablesMigration(),
  new CreateRolesTablesMigration(),
  new CreateUserBranchRolesTablesMigration(),
  new CreateUnitTableMigration(),
  new CreateItemGroupTableMigration(),
  new CreateItemTableMigration(),
  new CreateItemUnitPriceTableMigration(),
  new CreateStoreTableMigration(),
  new CreateItemBranchTableMigration(),
  new CreateItemBranchUnitPriceTableMigration(),
  new CreateInventoryJournalTableMigration(),
  new CreateItemExplorerTableMigration(),
  new CreateItemExplorerUnitPricesTableMigration(),
  new CreateItemExplorerUnitCostsTableMigration(),
  new CreateCompanySnapshotTableMigration(),
  new CreateStoreSnapshotTableMigration(),
  new CreateBranchSnapshotTableMigration(),
  new CreateCompanySnapshotUnitCostTableMigration(),
  new CreateBranchSnapshotUnitCostTableMigration(),
  new CreateStoreSnapshotUnitCostTableMigration(),
  new CreatePhysicalInventoryTableMigration(),
  new CreatePhysicalInventoryTransactionsTableMigration(),
  new CreatePhysicalInventoryPostingTableMigration(),
  new CreatePhysicalInventoryPostingVouchersTableMigration(),
  new CreateStoreAdjustmentTableMigration(),
  new CreateStoreAdjustmentTransactionsTableMigration(),
  new CreateFiscalYearTableMigration(),
  new CreateGeneralLedgerTableMigration(),
  new CreateGeneralLedgerTransactionsTableMigration(),
  new CreatePartnerLedgerTransactionsTableMigration(),
  new CreateTempBeginningBalanceTableMigration(),
  new CreateGeneralJournalTableMigration(),
  new CreateGeneralJournalTransactionsTableMigration(),
  new CreateVoucherTableMigration(),
  new CreateVoucherTransactionsTableMigration(),
  new CreateBundleTableMigration(),
  new CreateBundleItemTableMigration(),
];
