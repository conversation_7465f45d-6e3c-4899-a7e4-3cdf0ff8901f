import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { throwError } from 'rxjs';
import { generalExceptionCode } from '../exceptions/exception-code.general';
import { nameOf } from '../utils/object-key-name';
import { Request, Response } from 'express';
import {
  IHttpExceptions,
  IRpcExceptions,
  Services,
} from '../utils/interface/exceptions.interface';
import { getObjectKey } from '../utils/get-object-key';
import { ValidationError } from 'class-validator';
@Catch()
export class FlexibleExceptionFilter implements ExceptionFilter {
  constructor(
    private readonly configService: ConfigService, // if we want to put service name on
  ) {}
  httpException(exception: IHttpExceptions, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    if (exception.responseData) {
      return response.status(exception.statusCode).json(exception);
    }
    const statusCode = exception['getStatus'] ? exception.getStatus() : 500;
    const exceptionResponse = exception.getResponse
      ? exception.getResponse()
      : exception.message;
    let responseData, exceptionCode;
    if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      responseData = exceptionResponse['response'] || exception.message;
      exceptionCode = exceptionResponse['exceptionCode'];
    } else if (typeof exceptionResponse === 'string') {
      responseData = exceptionResponse;
      exceptionCode =
        exception instanceof HttpException
          ? exceptionResponse
          : nameOf(generalExceptionCode, (x) => x.internalServerError);
    }

    const responseObject: IHttpExceptions = {
      responseData,
      statusCode,
      additionalData: exception['additionalData'],
      timestamp: new Date().toISOString(),
      path: request.url,
      serviceTrace: [],
      exceptionCode:
        exceptionCode ||
        nameOf(
          generalExceptionCode,
          (x) =>
            x[
              getObjectKey(generalExceptionCode, statusCode) ||
                'internalServerError'
            ],
        ),
    };
    response.status(statusCode).json(responseObject);
  }
  rpcException(exception: IRpcExceptions) {
    // let exceptionError;
    // its means it already parsed in another service
    if (exception?.serviceTrace?.length > 0) {
      exception.serviceTrace.push(
        this.configService.get<Services>('SERVICE_NAME'),
      );
      return exception;
    }
    const exceptionError = (
      exception.getError ? exception.getError() : exception.message
    ) as any;
    let responseData = exceptionError.exceptionCode || exceptionError;

    let exceptionCode = exceptionError.exceptionCode || exception['response'];

    if (
      exception?.response?.response &&
      exception?.response?.response[0] instanceof ValidationError
    ) {
      responseData = exception.response.response;
      exceptionCode = exception.response.exceptionCode;
    }

    const statusCode = exceptionError.statusCode || exception['status'];

    const responseObject: IRpcExceptions = {
      responseData,
      serviceTrace: [this.configService.get<Services>('SERVICE_NAME')],
      additionalData: exception['additionalData'],
      timestamp: new Date().toISOString(),
      statusCode: statusCode || generalExceptionCode.internalServerError,
      exceptionCode:
        exceptionCode ||
        nameOf(generalExceptionCode, (x) => x.internalServerError),
    };
    return responseObject;
  }
  catch(exception: any, host: ArgumentsHost) {
    console.log(exception);

    if (host.getType() === 'rpc') {
      return throwError(() => this.rpcException(exception));
    }
    if (host.getType() == 'http') {
      return this.httpException(exception, host);
    }
  }
}
