import {
  HttpException,
  HttpStatus,
  Injectable,
  NestMiddleware,
} from '@nestjs/common';
import { Request } from 'express';
@Injectable()
export class GlobalConfigMiddleware implements NestMiddleware {
  async use(req: Request, res: any, next: () => void) {
    const config = {
      allowEdit: true,
      allowDelete: true,
    };
    if (config.allowEdit == false && req.method == 'PATCH') {
      throw new HttpException('you cant patch', HttpStatus.FORBIDDEN);
    }
    if (config.allowDelete == false && req.method == 'DELETE') {
      throw new HttpException('you cant delete', HttpStatus.FORBIDDEN);
    }
    next();
  }
}
