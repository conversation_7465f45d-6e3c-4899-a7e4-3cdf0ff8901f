import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class ModulePrefixMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const originalUrl = req.originalUrl;
    console.log(
      'originalUrloriginalUrloriginalUrl, originalUrl ',
      originalUrl,
      'originalUrloriginalUrloriginalUrloriginalUrloriginalUrl',
    );
    // Check if the URL starts with any of the module paths
    if (originalUrl.startsWith('/users/')) {
      // Keep the original path but make it work within the ERP service
      req.url = req.url.replace('/users/', '/');
    } else if (originalUrl.startsWith('/inventory/')) {
      req.url = req.url.replace('/inventory/', '/');
    } else if (originalUrl.startsWith('/accounting/')) {
      req.url = req.url.replace('/accounting/', '/');
    } else if (originalUrl.startsWith('/trade/')) {
      req.url = req.url.replace('/trade/', '/');
    }

    next();
  }
}
